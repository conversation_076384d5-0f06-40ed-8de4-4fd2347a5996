import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/travel/travel_services_provider.dart';
import 'package:culture_connect/screens/travel/cruise/cruise_details_screen.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/travel/travel_service_card.dart';

/// Screen for displaying a list of cruises
class CruiseListScreen extends ConsumerStatefulWidget {
  /// Creates a new cruise list screen
  const CruiseListScreen({super.key});

  @override
  ConsumerState<CruiseListScreen> createState() => _CruiseListScreenState();
}

class _CruiseListScreenState extends ConsumerState<CruiseListScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: _isSearching
            ? TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search cruises...',
                  border: InputBorder.none,
                  hintStyle: TextStyle(
                      color: theme.colorScheme.onSurface.withAlpha(153)),
                ),
                style: TextStyle(color: theme.colorScheme.onSurface),
                autofocus: true,
                onChanged: (value) {
                  // Implement search functionality
                },
              )
            : const Text('Cruises'),
        actions: [
          IconButton(
            icon: Icon(_isSearching ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                }
              });
            },
          ),
        ],
      ),
      body: _buildCruiseList(),
    );
  }

  Widget _buildCruiseList() {
    final cruisesAsyncValue = ref.watch(cruisesProvider);

    return cruisesAsyncValue.when(
      data: (cruises) {
        if (cruises.isEmpty) {
          return const Center(
            child: Text('No cruises available'),
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            // Refresh data
            ref.invalidate(cruisesProvider);
          },
          child: ListView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.all(16),
            itemCount: cruises.length,
            itemBuilder: (context, index) {
              final cruise = cruises[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: TravelServiceCard(
                  travelService: cruise,
                  onTap: () => _navigateToCruiseDetails(cruise),
                ),
              );
            },
          ),
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => Center(
        child: ErrorView(
          error: error.toString(),
          onRetry: () => ref.invalidate(cruisesProvider),
        ),
      ),
    );
  }

  void _navigateToCruiseDetails(Cruise cruise) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CruiseDetailsScreen(cruise: cruise),
      ),
    );
  }
}
