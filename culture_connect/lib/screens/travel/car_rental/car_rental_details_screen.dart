import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/travel/car_rental.dart';
import 'package:culture_connect/widgets/common/rating_display.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/travel/car_rental_map_view.dart';
import 'package:culture_connect/screens/payment/payment_screen.dart';
import 'package:culture_connect/widgets/common/image_gallery.dart';
import 'package:culture_connect/widgets/common/animated_section.dart';
import 'package:culture_connect/widgets/travel/car_comparison_dialog.dart';
import 'package:culture_connect/widgets/travel/share_travel_service_dialog.dart';

/// Screen for displaying car rental details
class CarRentalDetailsScreen extends ConsumerStatefulWidget {
  /// The car rental to display
  final CarRental carRental;

  /// Creates a new car rental details screen
  const CarRentalDetailsScreen({
    super.key,
    required this.carRental,
  });

  @override
  ConsumerState<CarRentalDetailsScreen> createState() =>
      _CarRentalDetailsScreenState();
}

class _CarRentalDetailsScreenState extends ConsumerState<CarRentalDetailsScreen>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  DateTime _pickupDate = DateTime.now().add(const Duration(days: 1));
  DateTime _dropoffDate = DateTime.now().add(const Duration(days: 3));
  int _durationDays = 2;
  double _totalPrice = 0;
  bool _isLoading = false;
  String? _errorMessage;

  // Date formatter
  final DateFormat _dateFormatter = DateFormat('EEE, MMM d, yyyy');

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _animationController.forward();

    // Calculate total price
    _calculateTotalPrice();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _calculateTotalPrice() {
    try {
      _durationDays = _dropoffDate.difference(_pickupDate).inDays;
      if (_durationDays < 1) _durationDays = 1;
      _totalPrice = widget.carRental.price * _durationDays;
      _errorMessage = null;
    } catch (e) {
      _errorMessage = 'Error calculating price: $e';
      _totalPrice = 0;
    }
  }

  Future<void> _selectPickupDate() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: _pickupDate,
        firstDate: DateTime.now(),
        lastDate: DateTime.now().add(const Duration(days: 365)),
        builder: (context, child) {
          return Theme(
            data: Theme.of(context).copyWith(
              colorScheme: Theme.of(context).colorScheme.copyWith(
                    primary: Theme.of(context).colorScheme.primary,
                  ),
            ),
            child: child!,
          );
        },
      );

      if (picked != null && picked != _pickupDate) {
        setState(() {
          _pickupDate = picked;
          if (_pickupDate.isAfter(_dropoffDate)) {
            _dropoffDate = _pickupDate.add(const Duration(days: 1));
          }
          _calculateTotalPrice();
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error selecting date: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _selectDropoffDate() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: _dropoffDate,
        firstDate: _pickupDate.add(const Duration(days: 1)),
        lastDate: _pickupDate.add(const Duration(days: 30)),
        builder: (context, child) {
          return Theme(
            data: Theme.of(context).copyWith(
              colorScheme: Theme.of(context).colorScheme.copyWith(
                    primary: Theme.of(context).colorScheme.primary,
                  ),
            ),
            child: child!,
          );
        },
      );

      if (picked != null && picked != _dropoffDate) {
        setState(() {
          _dropoffDate = picked;
          _calculateTotalPrice();
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error selecting date: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Shows the car comparison dialog
  void _showCarComparisonDialog() {
    showDialog(
      context: context,
      builder: (context) => CarComparisonDialog(
        carRental: widget.carRental,
        onCarSelected: (car) {
          // Navigate to the selected car's details screen
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => CarRentalDetailsScreen(carRental: car),
            ),
          );
        },
      ),
    );
  }

  /// Shows the share dialog
  void _showShareDialog() {
    showDialog(
      context: context,
      builder: (context) => ShareTravelServiceDialog(
        travelService: widget.carRental,
      ),
    );
  }

  /// Adds the car to favorites
  void _addToFavorites() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Added to favorites'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // App bar with image
          SliverAppBar(
            expandedHeight: 250,
            pinned: true,
            actions: [
              IconButton(
                icon: const Icon(Icons.share),
                onPressed: _showShareDialog,
                tooltip: 'Share this car',
              ),
              IconButton(
                icon: const Icon(Icons.compare_arrows),
                onPressed: _showCarComparisonDialog,
                tooltip: 'Compare with other cars',
              ),
              IconButton(
                icon: const Icon(Icons.favorite_border),
                onPressed: _addToFavorites,
                tooltip: 'Add to favorites',
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: Hero(
                tag: 'car_rental_image_${widget.carRental.id}',
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: FadeInImage.assetNetwork(
                    placeholder: 'assets/images/placeholder_image.png',
                    image: widget.carRental.imageUrl,
                    fit: BoxFit.cover,
                    fadeInDuration: const Duration(milliseconds: 300),
                    imageErrorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: theme.colorScheme.surfaceContainerHighest,
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.image_not_supported,
                                color: theme.colorScheme.onSurfaceVariant,
                                size: 48,
                                semanticLabel: 'Image failed to load',
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Image not available',
                                style: TextStyle(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ),

          // Content
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and rating
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.carRental.name,
                              style: theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              widget.carRental.fullName,
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          RatingDisplay(
                            rating: widget.carRental.rating,
                            size: 20,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${widget.carRental.reviewCount} reviews',
                            style: theme.textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Price
                  Row(
                    children: [
                      if (widget.carRental.isOnSale &&
                          widget.carRental.originalPrice != null) ...[
                        Text(
                          widget.carRental.formattedOriginalPrice!,
                          style: theme.textTheme.titleMedium?.copyWith(
                            decoration: TextDecoration.lineThrough,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                      Text(
                        '${widget.carRental.formattedPrice} / day',
                        style: theme.textTheme.titleLarge?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Photo Gallery
                  AnimatedSection(
                    title: 'Photo Gallery',
                    titleStyle: theme.textTheme.titleLarge,
                    content: ImageGallery(
                      images: [
                        widget.carRental.imageUrl,
                        ...widget.carRental.additionalImages
                      ],
                      height: 200,
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Car details
                  AnimatedSection(
                    title: 'Car Details',
                    content: _buildCarDetailsGrid(),
                    titleStyle: theme.textTheme.titleLarge,
                  ),

                  const SizedBox(height: 16),

                  // Description
                  AnimatedSection(
                    title: 'Description',
                    content: Text(
                      widget.carRental.description,
                      style: theme.textTheme.bodyMedium,
                    ),
                    titleStyle: theme.textTheme.titleLarge,
                  ),

                  const SizedBox(height: 24),

                  // Rental details
                  AnimatedSection(
                    title: 'Rental Details',
                    titleStyle: theme.textTheme.titleLarge,
                    content: Card(
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          children: [
                            // Pickup date
                            ListTile(
                              title: const Text('Pickup Date'),
                              subtitle: Text(
                                _dateFormatter.format(_pickupDate),
                                style: theme.textTheme.titleMedium,
                              ),
                              trailing: const Icon(Icons.calendar_today),
                              onTap: _selectPickupDate,
                            ),
                            const Divider(),

                            // Dropoff date
                            ListTile(
                              title: const Text('Dropoff Date'),
                              subtitle: Text(
                                _dateFormatter.format(_dropoffDate),
                                style: theme.textTheme.titleMedium,
                              ),
                              trailing: const Icon(Icons.calendar_today),
                              onTap: _selectDropoffDate,
                            ),
                            const Divider(),

                            // Duration
                            ListTile(
                              title: const Text('Duration'),
                              subtitle: Text(
                                '$_durationDays days',
                                style: theme.textTheme.titleMedium,
                              ),
                            ),
                            const Divider(),

                            // Total price
                            ListTile(
                              title: const Text('Total Price'),
                              subtitle: Text(
                                '${widget.carRental.currency}${_totalPrice.toStringAsFixed(2)}',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: theme.colorScheme.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Map view
                  AnimatedSection(
                    title: 'Pickup & Dropoff Locations',
                    titleStyle: theme.textTheme.titleLarge,
                    content: CarRentalMapView(
                      carRental: widget.carRental,
                      showRoute: true,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Rental company
                  AnimatedSection(
                    title: 'Rental Company',
                    titleStyle: theme.textTheme.titleLarge,
                    content: Card(
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundImage: NetworkImage(
                              widget.carRental.rentalCompanyLogoUrl),
                        ),
                        title: Text(widget.carRental.rentalCompany),
                        subtitle: Text(widget.carRental.location),
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Cancellation policy
                  AnimatedSection(
                    title: 'Cancellation Policy',
                    titleStyle: theme.textTheme.titleLarge,
                    content: Card(
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Text(
                          widget.carRental.cancellationPolicy,
                          style: theme.textTheme.bodyMedium,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Insurance recommendation
                  _buildInsuranceRecommendation(context),

                  const SizedBox(height: 100), // Space for the bottom button
                ],
              ),
            ),
          ),
        ],
      ),
      bottomSheet: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26), // Equivalent to opacity 0.1
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Error message
              if (_errorMessage != null)
                Container(
                  margin: EdgeInsets.only(bottom: 16),
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.errorContainer,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: theme.colorScheme.error,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(
                            color: theme.colorScheme.error,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              // Book now button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading
                      ? null
                      : () {
                          // Navigate to payment screen
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => PaymentScreen(
                                amount: _totalPrice,
                                currency: widget.carRental.currency,
                                description:
                                    'Car Rental: ${widget.carRental.name} ($_durationDays days)',
                              ),
                            ),
                          );
                        },
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 16),
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                  ),
                  child: _isLoading
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(
                              width: 20,
                              height: 20,
                              child: LoadingIndicator(color: Colors.white),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Processing...',
                              style: TextStyle(
                                color: theme.colorScheme.onPrimary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        )
                      : Text(
                          'Book Now - ${widget.carRental.currency}${_totalPrice.toStringAsFixed(2)}',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: theme.colorScheme.onPrimary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build insurance recommendation card
  Widget _buildInsuranceRecommendation(BuildContext context) {
    final theme = Theme.of(context);

    return AnimatedSection(
      title: 'Protect Your Rental',
      titleStyle: theme.textTheme.titleLarge,
      content: Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.shield,
                    color: theme.colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Car Rental Insurance',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'Protect your car rental with comprehensive insurance coverage. Get protection against damage, theft, and liability.',
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        // Navigate to insurance screen
                        Navigator.pushNamed(context, '/travel/insurance');
                      },
                      child: const Text('Learn More'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        // Navigate to insurance search with car rental details
                        Navigator.pushNamed(
                          context,
                          '/travel/insurance/search',
                          arguments: {
                            'destination': widget.carRental.location,
                            'startDate': _pickupDate,
                            'endDate': _dropoffDate,
                            'travelerCount': 1,
                            'vehicleRental': true,
                            'rentalValue': _totalPrice,
                          },
                        );
                      },
                      child: const Text('Get Quote'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCarDetailsGrid() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 3,
      children: [
        _buildCarDetailItem(
            Icons.car_rental, 'Type', widget.carRental.carType.displayName),
        _buildCarDetailItem(Icons.settings, 'Transmission',
            widget.carRental.transmission.displayName),
        _buildCarDetailItem(Icons.local_gas_station, 'Fuel',
            widget.carRental.fuelType.displayName),
        _buildCarDetailItem(Icons.airline_seat_recline_normal, 'Seats',
            widget.carRental.seats.toString()),
        _buildCarDetailItem(
            Icons.door_front_door, 'Doors', widget.carRental.doors.toString()),
        _buildCarDetailItem(Icons.luggage, 'Luggage',
            '${widget.carRental.luggageCapacity} bags'),
        _buildCarDetailItem(
            Icons.speed, 'Mileage', widget.carRental.formattedMileageLimit),
        _buildCarDetailItem(
          Icons.ac_unit,
          'A/C',
          widget.carRental.hasAirConditioning ? 'Yes' : 'No',
        ),
      ],
    );
  }

  Widget _buildCarDetailItem(IconData icon, String title, String value) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(8),
        child: Row(
          children: [
            Icon(
              icon,
              size: 24,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                  ),
                  Text(
                    value,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
