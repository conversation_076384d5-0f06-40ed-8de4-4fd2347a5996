import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import 'package:culture_connect/models/travel/price_alert.dart';
import 'package:culture_connect/models/travel/travel_service_base.dart';
import 'package:culture_connect/providers/services_providers.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/widgets/common/custom_button.dart';

/// A screen for creating a new price alert
class CreatePriceAlertScreen extends ConsumerStatefulWidget {
  /// The travel service ID
  final String? travelServiceId;

  /// The travel service type
  final TravelServiceType? travelServiceType;

  /// The travel service name
  final String? travelServiceName;

  /// The current price
  final double? currentPrice;

  /// The currency
  final String? currency;

  /// Creates a new create price alert screen
  const CreatePriceAlertScreen({
    super.key,
    this.travelServiceId,
    this.travelServiceType,
    this.travelServiceName,
    this.currentPrice,
    this.currency,
  });

  @override
  ConsumerState<CreatePriceAlertScreen> createState() =>
      _CreatePriceAlertScreenState();
}

class _CreatePriceAlertScreenState
    extends ConsumerState<CreatePriceAlertScreen> {
  final _formKey = GlobalKey<FormState>();

  late String _travelServiceId;
  late TravelServiceType _travelServiceType;
  late String _travelServiceName;
  late double _currentPrice;
  late String _currency;

  double _targetPrice = 0;
  AlertFrequency _frequency = AlertFrequency.immediate;
  DateTime _endDate = DateTime.now().add(const Duration(days: 30));

  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    // Initialize with provided values or defaults
    _travelServiceId = widget.travelServiceId ?? '';
    _travelServiceType = widget.travelServiceType ?? TravelServiceType.flight;
    _travelServiceName = widget.travelServiceName ?? '';
    _currentPrice = widget.currentPrice ?? 0;
    _currency = widget.currency ?? 'USD';

    // Set default target price (10% below current price)
    _targetPrice = _currentPrice * 0.9;
  }

  /// Create the price alert
  Future<void> _createPriceAlert() async {
    if (!_formKey.currentState!.validate()) return;

    final userAsync = ref.read(currentUserModelProvider);
    final user = userAsync.value;
    final userId = user?.id;
    if (userId == null) {
      if (!mounted) return;
      setState(() {
        _errorMessage = 'You must be signed in to create a price alert';
      });
      return;
    }

    if (!mounted) return;
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final priceAlertService = ref.read(priceAlertServiceProvider(userId));

      await priceAlertService.createPriceAlert(
        travelServiceId: _travelServiceId,
        travelServiceType: _travelServiceType,
        travelServiceName: _travelServiceName,
        currentPrice: _currentPrice,
        targetPrice: _targetPrice,
        currency: _currency,
        frequency: _frequency,
        startDate: DateTime.now(),
        endDate: _endDate,
      );

      if (mounted) {
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to create price alert: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('MMM d, yyyy');

    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Price Alert'),
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: EdgeInsets.all(16),
          children: [
            if (_errorMessage != null)
              Container(
                margin: EdgeInsets.only(bottom: 16),
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withAlpha(25),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red),
                ),
                child: Text(
                  _errorMessage!,
                  style: const TextStyle(color: Colors.red),
                ),
              ),

            // Travel Service Information
            Card(
              margin: EdgeInsets.only(bottom: 16),
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Travel Service',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      initialValue: _travelServiceName,
                      decoration: const InputDecoration(
                        labelText: 'Service Name',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a service name';
                        }
                        return null;
                      },
                      onChanged: (value) {
                        if (!mounted) return;
                        setState(() {
                          _travelServiceName = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<TravelServiceType>(
                      value: _travelServiceType,
                      decoration: const InputDecoration(
                        labelText: 'Service Type',
                        border: OutlineInputBorder(),
                      ),
                      items: TravelServiceType.values.map((type) {
                        return DropdownMenuItem<TravelServiceType>(
                          value: type,
                          child: Text(type.displayName),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null && mounted) {
                          setState(() {
                            _travelServiceType = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: TextFormField(
                            initialValue: _currentPrice.toString(),
                            decoration: InputDecoration(
                              labelText: 'Current Price',
                              border: const OutlineInputBorder(),
                              prefixText: _currency,
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                                decimal: true),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                  RegExp(r'^\d+\.?\d{0,2}')),
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter a price';
                              }
                              final price = double.tryParse(value);
                              if (price == null || price <= 0) {
                                return 'Please enter a valid price';
                              }
                              return null;
                            },
                            onChanged: (value) {
                              final price = double.tryParse(value);
                              if (price != null && mounted) {
                                setState(() {
                                  _currentPrice = price;
                                  // Update target price to be 10% below current price
                                  _targetPrice = _currentPrice * 0.9;
                                });
                              }
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            initialValue: _currency,
                            decoration: const InputDecoration(
                              labelText: 'Currency',
                              border: OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Required';
                              }
                              return null;
                            },
                            onChanged: (value) {
                              if (!mounted) return;
                              setState(() {
                                _currency = value;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Alert Settings
            Card(
              margin: EdgeInsets.only(bottom: 16),
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Alert Settings',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      initialValue: _targetPrice.toString(),
                      decoration: InputDecoration(
                        labelText: 'Target Price',
                        border: const OutlineInputBorder(),
                        prefixText: _currency,
                        helperText:
                            'You\'ll be notified when the price drops below this amount',
                      ),
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            RegExp(r'^\d+\.?\d{0,2}')),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a target price';
                        }
                        final price = double.tryParse(value);
                        if (price == null || price <= 0) {
                          return 'Please enter a valid price';
                        }
                        if (price >= _currentPrice) {
                          return 'Target price must be lower than current price';
                        }
                        return null;
                      },
                      onChanged: (value) {
                        final price = double.tryParse(value);
                        if (price != null && mounted) {
                          setState(() {
                            _targetPrice = price;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Alert Frequency',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...AlertFrequency.values.map((frequency) {
                      return RadioListTile<AlertFrequency>(
                        title: Text(frequency.displayName),
                        subtitle: Text(frequency.description),
                        value: frequency,
                        groupValue: _frequency,
                        onChanged: (value) {
                          if (value != null && mounted) {
                            setState(() {
                              _frequency = value;
                            });
                          }
                        },
                        activeColor: Theme.of(context).colorScheme.primary,
                        contentPadding: const EdgeInsets.all(0),
                      );
                    }),
                    const SizedBox(height: 16),
                    ListTile(
                      contentPadding: const EdgeInsets.all(0),
                      title: const Text('Alert Expiration Date'),
                      subtitle: Text(dateFormat.format(_endDate)),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () async {
                        final now = DateTime.now();
                        final picked = await showDatePicker(
                          context: context,
                          initialDate: _endDate,
                          firstDate: now,
                          lastDate: now.add(const Duration(days: 365)),
                        );

                        if (picked != null && mounted) {
                          setState(() {
                            _endDate = picked;
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            CustomButton(
              onPressed: _isLoading ? null : _createPriceAlert,
              text: 'Create Price Alert',
              isLoading: _isLoading,
            ),
          ],
        ),
      ),
    );
  }
}
