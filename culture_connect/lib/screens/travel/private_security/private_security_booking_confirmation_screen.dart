import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';
import 'package:culture_connect/models/travel/travel.dart';

import 'package:culture_connect/screens/travel/private_security/private_security_management_screen.dart';

/// Screen for displaying booking confirmation for private security service
class PrivateSecurityBookingConfirmationScreen extends ConsumerStatefulWidget {
  /// The private security service that was booked
  final PrivateSecurity privateSecurity;

  /// Start date of the service
  final DateTime startDate;

  /// End date of the service
  final DateTime endDate;

  /// Number of security personnel
  final int personnelCount;

  /// Whether a vehicle is included
  final bool hasVehicle;

  /// Type of vehicle (if applicable)
  final String? vehicleType;

  /// Special instructions for the service
  final String specialInstructions;

  /// Total price of the booking
  final double totalPrice;

  /// Booking ID
  final String bookingId;

  /// Creates a new private security booking confirmation screen
  const PrivateSecurityBookingConfirmationScreen({
    super.key,
    required this.privateSecurity,
    required this.startDate,
    required this.endDate,
    required this.personnelCount,
    required this.hasVehicle,
    required this.vehicleType,
    required this.specialInstructions,
    required this.totalPrice,
    required this.bookingId,
  });

  @override
  ConsumerState<PrivateSecurityBookingConfirmationScreen> createState() =>
      _PrivateSecurityBookingConfirmationScreenState();
}

class _PrivateSecurityBookingConfirmationScreenState
    extends ConsumerState<PrivateSecurityBookingConfirmationScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final days = widget.endDate.difference(widget.startDate).inDays;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Booking Confirmed'),
        automaticallyImplyLeading: false,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Success animation
            SizedBox(
              height: 200,
              child: Center(
                child: Lottie.network(
                  'https://assets10.lottiefiles.com/packages/lf20_s2lryxtd.json',
                  controller: _animationController,
                  repeat: false,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Confirmation message
            Text(
              'Your security service has been booked!',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            Text(
              'We\'ve sent a <NAME_EMAIL>',
              style: theme.textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            // Booking details card
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.security,
                          color: theme.colorScheme.primary,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            widget.privateSecurity.name,
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const Divider(height: 32),

                    // Booking ID
                    _buildDetailRow(
                      context,
                      'Booking ID',
                      widget.bookingId,
                    ),

                    const SizedBox(height: 16),

                    // Service period
                    _buildDetailRow(
                      context,
                      'Start Date',
                      DateFormat('EEEE, MMMM d, yyyy').format(widget.startDate),
                    ),

                    const SizedBox(height: 16),

                    _buildDetailRow(
                      context,
                      'End Date',
                      DateFormat('EEEE, MMMM d, yyyy').format(widget.endDate),
                    ),

                    const SizedBox(height: 16),

                    _buildDetailRow(
                      context,
                      'Duration',
                      '$days ${days == 1 ? 'day' : 'days'}',
                    ),

                    const SizedBox(height: 16),

                    // Service details
                    _buildDetailRow(
                      context,
                      'Personnel',
                      widget.personnelCount.toString(),
                    ),

                    const SizedBox(height: 16),

                    _buildDetailRow(
                      context,
                      'Vehicle',
                      widget.hasVehicle
                          ? 'Yes${widget.vehicleType != null ? ' (${widget.vehicleType})' : ''}'
                          : 'No',
                    ),

                    if (widget.specialInstructions.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      _buildDetailRow(
                        context,
                        'Special Instructions',
                        widget.specialInstructions,
                      ),
                    ],

                    const Divider(height: 32),

                    // Total price
                    _buildDetailRow(
                      context,
                      'Total Price',
                      '${widget.privateSecurity.currency}${widget.totalPrice.toStringAsFixed(2)}',
                      isHighlighted: true,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 32),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      // In a real app, this would share the booking details
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content:
                              Text('Sharing functionality not implemented yet'),
                        ),
                      );
                    },
                    icon: const Icon(Icons.share),
                    label: const Text('Share'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (context) =>
                              const PrivateSecurityManagementScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.security),
                    label: const Text('My Bookings'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Home button
            SizedBox(
              width: double.infinity,
              child: TextButton.icon(
                onPressed: () {
                  // Navigate to home screen
                  Navigator.popUntil(context, (route) => route.isFirst);
                },
                icon: const Icon(Icons.home),
                label: const Text('Back to Home'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(BuildContext context, String label, String value,
      {bool isHighlighted = false}) {
    final theme = Theme.of(context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            '$label:',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: isHighlighted
                ? theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  )
                : theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
          ),
        ),
      ],
    );
  }
}
