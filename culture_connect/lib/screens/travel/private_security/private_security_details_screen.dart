import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/widgets/common/rating_display.dart';
import 'package:culture_connect/screens/travel/private_security/private_security_booking_screen.dart';

/// Screen for displaying private security service details
class PrivateSecurityDetailsScreen extends ConsumerStatefulWidget {
  /// The private security service to display
  final PrivateSecurity privateSecurity;

  /// Creates a new private security details screen
  const PrivateSecurityDetailsScreen({
    super.key,
    required this.privateSecurity,
  });

  @override
  ConsumerState<PrivateSecurityDetailsScreen> createState() =>
      _PrivateSecurityDetailsScreenState();
}

class _PrivateSecurityDetailsScreenState
    extends ConsumerState<PrivateSecurityDetailsScreen> {
  final ScrollController _scrollController = ScrollController();
  DateTime _startDate = DateTime.now().add(const Duration(days: 1));
  DateTime _endDate = DateTime.now().add(const Duration(days: 3));
  int _durationDays = 2;
  double _totalPrice = 0;

  @override
  void initState() {
    super.initState();
    _calculateTotalPrice();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _calculateTotalPrice() {
    _durationDays = _endDate.difference(_startDate).inDays;
    if (_durationDays < 1) _durationDays = 1;
    _totalPrice = widget.privateSecurity.price * _durationDays;
  }

  Future<void> _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null && picked != _startDate) {
      setState(() {
        _startDate = picked;
        if (_startDate.isAfter(_endDate)) {
          _endDate = _startDate.add(const Duration(days: 1));
        }
        _calculateTotalPrice();
      });
    }
  }

  Future<void> _selectEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate,
      firstDate: _startDate.add(const Duration(days: 1)),
      lastDate: _startDate.add(const Duration(days: 30)),
    );

    if (picked != null && picked != _endDate) {
      setState(() {
        _endDate = picked;
        _calculateTotalPrice();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // App bar with image
          SliverAppBar(
            expandedHeight: 250,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              background: Hero(
                tag: 'private_security_image_${widget.privateSecurity.id}',
                child: Image.network(
                  widget.privateSecurity.imageUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: theme.colorScheme.surfaceContainerHighest,
                      child: Center(
                        child: Icon(
                          Icons.image_not_supported,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),

          // Content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and rating
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.privateSecurity.name,
                              style: theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              widget.privateSecurity.serviceType.displayName,
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          RatingDisplay(
                            rating: widget.privateSecurity.rating,
                            size: 20,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${widget.privateSecurity.reviewCount} reviews',
                            style: theme.textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Price
                  Row(
                    children: [
                      if (widget.privateSecurity.isOnSale &&
                          widget.privateSecurity.originalPrice != null) ...[
                        Text(
                          widget.privateSecurity.formattedOriginalPrice!,
                          style: theme.textTheme.titleMedium?.copyWith(
                            decoration: TextDecoration.lineThrough,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                      Text(
                        '${widget.privateSecurity.formattedPrice} / day',
                        style: theme.textTheme.titleLarge?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Security details
                  Text(
                    'Service Details',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  _buildSecurityDetailsGrid(),

                  const SizedBox(height: 24),

                  // Description
                  Text(
                    'Description',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.privateSecurity.description,
                    style: theme.textTheme.bodyMedium,
                  ),

                  const SizedBox(height: 24),

                  // Service details
                  Text(
                    'Service Period',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          // Start date
                          ListTile(
                            title: const Text('Start Date'),
                            subtitle: Text(
                              '${_startDate.day}/${_startDate.month}/${_startDate.year}',
                              style: theme.textTheme.titleMedium,
                            ),
                            trailing: const Icon(Icons.calendar_today),
                            onTap: _selectStartDate,
                          ),
                          const Divider(),

                          // End date
                          ListTile(
                            title: const Text('End Date'),
                            subtitle: Text(
                              '${_endDate.day}/${_endDate.month}/${_endDate.year}',
                              style: theme.textTheme.titleMedium,
                            ),
                            trailing: const Icon(Icons.calendar_today),
                            onTap: _selectEndDate,
                          ),
                          const Divider(),

                          // Duration
                          ListTile(
                            title: const Text('Duration'),
                            subtitle: Text(
                              '$_durationDays days',
                              style: theme.textTheme.titleMedium,
                            ),
                          ),
                          const Divider(),

                          // Total price
                          ListTile(
                            title: const Text('Total Price'),
                            subtitle: Text(
                              '${widget.privateSecurity.currency}${_totalPrice.toStringAsFixed(2)}',
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Security company
                  Text(
                    'Security Company',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Card(
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundImage: NetworkImage(
                            widget.privateSecurity.securityCompanyLogoUrl),
                      ),
                      title: Text(widget.privateSecurity.securityCompany),
                      subtitle: Text(widget.privateSecurity.location),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Cancellation policy
                  Text(
                    'Cancellation Policy',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Text(
                        widget.privateSecurity.cancellationPolicy,
                        style: theme.textTheme.bodyMedium,
                      ),
                    ),
                  ),

                  const SizedBox(height: 100), // Space for the bottom button
                ],
              ),
            ),
          ),
        ],
      ),
      bottomSheet: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: SafeArea(
          child: SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                // Navigate to booking screen
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => PrivateSecurityBookingScreen(
                      privateSecurity: widget.privateSecurity,
                    ),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
              ),
              child: Text(
                'Book Now - ${widget.privateSecurity.currency}${_totalPrice.toStringAsFixed(2)}',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSecurityDetailsGrid() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 3,
      children: [
        _buildSecurityDetailItem(Icons.person, 'Personnel',
            widget.privateSecurity.personnelCount.toString()),
        _buildSecurityDetailItem(Icons.school, 'Training',
            widget.privateSecurity.trainingLevel.displayName),
        _buildSecurityDetailItem(
          Icons.security,
          'Armed',
          widget.privateSecurity.isArmed ? 'Yes' : 'No',
        ),
        _buildSecurityDetailItem(
          Icons.person_outline,
          'Uniformed',
          widget.privateSecurity.isUniformed ? 'Yes' : 'No',
        ),
        _buildSecurityDetailItem(
          Icons.directions_car,
          'Vehicle',
          widget.privateSecurity.hasVehicle ? 'Yes' : 'No',
        ),
        _buildSecurityDetailItem(
          Icons.phone,
          'Communication',
          widget.privateSecurity.hasCommunicationEquipment ? 'Yes' : 'No',
        ),
        _buildSecurityDetailItem(
          Icons.language,
          'Languages',
          widget.privateSecurity.languages.join(', '),
        ),
        _buildSecurityDetailItem(
          Icons.support_agent,
          '24/7 Support',
          widget.privateSecurity.includes24HrSupport ? 'Yes' : 'No',
        ),
      ],
    );
  }

  Widget _buildSecurityDetailItem(IconData icon, String title, String value) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Row(
          children: [
            Icon(
              icon,
              size: 24,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                  ),
                  Text(
                    value,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
