import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/screens/travel/private_security/private_security_booking_confirmation_screen.dart';

// Providers for booking state
final selectedStartDateProvider = StateProvider<DateTime>(
    (ref) => DateTime.now().add(const Duration(days: 1)));
final selectedEndDateProvider = StateProvider<DateTime>(
    (ref) => DateTime.now().add(const Duration(days: 3)));
final selectedPersonnelCountProvider = StateProvider<int>((ref) => 1);
final selectedVehicleProvider = StateProvider<bool>((ref) => false);
final selectedVehicleTypeProvider = StateProvider<String?>((ref) => null);
final specialInstructionsProvider = StateProvider<String>((ref) => '');

/// Screen for booking a private security service
class PrivateSecurityBookingScreen extends ConsumerStatefulWidget {
  /// The private security service to book
  final PrivateSecurity privateSecurity;

  /// Creates a new private security booking screen
  const PrivateSecurityBookingScreen({
    super.key,
    required this.privateSecurity,
  });

  @override
  ConsumerState<PrivateSecurityBookingScreen> createState() =>
      _PrivateSecurityBookingScreenState();
}

class _PrivateSecurityBookingScreenState
    extends ConsumerState<PrivateSecurityBookingScreen> {
  final _formKey = GlobalKey<FormState>();
  final _specialInstructionsController = TextEditingController();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();

  int _currentStep = 0;
  bool _isLoading = false;
  String? _errorMessage;
  double _totalPrice = 0;

  @override
  void initState() {
    super.initState();

    // Set initial values for providers
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Set selected start date to tomorrow
      ref.read(selectedStartDateProvider.notifier).state =
          DateTime.now().add(const Duration(days: 1));

      // Set selected end date to 3 days from now
      ref.read(selectedEndDateProvider.notifier).state =
          DateTime.now().add(const Duration(days: 3));

      // Set selected personnel count to match the service default
      ref.read(selectedPersonnelCountProvider.notifier).state =
          widget.privateSecurity.personnelCount;

      // Set vehicle option based on service default
      ref.read(selectedVehicleProvider.notifier).state =
          widget.privateSecurity.hasVehicle;

      // Set vehicle type if available
      if (widget.privateSecurity.vehicleType != null) {
        ref.read(selectedVehicleTypeProvider.notifier).state =
            widget.privateSecurity.vehicleType;
      }

      // Clear special instructions
      ref.read(specialInstructionsProvider.notifier).state = '';

      // Pre-fill user information if available
      // In a real app, this would use the actual user model properties
      _nameController.text = 'John Doe';
      _emailController.text = '<EMAIL>';
      _phoneController.text = '+**********';

      // Calculate initial price
      _calculateTotalPrice();
    });
  }

  @override
  void dispose() {
    _specialInstructionsController.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  void _calculateTotalPrice() {
    final startDate = ref.read(selectedStartDateProvider);
    final endDate = ref.read(selectedEndDateProvider);
    final personnelCount = ref.read(selectedPersonnelCountProvider);
    final hasVehicle = ref.read(selectedVehicleProvider);

    // Calculate number of days
    final days = endDate.difference(startDate).inDays;
    if (days < 1) return;

    // Base price per day
    double basePrice = widget.privateSecurity.price;

    // Adjust for personnel count (if different from default)
    final defaultPersonnelCount = widget.privateSecurity.personnelCount;
    if (personnelCount != defaultPersonnelCount) {
      // Add 80% of base price for each additional personnel
      if (personnelCount > defaultPersonnelCount) {
        basePrice +=
            (personnelCount - defaultPersonnelCount) * (basePrice * 0.8);
      } else {
        // Reduce by 50% of base price for each fewer personnel (minimum 50% of original price)
        basePrice =
            basePrice * (0.5 + (0.5 * personnelCount / defaultPersonnelCount));
      }
    }

    // Add vehicle cost if selected and not included by default
    if (hasVehicle && !widget.privateSecurity.hasVehicle) {
      basePrice += 100; // Add $100 per day for vehicle
    }

    // Calculate total price
    setState(() {
      _totalPrice = basePrice * days;
    });
  }

  Future<void> _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: ref.read(selectedStartDateProvider),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      ref.read(selectedStartDateProvider.notifier).state = picked;

      // Ensure end date is after start date
      final endDate = ref.read(selectedEndDateProvider);
      if (endDate.isBefore(picked) || endDate.isAtSameMomentAs(picked)) {
        ref.read(selectedEndDateProvider.notifier).state =
            picked.add(const Duration(days: 1));
      }

      _calculateTotalPrice();
    }
  }

  Future<void> _selectEndDate() async {
    final startDate = ref.read(selectedStartDateProvider);
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: ref.read(selectedEndDateProvider),
      firstDate: startDate.add(const Duration(days: 1)),
      lastDate: startDate.add(const Duration(days: 30)),
    );

    if (picked != null) {
      ref.read(selectedEndDateProvider.notifier).state = picked;
      _calculateTotalPrice();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Book Security Service'),
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : _errorMessage != null
              ? ErrorView(
                  error: _errorMessage!,
                  onRetry: () => setState(() => _errorMessage = null),
                )
              : Stepper(
                  currentStep: _currentStep,
                  onStepContinue: _handleContinue,
                  onStepCancel: _handleCancel,
                  controlsBuilder: (context, details) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 16),
                      child: Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              onPressed: details.onStepContinue,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: theme.colorScheme.primary,
                                foregroundColor: theme.colorScheme.onPrimary,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                              ),
                              child: Text(
                                _currentStep == 2
                                    ? 'Complete Booking'
                                    : 'Continue',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: theme.colorScheme.onPrimary,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          if (_currentStep > 0)
                            Expanded(
                              child: OutlinedButton(
                                onPressed: details.onStepCancel,
                                style: OutlinedButton.styleFrom(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 12),
                                ),
                                child: Text(
                                  'Back',
                                  style: theme.textTheme.titleMedium,
                                ),
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                  steps: [
                    _buildServicePeriodStep(),
                    _buildServiceDetailsStep(),
                    _buildContactInfoStep(),
                  ],
                ),
    );
  }

  Step _buildServicePeriodStep() {
    final startDate = ref.watch(selectedStartDateProvider);
    final endDate = ref.watch(selectedEndDateProvider);
    final days = endDate.difference(startDate).inDays;

    return Step(
      title: const Text('Service Period'),
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Service image and name
          Card(
            clipBehavior: Clip.antiAlias,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Service image
                SizedBox(
                  height: 150,
                  width: double.infinity,
                  child: Hero(
                    tag: 'private_security_image_${widget.privateSecurity.id}',
                    child: Image.network(
                      widget.privateSecurity.imageUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Theme.of(context)
                              .colorScheme
                              .surfaceContainerHighest,
                          child: Center(
                            child: Icon(
                              Icons.image_not_supported,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),

                // Service name and type
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.privateSecurity.name,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.privateSecurity.serviceType.displayName,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Date selection
          Text(
            'Select Service Period',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Start date
                  ListTile(
                    title: const Text('Start Date'),
                    subtitle: Text(
                      DateFormat('EEEE, MMMM d, yyyy').format(startDate),
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    trailing: const Icon(Icons.calendar_today),
                    onTap: _selectStartDate,
                  ),
                  const Divider(),

                  // End date
                  ListTile(
                    title: const Text('End Date'),
                    subtitle: Text(
                      DateFormat('EEEE, MMMM d, yyyy').format(endDate),
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    trailing: const Icon(Icons.calendar_today),
                    onTap: _selectEndDate,
                  ),
                  const Divider(),

                  // Duration
                  ListTile(
                    title: const Text('Duration'),
                    subtitle: Text(
                      '$days ${days == 1 ? 'day' : 'days'}',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      isActive: _currentStep == 0,
      state: _currentStep > 0 ? StepState.complete : StepState.indexed,
    );
  }

  Step _buildServiceDetailsStep() {
    final personnelCount = ref.watch(selectedPersonnelCountProvider);
    final hasVehicle = ref.watch(selectedVehicleProvider);
    final vehicleType = ref.watch(selectedVehicleTypeProvider);

    return Step(
      title: const Text('Service Details'),
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Personnel count
          Text(
            'Number of Security Personnel',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  IconButton(
                    onPressed: personnelCount > 1
                        ? () {
                            ref
                                .read(selectedPersonnelCountProvider.notifier)
                                .state = personnelCount - 1;
                            _calculateTotalPrice();
                          }
                        : null,
                    icon: const Icon(Icons.remove_circle_outline),
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  Text(
                    '$personnelCount',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  IconButton(
                    onPressed: personnelCount < 10
                        ? () {
                            ref
                                .read(selectedPersonnelCountProvider.notifier)
                                .state = personnelCount + 1;
                            _calculateTotalPrice();
                          }
                        : null,
                    icon: const Icon(Icons.add_circle_outline),
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Vehicle option
          Text(
            'Vehicle Options',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  SwitchListTile(
                    title: const Text('Include Vehicle'),
                    subtitle: Text(
                      widget.privateSecurity.hasVehicle
                          ? 'Vehicle is included with this service'
                          : 'Add a vehicle for an additional fee',
                    ),
                    value: hasVehicle,
                    onChanged: (value) {
                      ref.read(selectedVehicleProvider.notifier).state = value;
                      _calculateTotalPrice();
                    },
                    secondary: Icon(
                      Icons.directions_car,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  if (hasVehicle) ...[
                    const Divider(),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Vehicle Type',
                          border: OutlineInputBorder(),
                        ),
                        value: vehicleType,
                        items: const [
                          DropdownMenuItem(
                            value: 'sedan',
                            child: Text('Sedan'),
                          ),
                          DropdownMenuItem(
                            value: 'suv',
                            child: Text('SUV'),
                          ),
                          DropdownMenuItem(
                            value: 'armored',
                            child: Text('Armored Vehicle'),
                          ),
                        ],
                        onChanged: (value) {
                          ref.read(selectedVehicleTypeProvider.notifier).state =
                              value;
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Special instructions
          Text(
            'Special Instructions (Optional)',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _specialInstructionsController,
            decoration: const InputDecoration(
              hintText:
                  'E.g., specific security concerns, locations to visit, etc.',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
            onChanged: (value) {
              ref.read(specialInstructionsProvider.notifier).state = value;
            },
          ),

          const SizedBox(height: 24),

          // Price summary
          Card(
            color: Theme.of(context).colorScheme.primaryContainer,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Price Summary',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color:
                              Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Total Price:',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: Theme.of(context)
                                      .colorScheme
                                      .onPrimaryContainer,
                                ),
                      ),
                      Text(
                        '${widget.privateSecurity.currency}${_totalPrice.toStringAsFixed(2)}',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onPrimaryContainer,
                            ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      isActive: _currentStep == 1,
      state: _currentStep > 1 ? StepState.complete : StepState.indexed,
    );
  }

  Step _buildContactInfoStep() {
    return Step(
      title: const Text('Contact Information'),
      content: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Name
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Full Name',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Phone
            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone Number',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your phone number';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Email
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your email';
                }
                if (!value.contains('@') || !value.contains('.')) {
                  return 'Please enter a valid email';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),

            // Booking summary
            _buildBookingSummary(),
          ],
        ),
      ),
      isActive: _currentStep == 2,
      state: _currentStep > 2 ? StepState.complete : StepState.indexed,
    );
  }

  Widget _buildBookingSummary() {
    final theme = Theme.of(context);
    final startDate = ref.watch(selectedStartDateProvider);
    final endDate = ref.watch(selectedEndDateProvider);
    final days = endDate.difference(startDate).inDays;
    final personnelCount = ref.watch(selectedPersonnelCountProvider);
    final hasVehicle = ref.watch(selectedVehicleProvider);
    final vehicleType = ref.watch(selectedVehicleTypeProvider);
    final specialInstructions = ref.watch(specialInstructionsProvider);

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Booking Summary',
              style: theme.textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            _buildSummaryRow('Service', widget.privateSecurity.name),
            _buildSummaryRow(
                'Type', widget.privateSecurity.serviceType.displayName),
            _buildSummaryRow('Start Date',
                DateFormat('EEEE, MMMM d, yyyy').format(startDate)),
            _buildSummaryRow(
                'End Date', DateFormat('EEEE, MMMM d, yyyy').format(endDate)),
            _buildSummaryRow('Duration', '$days ${days == 1 ? 'day' : 'days'}'),
            _buildSummaryRow('Personnel', personnelCount.toString()),
            _buildSummaryRow(
                'Vehicle',
                hasVehicle
                    ? 'Yes${vehicleType != null ? ' ($vehicleType)' : ''}'
                    : 'No'),
            if (specialInstructions.isNotEmpty)
              _buildSummaryRow('Special Instructions', specialInstructions),
            const Divider(),
            _buildSummaryRow(
              'Total Price',
              '${widget.privateSecurity.currency}${_totalPrice.toStringAsFixed(2)}',
              isHighlighted: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value,
      {bool isHighlighted = false}) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: isHighlighted
                  ? theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    )
                  : theme.textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  void _handleContinue() {
    // Validate current step
    if (_currentStep == 0) {
      // Validate service period
      final startDate = ref.read(selectedStartDateProvider);
      final endDate = ref.read(selectedEndDateProvider);
      if (endDate.difference(startDate).inDays < 1) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('End date must be after start date')),
        );
        return;
      }
    } else if (_currentStep == 2) {
      // Validate contact info and submit booking
      if (_formKey.currentState?.validate() ?? false) {
        _submitBooking();
        return;
      } else {
        return;
      }
    }

    // Move to next step
    setState(() {
      _currentStep += 1;
    });
  }

  void _handleCancel() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep -= 1;
      });
    }
  }

  Future<void> _submitBooking() async {
    final startDate = ref.read(selectedStartDateProvider);
    final endDate = ref.read(selectedEndDateProvider);
    final personnelCount = ref.read(selectedPersonnelCountProvider);
    final hasVehicle = ref.read(selectedVehicleProvider);
    final vehicleType = ref.read(selectedVehicleTypeProvider);
    final specialInstructions = ref.read(specialInstructionsProvider);
    final user = ref.read(authStateProvider).user;

    if (user == null) {
      setState(() {
        _errorMessage = 'You must be logged in to book a security service';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // In a real app, this would call a service to create the booking
      // For now, we'll just simulate a network delay and navigate to the confirmation screen
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        // Navigate to confirmation screen
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => PrivateSecurityBookingConfirmationScreen(
              privateSecurity: widget.privateSecurity,
              startDate: startDate,
              endDate: endDate,
              personnelCount: personnelCount,
              hasVehicle: hasVehicle,
              vehicleType: vehicleType,
              specialInstructions: specialInstructions,
              totalPrice: _totalPrice,
              bookingId: 'SEC-${DateTime.now().millisecondsSinceEpoch}',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Error: ${e.toString()}';
        });
      }
    }
  }
}
