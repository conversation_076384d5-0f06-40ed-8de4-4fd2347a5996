// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/services/travel/visa/visa_tracking_service.dart';
import 'package:culture_connect/screens/travel/visa/visa_notification_preferences_screen.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';

/// Screen for monitoring visa stay duration with overstay warnings
class VisaStayTrackerScreen extends ConsumerStatefulWidget {
  /// Creates a new visa stay tracker screen
  const VisaStayTrackerScreen({super.key});

  @override
  ConsumerState<VisaStayTrackerScreen> createState() =>
      _VisaStayTrackerScreenState();
}

class _VisaStayTrackerScreenState extends ConsumerState<VisaStayTrackerScreen> {
  final VisaTrackingService _trackingService = VisaTrackingService();

  // State
  List<TrackedVisa> _trackedVisas = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadTrackedVisas();
  }

  Future<void> _loadTrackedVisas() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      await _trackingService.initialize();
      final visas = _trackingService.getAllTrackedVisas();

      if (mounted) {
        setState(() {
          _trackedVisas = visas;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to load tracked visas: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Visa Stay Tracker'),
        actions: [
          IconButton(
            onPressed: _showAddVisaDialog,
            icon: const Icon(Icons.add),
            tooltip: 'Add visa tracking',
          ),
        ],
      ),
      body: _buildBody(theme),
    );
  }

  Widget _buildBody(ThemeData theme) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            LoadingIndicator(),
            SizedBox(height: 16),
            Text('Loading tracked visas...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return ErrorView(
        error: _errorMessage!,
        onRetry: _loadTrackedVisas,
      );
    }

    if (_trackedVisas.isEmpty) {
      return _buildEmptyState(theme);
    }

    return RefreshIndicator(
      onRefresh: _loadTrackedVisas,
      child: ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: _trackedVisas.length,
        itemBuilder: (context, index) {
          final visa = _trackedVisas[index];
          return Padding(
            padding: EdgeInsets.only(bottom: 16),
            child: _buildVisaCard(visa, theme),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.flight_takeoff,
            size: 64,
            color: theme.colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'No visas being tracked',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.outline,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add a visa to start tracking your stay duration',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.outline,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          FilledButton.icon(
            onPressed: _showAddVisaDialog,
            icon: const Icon(Icons.add),
            label: const Text('Add Visa'),
          ),
        ],
      ),
    );
  }

  Widget _buildVisaCard(TrackedVisa visa, ThemeData theme) {
    final statusColor =
        Color(int.parse('0xFF${visa.status.colorHex.substring(1)}'));
    final isUrgent = visa.status == VisaTrackingStatus.expiringSoon ||
        visa.status == VisaTrackingStatus.expired ||
        visa.status == VisaTrackingStatus.overstay;

    return Card(
      elevation: isUrgent ? 4 : 2,
      child: Container(
        decoration: isUrgent
            ? BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: statusColor, width: 2),
              )
            : null,
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildVisaHeader(visa, theme, statusColor),
              const SizedBox(height: 12),
              _buildVisaDetails(visa, theme),
              const SizedBox(height: 12),
              _buildCountdownSection(visa, theme, statusColor),
              const SizedBox(height: 12),
              _buildVisaActions(visa, theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVisaHeader(
      TrackedVisa visa, ThemeData theme, Color statusColor) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: statusColor.withAlpha(26),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getStatusIcon(visa.status),
            color: statusColor,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                visa.country,
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      visa.status.displayName,
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    visa.visaType,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.outline,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => _showVisaOptions(visa),
          icon: const Icon(Icons.more_vert),
        ),
      ],
    );
  }

  Widget _buildVisaDetails(TrackedVisa visa, ThemeData theme) {
    return Column(
      children: [
        _buildDetailRow(
          'Entry Date',
          _formatDate(visa.entryDate),
          Icons.flight_land,
          theme,
        ),
        const SizedBox(height: 8),
        _buildDetailRow(
          'Expiry Date',
          _formatDate(visa.expiryDate),
          Icons.event,
          theme,
        ),
        const SizedBox(height: 8),
        _buildDetailRow(
          'Max Stay',
          '${visa.maxStayDays} days',
          Icons.schedule,
          theme,
        ),
      ],
    );
  }

  Widget _buildDetailRow(
      String label, String value, IconData icon, ThemeData theme) {
    return Row(
      children: [
        Icon(icon, size: 16, color: theme.colorScheme.outline),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.outline,
          ),
        ),
        Text(
          value,
          style: theme.textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildCountdownSection(
      TrackedVisa visa, ThemeData theme, Color statusColor) {
    final daysRemaining = visa.daysRemainingInStay;
    final daysSinceEntry = visa.daysSinceEntry;

    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildCountdownItem(
                  'Days in Country',
                  daysSinceEntry.toString(),
                  theme.colorScheme.primary,
                  theme,
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: theme.colorScheme.outline,
              ),
              Expanded(
                child: _buildCountdownItem(
                  'Days Remaining',
                  daysRemaining > 0 ? daysRemaining.toString() : 'OVERSTAY',
                  daysRemaining > 0 ? statusColor : Colors.red,
                  theme,
                ),
              ),
            ],
          ),
          if (visa.status == VisaTrackingStatus.overstay) ...[
            const SizedBox(height: 8),
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.withAlpha(26),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                children: [
                  const Icon(Icons.warning, color: Colors.red, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'You have exceeded your allowed stay duration. Please contact immigration authorities.',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.red,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCountdownItem(
      String label, String value, Color color, ThemeData theme) {
    return Column(
      children: [
        Text(
          value,
          style: theme.textTheme.headlineSmall?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.outline,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildVisaActions(TrackedVisa visa, ThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _editNotifications(visa),
            icon: Icon(
              visa.notificationsEnabled
                  ? Icons.notifications
                  : Icons.notifications_off,
              size: 16,
            ),
            label: const Text('Notifications'),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: FilledButton.icon(
            onPressed: () => _viewDetails(visa),
            icon: const Icon(Icons.info, size: 16),
            label: const Text('Details'),
          ),
        ),
      ],
    );
  }

  IconData _getStatusIcon(VisaTrackingStatus status) {
    switch (status) {
      case VisaTrackingStatus.active:
        return Icons.check_circle;
      case VisaTrackingStatus.expiringSoon:
        return Icons.warning;
      case VisaTrackingStatus.expired:
        return Icons.error;
      case VisaTrackingStatus.overstay:
        return Icons.dangerous;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showAddVisaDialog() {
    // TODO: Implement add visa dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Add visa dialog - coming soon!'),
      ),
    );
  }

  void _showVisaOptions(TrackedVisa visa) {
    // TODO: Implement visa options menu
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Options for ${visa.country} visa - coming soon!'),
      ),
    );
  }

  Future<void> _editNotifications(TrackedVisa visa) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => VisaNotificationPreferencesScreen(
          trackedVisa: visa,
        ),
      ),
    );

    if (result == true) {
      // Refresh the visa list to show updated preferences
      _loadTrackedVisas();
    }
  }

  void _viewDetails(TrackedVisa visa) {
    // TODO: Implement visa details view
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Details for ${visa.country} visa - coming soon!'),
      ),
    );
  }
}
