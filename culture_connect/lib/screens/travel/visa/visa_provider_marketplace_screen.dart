// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/document/visa_service_provider.dart';
import 'package:culture_connect/services/travel/visa/visa_provider_service.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';

/// Screen for browsing and comparing visa service providers
class VisaProviderMarketplaceScreen extends ConsumerStatefulWidget {
  /// Creates a new visa provider marketplace screen
  const VisaProviderMarketplaceScreen({
    super.key,
    this.destinationCountry,
    this.visaType,
  });

  /// Destination country code (optional pre-filter)
  final String? destinationCountry;

  /// Visa type (optional pre-filter)
  final VisaProviderSpecialization? visaType;

  @override
  ConsumerState<VisaProviderMarketplaceScreen> createState() =>
      _VisaProviderMarketplaceScreenState();
}

class _VisaProviderMarketplaceScreenState
    extends ConsumerState<VisaProviderMarketplaceScreen> {
  final VisaProviderService _providerService = VisaProviderService();
  final TextEditingController _searchController = TextEditingController();

  // State
  List<VisaServiceProvider> _filteredProviders = [];
  bool _isLoading = true;
  String? _errorMessage;

  // Filters
  String? _selectedDestinationCountry;
  VisaProviderSpecialization? _selectedVisaType;
  VisaProviderTier? _selectedTier;
  double? _minRating;
  double? _maxPrice;
  bool? _isFeatured;
  bool? _isVerifiedPartner;
  String _sortBy = 'rating';
  bool _sortAscending = false;

  @override
  void initState() {
    super.initState();
    _selectedDestinationCountry = widget.destinationCountry;
    _selectedVisaType = widget.visaType;
    _loadProviders();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadProviders() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final providers = await _providerService.searchProviders(
        query:
            _searchController.text.isNotEmpty ? _searchController.text : null,
        destinationCountry: _selectedDestinationCountry,
        specialization: _selectedVisaType,
        tier: _selectedTier,
        minRating: _minRating,
        maxPrice: _maxPrice,
        isFeatured: _isFeatured,
        isVerifiedPartner: _isVerifiedPartner,
        sortBy: _sortBy,
        sortAscending: _sortAscending,
      );

      if (mounted) {
        setState(() {
          _filteredProviders = providers;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to load visa service providers: $e';
        });
      }
    }
  }

  void _clearFilters() {
    setState(() {
      _selectedDestinationCountry = widget.destinationCountry;
      _selectedVisaType = widget.visaType;
      _selectedTier = null;
      _minRating = null;
      _maxPrice = null;
      _isFeatured = null;
      _isVerifiedPartner = null;
      _sortBy = 'rating';
      _sortAscending = false;
      _searchController.clear();
    });
    _loadProviders();
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => _buildFilterDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Visa Service Providers'),
        actions: [
          IconButton(
            onPressed: _showFilterDialog,
            icon: const Icon(Icons.filter_list),
            tooltip: 'Filter providers',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(theme),
          _buildActiveFilters(theme),
          Expanded(
            child: _buildProvidersList(theme),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search providers...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    _loadProviders();
                  },
                  icon: const Icon(Icons.clear),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onSubmitted: (_) => _loadProviders(),
      ),
    );
  }

  Widget _buildActiveFilters(ThemeData theme) {
    final activeFilters = <Widget>[];

    if (_selectedDestinationCountry != null) {
      activeFilters.add(_buildFilterChip(
        'Country: $_selectedDestinationCountry',
        () => setState(() {
          _selectedDestinationCountry = null;
          _loadProviders();
        }),
      ));
    }

    if (_selectedVisaType != null) {
      activeFilters.add(_buildFilterChip(
        'Type: ${_selectedVisaType!.displayName}',
        () => setState(() {
          _selectedVisaType = null;
          _loadProviders();
        }),
      ));
    }

    if (_selectedTier != null) {
      activeFilters.add(_buildFilterChip(
        'Tier: ${_selectedTier!.displayName}',
        () => setState(() {
          _selectedTier = null;
          _loadProviders();
        }),
      ));
    }

    if (_minRating != null) {
      activeFilters.add(_buildFilterChip(
        'Rating: ${_minRating!.toStringAsFixed(1)}+',
        () => setState(() {
          _minRating = null;
          _loadProviders();
        }),
      ));
    }

    if (activeFilters.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Active Filters:',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: _clearFilters,
                child: const Text('Clear All'),
              ),
            ],
          ),
          Wrap(
            spacing: 8,
            children: activeFilters,
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, VoidCallback onDeleted) {
    return Chip(
      label: Text(label),
      onDeleted: onDeleted,
      deleteIcon: const Icon(Icons.close, size: 18),
    );
  }

  Widget _buildProvidersList(ThemeData theme) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            LoadingIndicator(),
            SizedBox(height: 16),
            Text('Loading visa service providers...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return ErrorView(
        error: _errorMessage!,
        onRetry: _loadProviders,
      );
    }

    if (_filteredProviders.isEmpty) {
      return _buildEmptyState(theme);
    }

    return RefreshIndicator(
      onRefresh: _loadProviders,
      child: ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: _filteredProviders.length,
        itemBuilder: (context, index) {
          final provider = _filteredProviders[index];
          return Padding(
            padding: EdgeInsets.only(bottom: 16),
            child: _buildProviderCard(provider, theme),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.business_outlined,
            size: 64,
            color: theme.colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'No providers found',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.outline,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search criteria or filters',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.outline,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          FilledButton(
            onPressed: _clearFilters,
            child: const Text('Clear Filters'),
          ),
        ],
      ),
    );
  }

  Widget _buildProviderCard(VisaServiceProvider provider, ThemeData theme) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => _navigateToProviderDetails(provider),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildProviderHeader(provider, theme),
              const SizedBox(height: 12),
              _buildProviderInfo(provider, theme),
              const SizedBox(height: 12),
              _buildProviderActions(provider, theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProviderHeader(VisaServiceProvider provider, ThemeData theme) {
    return Row(
      children: [
        CircleAvatar(
          radius: 24,
          backgroundImage: NetworkImage(provider.logoUrl),
          onBackgroundImageError: (_, __) {},
          child: Text(provider.name.substring(0, 1).toUpperCase()),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      provider.name,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (provider.isFeatured)
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'FEATURED',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: theme.colorScheme.onPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(
                    Icons.star,
                    size: 16,
                    color: Colors.amber,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${provider.formattedRating} (${provider.formattedReviewCount})',
                    style: theme.textTheme.bodySmall,
                  ),
                  const SizedBox(width: 12),
                  if (provider.isVerifiedPartner) ...[
                    Icon(
                      Icons.verified,
                      size: 16,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Verified',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProviderInfo(VisaServiceProvider provider, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          provider.description,
          style: theme.textTheme.bodyMedium,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: [
            _buildInfoChip(
              Icons.schedule,
              provider.formattedAverageProcessingTime,
              theme,
            ),
            _buildInfoChip(
              Icons.check_circle,
              provider.formattedSuccessRate,
              theme,
            ),
            _buildInfoChip(
              Icons.attach_money,
              'From ${provider.formattedMinimumFee}',
              theme,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoChip(IconData icon, String label, ThemeData theme) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: theme.colorScheme.onSurfaceVariant),
          const SizedBox(width: 4),
          Text(
            label,
            style: theme.textTheme.labelSmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProviderActions(VisaServiceProvider provider, ThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => _viewProviderDetails(provider),
            child: const Text('View Details'),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: FilledButton(
            onPressed: () => _contactProvider(provider),
            child: const Text('Contact'),
          ),
        ),
      ],
    );
  }

  Widget _buildFilterDialog() {
    // TODO: Implement comprehensive filter dialog
    return Container(
      padding: EdgeInsets.all(16),
      child: const Text('Filter dialog - to be implemented'),
    );
  }

  void _navigateToProviderDetails(VisaServiceProvider provider) {
    // TODO: Navigate to provider details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Provider details for ${provider.name} - coming soon!'),
      ),
    );
  }

  void _viewProviderDetails(VisaServiceProvider provider) {
    _navigateToProviderDetails(provider);
  }

  void _contactProvider(VisaServiceProvider provider) {
    // TODO: Implement provider contact functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Contact ${provider.name} - coming soon!'),
      ),
    );
  }
}
