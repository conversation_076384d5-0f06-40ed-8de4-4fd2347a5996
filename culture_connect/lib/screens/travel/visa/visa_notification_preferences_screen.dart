// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/services/travel/visa/visa_tracking_service.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

/// Screen for customizing visa notification preferences
class VisaNotificationPreferencesScreen extends ConsumerStatefulWidget {
  /// Creates a new visa notification preferences screen
  const VisaNotificationPreferencesScreen({
    super.key,
    required this.trackedVisa,
  });

  /// The tracked visa to configure notifications for
  final TrackedVisa trackedVisa;

  @override
  ConsumerState<VisaNotificationPreferencesScreen> createState() =>
      _VisaNotificationPreferencesScreenState();
}

class _VisaNotificationPreferencesScreenState
    extends ConsumerState<VisaNotificationPreferencesScreen> {
  final VisaTrackingService _trackingService = VisaTrackingService();

  // State
  bool _notificationsEnabled = true;
  List<int> _notificationDays = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Predefined notification options
  final Map<String, List<int>> _presetOptions = {
    'Minimal': [3, 1],
    'Standard': [15, 7, 3, 1],
    'Comprehensive': [30, 15, 7, 3, 1],
    'Extended': [60, 30, 15, 7, 3, 1],
  };

  @override
  void initState() {
    super.initState();
    _initializePreferences();
  }

  void _initializePreferences() {
    setState(() {
      _notificationsEnabled = widget.trackedVisa.notificationsEnabled;
      _notificationDays = List.from(widget.trackedVisa.notificationDays);
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Preferences'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _savePreferences,
            child: _isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ],
      ),
      body: _buildBody(theme),
    );
  }

  Widget _buildBody(ThemeData theme) {
    if (_errorMessage != null) {
      return ErrorView(
        error: _errorMessage!,
        onRetry: () => setState(() => _errorMessage = null),
      );
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildVisaInfo(theme),
          const SizedBox(height: 24),
          _buildNotificationToggle(theme),
          if (_notificationsEnabled) ...[
            const SizedBox(height: 24),
            _buildPresetOptions(theme),
            const SizedBox(height: 24),
            _buildCustomNotifications(theme),
            const SizedBox(height: 24),
            _buildNotificationPreview(theme),
          ],
        ],
      ),
    );
  }

  Widget _buildVisaInfo(ThemeData theme) {
    final visa = widget.trackedVisa;
    final statusColor =
        Color(int.parse('0xFF${visa.status.colorHex.substring(1)}'));

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: statusColor.withAlpha(26),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.flight_takeoff,
                    color: statusColor,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        visa.country,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${visa.visaType} • ${visa.daysRemainingInStay} days remaining',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.outline,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationToggle(ThemeData theme) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Enable Notifications',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Receive alerts before your visa expires or stay limit is reached',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.outline,
              ),
            ),
            const SizedBox(height: 12),
            SwitchListTile(
              value: _notificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _notificationsEnabled = value;
                });
              },
              title: Text(
                _notificationsEnabled
                    ? 'Notifications Enabled'
                    : 'Notifications Disabled',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              contentPadding: EdgeInsets.zero,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPresetOptions(ThemeData theme) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Preset Options',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Choose a predefined notification schedule',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.outline,
              ),
            ),
            const SizedBox(height: 16),
            ...(_presetOptions.entries.map((entry) {
              final isSelected = _listsEqual(_notificationDays, entry.value);
              return Padding(
                padding: EdgeInsets.only(bottom: 8),
                child: InkWell(
                  onTap: () {
                    setState(() {
                      _notificationDays = List.from(entry.value);
                    });
                  },
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: isSelected
                            ? theme.colorScheme.primary
                            : theme.colorScheme.outline,
                      ),
                      borderRadius: BorderRadius.circular(8),
                      color: isSelected
                          ? theme.colorScheme.primaryContainer
                          : null,
                    ),
                    child: Row(
                      children: [
                        Icon(
                          isSelected
                              ? Icons.radio_button_checked
                              : Icons.radio_button_unchecked,
                          color: isSelected
                              ? theme.colorScheme.primary
                              : theme.colorScheme.outline,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                entry.key,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                '${entry.value.join(", ")} days before',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.outline,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            })),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomNotifications(ThemeData theme) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Custom Schedule',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                TextButton.icon(
                  onPressed: _addCustomNotification,
                  icon: const Icon(Icons.add, size: 16),
                  label: const Text('Add'),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Set custom notification days',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.outline,
              ),
            ),
            const SizedBox(height: 16),
            if (_notificationDays.isEmpty)
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    'No custom notifications set',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.outline,
                    ),
                  ),
                ),
              )
            else
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _notificationDays.map((days) {
                  return Chip(
                    label: Text('$days days'),
                    onDeleted: () {
                      setState(() {
                        _notificationDays.remove(days);
                      });
                    },
                    deleteIcon: const Icon(Icons.close, size: 16),
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationPreview(ThemeData theme) {
    if (_notificationDays.isEmpty) return const SizedBox.shrink();

    final sortedDays = List<int>.from(_notificationDays)
      ..sort((a, b) => b.compareTo(a));

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notification Preview',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'You will receive notifications on these days:',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.outline,
              ),
            ),
            const SizedBox(height: 16),
            ...sortedDays.map((days) {
              final notificationDate =
                  widget.trackedVisa.expiryDate.subtract(Duration(days: days));
              return Padding(
                padding: EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    Icon(
                      Icons.notifications,
                      size: 16,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '$days days before',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${notificationDate.day}/${notificationDate.month}/${notificationDate.year}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.outline,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  void _addCustomNotification() {
    showDialog(
      context: context,
      builder: (context) => _buildAddNotificationDialog(),
    );
  }

  Widget _buildAddNotificationDialog() {
    final controller = TextEditingController();

    return AlertDialog(
      title: const Text('Add Notification'),
      content: TextField(
        controller: controller,
        keyboardType: TextInputType.number,
        decoration: const InputDecoration(
          labelText: 'Days before expiry',
          hintText: 'Enter number of days',
          border: OutlineInputBorder(),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            final days = int.tryParse(controller.text);
            if (days != null && days > 0 && !_notificationDays.contains(days)) {
              setState(() {
                _notificationDays.add(days);
                _notificationDays.sort((a, b) => b.compareTo(a));
              });
            }
            Navigator.pop(context);
          },
          child: const Text('Add'),
        ),
      ],
    );
  }

  bool _listsEqual(List<int> a, List<int> b) {
    if (a.length != b.length) return false;
    final sortedA = List<int>.from(a)..sort();
    final sortedB = List<int>.from(b)..sort();
    for (int i = 0; i < sortedA.length; i++) {
      if (sortedA[i] != sortedB[i]) return false;
    }
    return true;
  }

  Future<void> _savePreferences() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      await _trackingService.updateNotificationPreferences(
        visaId: widget.trackedVisa.id,
        notificationsEnabled: _notificationsEnabled,
        notificationDays: _notificationDays,
      );

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Notification preferences saved')),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to save preferences: $e';
        });
      }
    }
  }
}
