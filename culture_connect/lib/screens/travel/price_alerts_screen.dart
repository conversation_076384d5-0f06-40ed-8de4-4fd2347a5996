import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/models/travel/price_alert.dart';
import 'package:culture_connect/providers/services_providers.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/screens/travel/create_price_alert_screen.dart';
import 'package:culture_connect/widgets/common/empty_state.dart';
import 'package:culture_connect/widgets/common/error_state.dart';
import 'package:culture_connect/widgets/travel/price_alert_card.dart';

/// A screen that displays price alerts
class PriceAlertsScreen extends ConsumerStatefulWidget {
  /// Creates a new price alerts screen
  const PriceAlertsScreen({super.key});

  @override
  ConsumerState<PriceAlertsScreen> createState() => _PriceAlertsScreenState();
}

class _PriceAlertsScreenState extends ConsumerState<PriceAlertsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadPriceAlerts();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Load price alerts
  Future<void> _loadPriceAlerts() async {
    final userAsync = ref.read(currentUserModelProvider);
    final userId = userAsync.value?.id;
    if (userId == null) return;

    if (!mounted) return;
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final priceAlertService = ref.read(priceAlertServiceProvider(userId));
      await priceAlertService.initialize();

      // Check for price updates
      await priceAlertService.checkPriceAlerts();

      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load price alerts: $e';
      });
    }
  }

  /// Delete a price alert
  Future<void> _deletePriceAlert(String alertId) async {
    final userAsync = ref.read(currentUserModelProvider);
    final userId = userAsync.value?.id;
    if (userId == null) return;

    try {
      final priceAlertService = ref.read(priceAlertServiceProvider(userId));
      await priceAlertService.deletePriceAlert(alertId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Price alert deleted'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete price alert: $e'),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final userAsync = ref.watch(currentUserModelProvider);
    final userId = userAsync.value?.id;

    if (userId == null) {
      return const Scaffold(
        body: Center(
          child: Text('Please sign in to view price alerts'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Price Alerts'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Active'),
            Tab(text: 'Triggered'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? ErrorState(
                  message: _errorMessage!,
                  onRetry: _loadPriceAlerts,
                )
              : RefreshIndicator(
                  onRefresh: _loadPriceAlerts,
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildActiveAlerts(userId),
                      _buildTriggeredAlerts(userId),
                    ],
                  ),
                ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          final result = await Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const CreatePriceAlertScreen(),
            ),
          );

          if (result == true && mounted) {
            _loadPriceAlerts();
          }
        },
        icon: const Icon(Icons.add),
        label: const Text('New Alert'),
      ),
    );
  }

  /// Build the active alerts tab
  Widget _buildActiveAlerts(String userId) {
    final activeAlertsProvider = FutureProvider<List<PriceAlert>>((ref) async {
      final priceAlertService = ref.watch(priceAlertServiceProvider(userId));
      return priceAlertService.getActivePriceAlerts();
    });

    return ref.watch(activeAlertsProvider).when(
          data: (alerts) {
            if (alerts.isEmpty) {
              return const EmptyState(
                icon: Icons.notifications_none,
                title: 'No Active Alerts',
                message:
                    'You don\'t have any active price alerts. Create one to get notified when prices drop!',
              );
            }

            return ListView.builder(
              padding: EdgeInsets.only(bottom: 80),
              itemCount: alerts.length,
              itemBuilder: (context, index) {
                final alert = alerts[index];
                return PriceAlertCard(
                  priceAlert: alert,
                  onDelete: () => _deletePriceAlert(alert.id),
                  onEdit: () {
                    // TODO: Implement edit functionality
                  },
                  onTap: () {
                    // TODO: Navigate to travel service details
                  },
                );
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stackTrace) => ErrorState(
            message: 'Failed to load active alerts: $error',
            onRetry: _loadPriceAlerts,
          ),
        );
  }

  /// Build the triggered alerts tab
  Widget _buildTriggeredAlerts(String userId) {
    final triggeredAlertsProvider =
        FutureProvider<List<PriceAlert>>((ref) async {
      final priceAlertService = ref.watch(priceAlertServiceProvider(userId));
      return priceAlertService.getTriggeredPriceAlerts();
    });

    return ref.watch(triggeredAlertsProvider).when(
          data: (alerts) {
            if (alerts.isEmpty) {
              return const EmptyState(
                icon: Icons.notifications_off,
                title: 'No Triggered Alerts',
                message:
                    'You don\'t have any triggered price alerts yet. We\'ll notify you when prices drop below your target!',
              );
            }

            return ListView.builder(
              padding: EdgeInsets.only(bottom: 80),
              itemCount: alerts.length,
              itemBuilder: (context, index) {
                final alert = alerts[index];
                return PriceAlertCard(
                  priceAlert: alert,
                  onDelete: () => _deletePriceAlert(alert.id),
                  onTap: () {
                    // TODO: Navigate to travel service details
                  },
                );
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stackTrace) => ErrorState(
            message: 'Failed to load triggered alerts: $error',
            onRetry: _loadPriceAlerts,
          ),
        );
  }
}
