import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/transfer/transfers.dart'
    as models;
import 'package:culture_connect/models/location/geo_location.dart';
import 'package:culture_connect/models/travel/flight/flight_info.dart';
import 'package:culture_connect/providers/travel/transfer/transfer_provider.dart';
import 'package:culture_connect/screens/travel/transfer/transfer_booking_details_screen.dart';
import 'package:culture_connect/services/travel/transfer/flight_integration_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';
import 'package:culture_connect/widgets/common/custom_text_field.dart';
import 'package:culture_connect/widgets/common/date_time_picker_field.dart';
import 'package:culture_connect/widgets/common/error_state.dart';
import 'package:culture_connect/widgets/travel/transfer/transfer_widgets.dart';
import 'package:provider/provider.dart';

// Type aliases to make the code more readable
typedef TransferServiceModel = models.TransferService;
typedef TransferLocation = models.TransferLocation;
typedef TransferLocationType = models.TransferLocationType;
typedef TransferBooking = models.TransferBooking;
typedef TransferBookingStatus = models.TransferBookingStatus;

/// A screen for booking a transfer
class TransferBookingScreen extends StatefulWidget {
  /// The ID of the transfer to book
  final String transferId;

  /// Creates a new transfer booking screen
  const TransferBookingScreen({
    super.key,
    required this.transferId,
  });

  @override
  State<TransferBookingScreen> createState() => _TransferBookingScreenState();
}

class _TransferBookingScreenState extends State<TransferBookingScreen> {
  final _formKey = GlobalKey<FormState>();
  TransferServiceModel? _transfer;
  bool _isLoading = true;
  String? _error;
  final LoggingService _loggingService = LoggingService();

  // Form fields
  TransferLocation? _pickupLocation;
  TransferLocation? _dropoffLocation;
  DateTime? _pickupDateTime;
  int _passengerCount = 1;
  int _luggageCount = 1;
  final _specialRequestsController = TextEditingController();
  final _contactNameController = TextEditingController();
  final _contactPhoneController = TextEditingController();
  final _contactEmailController = TextEditingController();
  final _flightInfoController = TextEditingController();
  FlightInfo? _flightInfo;

  @override
  void initState() {
    super.initState();
    _loadTransfer();
  }

  @override
  void dispose() {
    _specialRequestsController.dispose();
    _contactNameController.dispose();
    _contactPhoneController.dispose();
    _contactEmailController.dispose();
    _flightInfoController.dispose();
    super.dispose();
  }

  /// Load the transfer
  Future<void> _loadTransfer() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final transferProvider =
          Provider.of<TransferProvider>(context, listen: false);
      final transfer = await transferProvider.getTransfer(widget.transferId);

      if (!mounted) return;

      setState(() {
        _transfer = transfer;
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      _loggingService.error(
        'TransferBookingScreen',
        'Failed to load transfer: ${widget.transferId}',
        e,
        stackTrace,
      );

      if (!mounted) return;

      setState(() {
        _error = 'Failed to load transfer: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Book Transfer'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? ErrorState(
                  message: _error!,
                  onRetry: _loadTransfer,
                )
              : _transfer != null
                  ? _buildBookingForm()
                  : const Center(
                      child: Text('Transfer not found'),
                    ),
    );
  }

  /// Build the booking form
  Widget _buildBookingForm() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTransferSummary(),
            const SizedBox(height: 24),
            _buildLocationSection(),
            const SizedBox(height: 24),
            _buildPassengersSection(),
            const SizedBox(height: 24),
            _buildFlightInfoSection(),
            const SizedBox(height: 24),
            _buildContactSection(),
            const SizedBox(height: 24),
            _buildSpecialRequestsSection(),
            const SizedBox(height: 24),
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  /// Build the transfer summary
  Widget _buildTransferSummary() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Transfer Summary',
              style: AppTextStyles.headline6,
            ),
            const SizedBox(height: 16),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    _transfer!.imageUrl,
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 80,
                        height: 80,
                        color: AppColors.surface,
                        child: const Center(
                          child: Icon(
                            Icons.airport_shuttle,
                            size: 32,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _transfer!.name,
                        style: AppTextStyles.subtitle1.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _transfer!.vehicle.fullName,
                        style: AppTextStyles.body2,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Provider: ${_transfer!.provider}',
                        style: AppTextStyles.body2.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          if (_transfer!.isOnSale) ...[
                            Text(
                              '${_transfer!.currency}${_transfer!.originalPrice?.toStringAsFixed(2)}',
                              style: AppTextStyles.body2.copyWith(
                                color: AppColors.textSecondary,
                                decoration: TextDecoration.lineThrough,
                              ),
                            ),
                            const SizedBox(width: 8),
                          ],
                          Text(
                            '${_transfer!.currency}${_transfer!.price.toStringAsFixed(2)}',
                            style: AppTextStyles.subtitle1.copyWith(
                              fontWeight: FontWeight.bold,
                              color: _transfer!.isOnSale
                                  ? AppColors.error
                                  : Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build the location section
  Widget _buildLocationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Pickup & Dropoff',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 16),
        LocationPicker(
          initialLocation: _pickupLocation,
          locationType: TransferLocationType.airport,
          availableLocations: const [], // This should be populated with real data
          onLocationSelected: (location) {
            setState(() {
              _pickupLocation = location;
            });
          },
          onAddNewLocation: () {
            // Navigate to map screen
          },
        ),
        const SizedBox(height: 16),
        LocationPicker(
          initialLocation: _dropoffLocation,
          locationType: TransferLocationType.hotel,
          availableLocations: const [], // This should be populated with real data
          onLocationSelected: (location) {
            setState(() {
              _dropoffLocation = location;
            });
          },
          onAddNewLocation: () {
            // Navigate to map screen
          },
        ),
        const SizedBox(height: 16),
        DateTimePickerField(
          label: 'Pickup Date & Time',
          selectedDateTime: _pickupDateTime,
          onDateTimeSelected: (dateTime) {
            setState(() {
              _pickupDateTime = dateTime;
            });
          },
          validator: (dateTime) {
            if (dateTime == null) {
              return 'Please select pickup date and time';
            }
            if (dateTime.isBefore(DateTime.now())) {
              return 'Pickup time must be in the future';
            }
            return null;
          },
        ),
      ],
    );
  }

  /// Build the passengers section
  Widget _buildPassengersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Passengers & Luggage',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Passengers',
                    style: AppTextStyles.subtitle2,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.remove_circle),
                        onPressed: _passengerCount > 1
                            ? () {
                                setState(() {
                                  _passengerCount--;
                                });
                              }
                            : null,
                        color: _passengerCount > 1
                            ? AppColors.primary
                            : AppColors.textSecondary,
                      ),
                      Expanded(
                        child: Text(
                          _passengerCount.toString(),
                          style: AppTextStyles.headline6,
                          textAlign: TextAlign.center,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.add_circle),
                        onPressed:
                            _passengerCount < _transfer!.passengerCapacity
                                ? () {
                                    setState(() {
                                      _passengerCount++;
                                    });
                                  }
                                : null,
                        color: _passengerCount < _transfer!.passengerCapacity
                            ? AppColors.primary
                            : AppColors.textSecondary,
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Max: ${_transfer!.passengerCapacity}',
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Luggage',
                    style: AppTextStyles.subtitle2,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.remove_circle),
                        onPressed: _luggageCount > 0
                            ? () {
                                setState(() {
                                  _luggageCount--;
                                });
                              }
                            : null,
                        color: _luggageCount > 0
                            ? AppColors.primary
                            : AppColors.textSecondary,
                      ),
                      Expanded(
                        child: Text(
                          _luggageCount.toString(),
                          style: AppTextStyles.headline6,
                          textAlign: TextAlign.center,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.add_circle),
                        onPressed: _luggageCount < _transfer!.luggageCapacity
                            ? () {
                                setState(() {
                                  _luggageCount++;
                                });
                              }
                            : null,
                        color: _luggageCount < _transfer!.luggageCapacity
                            ? AppColors.primary
                            : AppColors.textSecondary,
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Max: ${_transfer!.luggageCapacity}',
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build the flight info section
  Widget _buildFlightInfoSection() {
    if (!_transfer!.includesFlightTracking) {
      return const SizedBox();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Flight Information',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 8),
        Text(
          'If you\'re arriving by flight, please provide your flight details for tracking.',
          style: AppTextStyles.body2.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 16),
        if (_flightInfo == null)
          Row(
            children: [
              Expanded(
                child: CustomTextField(
                  controller: _flightInfoController,
                  label: 'Flight Number',
                  hint: 'e.g., AA123',
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: () {
                  // Show flight info form
                  _showFlightInfoForm();
                },
                child: const Text('Search'),
              ),
            ],
          )
        else
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.primary
                              .withAlpha(25), // Equivalent to withOpacity(0.1)
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.flight,
                          color: AppColors.primary,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${_flightInfo!.airlineName} ${_flightInfo!.flightNumber}',
                              style: AppTextStyles.subtitle1.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${_flightInfo!.departureAirportCode} → ${_flightInfo!.arrivalAirportCode}',
                              style: AppTextStyles.body2,
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.edit),
                        onPressed: () {
                          _showFlightInfoForm();
                        },
                        tooltip: 'Edit',
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  /// Build the contact section
  Widget _buildContactSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Contact Information',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _contactNameController,
          label: 'Full Name',
          hint: 'Enter your full name',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your name';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _contactPhoneController,
          label: 'Phone Number',
          hint: 'Enter your phone number',
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your phone number';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _contactEmailController,
          label: 'Email',
          hint: 'Enter your email address',
          keyboardType: TextInputType.emailAddress,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your email';
            }
            if (!value.contains('@') || !value.contains('.')) {
              return 'Please enter a valid email';
            }
            return null;
          },
        ),
      ],
    );
  }

  /// Build the special requests section
  Widget _buildSpecialRequestsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Special Requests (Optional)',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _specialRequestsController,
          label: 'Special Requests',
          hint: 'Enter any special requests or requirements',
          maxLines: 3,
        ),
      ],
    );
  }

  /// Build the submit button
  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _submitBooking,
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 16),
        ),
        child: const Text('Continue to Payment'),
      ),
    );
  }

  /// Show the flight info form
  Future<void> _showFlightInfoForm() async {
    try {
      await showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        builder: (context) {
          return Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
              top: 16,
              left: 16,
              right: 16,
            ),
            child: FlightInfoForm(
              initialFlightNumber: _flightInfoController.text,
              initialFlightDate: _pickupDateTime,
              onSubmit: (flightNumber, flightDate) {
                if (!mounted) return;

                try {
                  // Create a simple FlightInfo object
                  final flightInfo = FlightInfo(
                    flightNumber: flightNumber,
                    airlineCode: 'AA',
                    airlineName: 'American Airlines',
                    departureAirportCode: 'JFK',
                    arrivalAirportCode: 'LAX',
                    departureAirportName:
                        'John F. Kennedy International Airport',
                    arrivalAirportName: 'Los Angeles International Airport',
                    departureCity: 'New York',
                    arrivalCity: 'Los Angeles',
                    scheduledDepartureTime: flightDate,
                    scheduledArrival: flightDate.add(const Duration(hours: 6)),
                    status: FlightStatus.scheduled,
                    delayMinutes: null,
                  );

                  setState(() {
                    _flightInfo = flightInfo;
                    _flightInfoController.text = flightInfo.flightNumber;

                    // If pickup location is not set, set it to the arrival airport
                    _pickupLocation ??= models.TransferLocation(
                      id: flightInfo.arrivalAirportCode,
                      type: models.TransferLocationType.airport,
                      name: flightInfo.arrivalAirportName,
                      address: 'Airport',
                      city: flightInfo.arrivalCity,
                      country: '',
                      postalCode: '',
                      coordinates: const GeoLocation(latitude: 0, longitude: 0),
                    );

                    // If pickup date is not set, set it to the arrival time
                    _pickupDateTime ??= flightInfo.scheduledArrival;
                  });

                  Navigator.pop(context);
                } catch (e, stackTrace) {
                  _loggingService.error(
                    'TransferBookingScreen',
                    'Failed to process flight info',
                    e,
                    stackTrace,
                  );

                  if (!mounted) return;

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to process flight info: $e'),
                      backgroundColor: AppColors.error,
                    ),
                  );

                  Navigator.pop(context);
                }
              },
            ),
          );
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'TransferBookingScreen',
        'Failed to show flight info form',
        e,
        stackTrace,
      );

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to show flight info form: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  /// Submit the booking
  Future<void> _submitBooking() async {
    try {
      if (!_formKey.currentState!.validate()) {
        return;
      }

      if (_pickupLocation == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select a pickup location'),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }

      if (_dropoffLocation == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select a dropoff location'),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }

      if (_pickupDateTime == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select a pickup date and time'),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }

      // Create the booking
      final booking = TransferBooking(
        id: '',
        userId: '',
        transferId: _transfer!.id,
        transferService: _transfer,
        pickupLocation: _pickupLocation!,
        dropoffLocation: _dropoffLocation!,
        pickupDateTime: _pickupDateTime!,
        passengerCount: _passengerCount,
        luggageCount: _luggageCount,
        thisecialRequests: _specialRequestsController.text.isEmpty
            ? null
            : _specialRequestsController.text,
        contactName: _contactNameController.text,
        contactPhone: _contactPhoneController.text,
        contactEmail: _contactEmailController.text,
        flightInfo: _flightInfo != null
            ? '${_flightInfo!.flightNumber} (${_flightInfo!.departureAirportCode} → ${_flightInfo!.arrivalAirportCode})'
            : null,
        status: TransferBookingStatus.pending,
        totalPrice: _transfer!.price,
        currency: _transfer!.currency,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Navigate to booking details screen
      if (!mounted) return;

      await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TransferBookingDetailsScreen(
            booking: booking,
          ),
        ),
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'TransferBookingScreen',
        'Failed to submit booking',
        e,
        stackTrace,
      );

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to submit booking: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }
}
