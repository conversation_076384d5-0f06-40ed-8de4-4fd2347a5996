import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/transfer/transfers.dart'
    as models;
import 'package:culture_connect/models/travel/transfer/transfer_extensions.dart';
import 'package:culture_connect/providers/travel/transfer/transfer_provider.dart';
import 'package:culture_connect/screens/travel/transfer/transfer_booking_confirmation_screen.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';
import 'package:culture_connect/widgets/common/payment_method_selector.dart';
import 'package:provider/provider.dart';

// Type aliases to make the code more readable
typedef TransferBooking = models.TransferBooking;
typedef TransferBookingStatus = models.TransferBookingStatus;

/// A screen for displaying booking details and payment
class TransferBookingDetailsScreen extends StatefulWidget {
  /// The booking to display
  final TransferBooking booking;

  /// Creates a new transfer booking details screen
  const TransferBookingDetailsScreen({
    super.key,
    required this.booking,
  });

  @override
  State<TransferBookingDetailsScreen> createState() =>
      _TransferBookingDetailsScreenState();
}

class _TransferBookingDetailsScreenState
    extends State<TransferBookingDetailsScreen> {
  bool _isLoading = false;
  String? _selectedPaymentMethod;
  bool _acceptTerms = false;
  final LoggingService _loggingService = LoggingService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Booking Details'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildBookingSummary(),
                  const SizedBox(height: 24),
                  _buildPriceDetails(),
                  const SizedBox(height: 24),
                  _buildPaymentSection(),
                  const SizedBox(height: 24),
                  _buildTermsAndConditions(),
                  const SizedBox(height: 24),
                  _buildSubmitButton(),
                ],
              ),
            ),
    );
  }

  /// Build the booking summary
  Widget _buildBookingSummary() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Booking Summary',
              style: AppTextStyles.headline6,
            ),
            const SizedBox(height: 16),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    widget.booking.transferService?.imageUrl ?? '',
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 80,
                        height: 80,
                        color: AppColors.surface,
                        child: const Center(
                          child: Icon(
                            Icons.airport_shuttle,
                            size: 32,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.booking.transferService?.name ??
                            'Airport Transfer',
                        style: AppTextStyles.subtitle1.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.booking.transferService?.vehicle.fullName ??
                            'Vehicle',
                        style: AppTextStyles.body2,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Provider: ${widget.booking.transferService?.provider ?? 'Unknown'}',
                        style: AppTextStyles.body2.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            _buildInfoRow(
              icon: Icons.calendar_today,
              title: 'Pickup Date',
              value: widget.booking.formattedPickupDate,
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              icon: Icons.access_time,
              title: 'Pickup Time',
              value: widget.booking.formattedPickupTime,
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              icon: Icons.location_on,
              title: 'Pickup Location',
              value: widget.booking.pickupLocation.name,
              subtitle: widget.booking.pickupLocation.address,
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              icon: Icons.location_on,
              title: 'Dropoff Location',
              value: widget.booking.dropoffLocation.name,
              subtitle: widget.booking.dropoffLocation.address,
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              icon: Icons.people,
              title: 'Passengers',
              value: widget.booking.passengerCount.toString(),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              icon: Icons.luggage,
              title: 'Luggage',
              value: widget.booking.luggageCount.toString(),
            ),
            if (widget.booking.flightInfo != null) ...[
              const SizedBox(height: 12),
              _buildInfoRow(
                icon: Icons.flight,
                title: 'Flight',
                value: widget.booking.flightInfo!,
              ),
            ],
            if (widget.booking.specialRequests != null) ...[
              const SizedBox(height: 12),
              _buildInfoRow(
                icon: Icons.note,
                title: 'Special Requests',
                value: widget.booking.specialRequests!,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Build the price details
  Widget _buildPriceDetails() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Price Details',
              style: AppTextStyles.headline6,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Base Price',
                  style: AppTextStyles.body1,
                ),
                Text(
                  '${widget.booking.currency}${widget.booking.totalPrice.toStringAsFixed(2)}',
                  style: AppTextStyles.body1.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Service Fee',
                  style: AppTextStyles.body1,
                ),
                Text(
                  '${widget.booking.currency}0.00',
                  style: AppTextStyles.body1.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Tax',
                  style: AppTextStyles.body1,
                ),
                Text(
                  '${widget.booking.currency}0.00',
                  style: AppTextStyles.body1.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total',
                  style: AppTextStyles.subtitle1.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${widget.booking.currency}${widget.booking.totalPrice.toStringAsFixed(2)}',
                  style: AppTextStyles.subtitle1.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build the payment section
  Widget _buildPaymentSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Method',
              style: AppTextStyles.headline6,
            ),
            const SizedBox(height: 16),
            PaymentMethodSelector(
              onPaymentMethodSelected: (method) {
                setState(() {
                  _selectedPaymentMethod = method;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Build the terms and conditions
  Widget _buildTermsAndConditions() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Terms & Conditions',
              style: AppTextStyles.headline6,
            ),
            const SizedBox(height: 16),
            Text(
              'Cancellation Policy:',
              style: AppTextStyles.subtitle2.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.booking.transferService?.cancellationPolicy ??
                  'Free cancellation up to 24 hours before pickup.',
              style: AppTextStyles.body2,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Checkbox(
                  value: _acceptTerms,
                  onChanged: (value) {
                    setState(() {
                      _acceptTerms = value ?? false;
                    });
                  },
                ),
                Expanded(
                  child: Text(
                    'I agree to the terms and conditions, including the cancellation policy.',
                    style: AppTextStyles.body2,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build the submit button
  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _acceptTerms && _selectedPaymentMethod != null
            ? _confirmBooking
            : null,
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 16),
        ),
        child: const Text('Confirm & Pay'),
      ),
    );
  }

  /// Build an info row
  Widget _buildInfoRow({
    required IconData icon,
    required String title,
    required String value,
    String? subtitle,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primary
                .withAlpha(25), // Equivalent to withOpacity(0.1)
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: AppColors.primary,
            size: 16,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.body2.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: AppTextStyles.body1.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (subtitle != null) ...[
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: AppTextStyles.caption,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  /// Confirm the booking
  Future<void> _confirmBooking() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final transferProvider =
          Provider.of<TransferProvider>(context, listen: false);

      // Add payment method to booking
      final updatedBooking = TransferBooking(
        id: widget.booking.id,
        userId: widget.booking.userId,
        transferId: widget.booking.transferId,
        transferService: widget.booking.transferService,
        pickupLocation: widget.booking.pickupLocation,
        dropoffLocation: widget.booking.dropoffLocation,
        pickupDateTime: widget.booking.pickupDateTime,
        passengerCount: widget.booking.passengerCount,
        luggageCount: widget.booking.luggageCount,
        thisecialRequests: widget.booking.specialRequests,
        contactName: widget.booking.contactName,
        contactPhone: widget.booking.contactPhone,
        contactEmail: widget.booking.contactEmail,
        flightInfo: widget.booking.flightInfo,
        status: widget.booking.status,
        totalPrice: widget.booking.totalPrice,
        currency: widget.booking.currency,
        paymentMethod: _selectedPaymentMethod,
        createdAt: widget.booking.createdAt,
        updatedAt: DateTime.now(),
      );

      // Book the transfer
      final newBooking = await transferProvider.bookTransfer(updatedBooking);

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      if (newBooking != null) {
        if (!mounted) return;

        // Navigate to confirmation screen
        await Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => TransferBookingConfirmationScreen(
              booking: newBooking,
            ),
          ),
        );
      } else {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to book transfer'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'TransferBookingDetailsScreen',
        'Failed to confirm booking',
        e,
        stackTrace,
      );

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }
}
