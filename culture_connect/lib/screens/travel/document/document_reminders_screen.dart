import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/document/document_reminder.dart';
import 'package:culture_connect/providers/travel/document/document_providers.dart';
import 'package:culture_connect/screens/travel/document/document_details_screen.dart';
import 'package:culture_connect/theme/app_colors.dart';

import 'package:culture_connect/widgets/common/empty_state.dart';
import 'package:culture_connect/widgets/common/error_state.dart';
import 'package:culture_connect/widgets/travel/document/document_widgets.dart';
import 'package:provider/provider.dart';

/// A screen for displaying document reminders
class DocumentRemindersScreen extends StatefulWidget {
  /// Creates a new document reminders screen
  const DocumentRemindersScreen({super.key});

  @override
  State<DocumentRemindersScreen> createState() =>
      _DocumentRemindersScreenState();
}

class _DocumentRemindersScreenState extends State<DocumentRemindersScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Load data from providers
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final reminderProvider =
          Provider.of<DocumentReminderProvider>(context, listen: false);
      await reminderProvider.loadReminders();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Document Reminders'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Due'),
            Tab(text: 'Upcoming'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildDueRemindersTab(),
                _buildUpcomingRemindersTab(),
              ],
            ),
    );
  }

  /// Build the due reminders tab
  Widget _buildDueRemindersTab() {
    return Consumer<DocumentReminderProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return ErrorState(
            message: provider.error!,
            onRetry: () => provider.loadReminders(),
          );
        }

        final dueReminders = provider.dueReminders;

        if (dueReminders.isEmpty) {
          return const EmptyState(
            icon: Icons.check_circle,
            title: 'No Due Reminders',
            message: 'You don\'t have any due reminders.',
          );
        }

        return RefreshIndicator(
          onRefresh: () => provider.loadReminders(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: dueReminders.length,
            itemBuilder: (context, index) {
              final reminder = dueReminders[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: DocumentReminderCard(
                  thiseminder: reminder,
                  onTap: () {
                    _navigateToDocument(reminder);
                  },
                  onMarkAsRead: () {
                    _markReminderAsRead(reminder);
                  },
                  onDismiss: () {
                    _dismissReminder(reminder);
                  },
                ),
              );
            },
          ),
        );
      },
    );
  }

  /// Build the upcoming reminders tab
  Widget _buildUpcomingRemindersTab() {
    return Consumer<DocumentReminderProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return ErrorState(
            message: provider.error!,
            onRetry: () => provider.loadReminders(),
          );
        }

        final upcomingReminders = provider.upcomingReminders;

        if (upcomingReminders.isEmpty) {
          return const EmptyState(
            icon: Icons.event,
            title: 'No Upcoming Reminders',
            message: 'You don\'t have any upcoming reminders.',
          );
        }

        return RefreshIndicator(
          onRefresh: () => provider.loadReminders(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: upcomingReminders.length,
            itemBuilder: (context, index) {
              final reminder = upcomingReminders[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: DocumentReminderCard(
                  thiseminder: reminder,
                  onTap: () {
                    _navigateToDocument(reminder);
                  },
                  onMarkAsRead: null, // Can't mark upcoming reminders as read
                  onDismiss: () {
                    _dismissReminder(reminder);
                  },
                ),
              );
            },
          ),
        );
      },
    );
  }

  /// Navigate to the document details
  void _navigateToDocument(DocumentReminder reminder) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DocumentDetailsScreen(
          documentId: reminder.documentId,
        ),
      ),
    );
  }

  /// Mark a reminder as read
  Future<void> _markReminderAsRead(DocumentReminder reminder) async {
    try {
      final reminderProvider =
          Provider.of<DocumentReminderProvider>(context, listen: false);
      await reminderProvider.markReminderAsRead(reminder.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Reminder marked as read'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  /// Dismiss a reminder
  Future<void> _dismissReminder(DocumentReminder reminder) async {
    try {
      final reminderProvider =
          Provider.of<DocumentReminderProvider>(context, listen: false);
      await reminderProvider.dismissReminder(reminder.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Reminder dismissed'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }
}
