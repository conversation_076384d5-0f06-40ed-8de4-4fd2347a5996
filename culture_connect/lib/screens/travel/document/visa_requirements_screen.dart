import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/document/visa_requirement.dart';
import 'package:culture_connect/providers/travel/document/document_providers.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/widgets/travel/document/document_widgets.dart';
import 'package:culture_connect/widgets/common/empty_state.dart';
import 'package:culture_connect/widgets/common/error_state.dart';
import 'package:culture_connect/widgets/common/loading_view.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:provider/provider.dart';

/// A screen for displaying visa requirements
class VisaRequirementsScreen extends StatefulWidget {
  /// The country of origin
  final String countryFrom;

  /// Creates a new visa requirements screen
  const VisaRequirementsScreen({
    super.key,
    required this.countryFrom,
  });

  @override
  State<VisaRequirementsScreen> createState() => _VisaRequirementsScreenState();
}

class _VisaRequirementsScreenState extends State<VisaRequirementsScreen> {
  String _selectedCountry = 'Japan';
  bool _isLoading = true;
  List<VisaRequirement> _requirements = [];
  String? _error;
  final LoggingService _loggingService = LoggingService();

  @override
  void initState() {
    super.initState();
    _loadRequirements();
  }

  /// Load visa requirements
  Future<void> _loadRequirements() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final provider =
          Provider.of<VisaRequirementProvider>(context, listen: false);
      await provider.initialize();

      // Get all requirements
      final requirements = provider.requirements;

      // Filter for the country
      final countryRequirements = requirements
          .where((req) => req.countryFrom == widget.countryFrom)
          .toList();

      setState(() {
        _requirements = countryRequirements;
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaRequirementsScreen',
        'Error loading visa requirements',
        e,
        stackTrace,
      );

      setState(() {
        _error = 'Failed to load visa requirements: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Visa Requirements - ${widget.countryFrom}'),
      ),
      body: Column(
        children: [
          // Country selector
          Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Select Destination Country',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                // Using a simple dropdown for country selection
                DropdownButton<String>(
                  value: _selectedCountry,
                  isExpanded: true,
                  items: const [
                    DropdownMenuItem(value: 'Japan', child: Text('Japan')),
                    DropdownMenuItem(value: 'China', child: Text('China')),
                    DropdownMenuItem(
                        value: 'South Korea', child: Text('South Korea')),
                    DropdownMenuItem(
                        value: 'Thailand', child: Text('Thailand')),
                    DropdownMenuItem(value: 'Vietnam', child: Text('Vietnam')),
                    DropdownMenuItem(
                        value: 'Singapore', child: Text('Singapore')),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedCountry = value;
                      });
                    }
                  },
                ),
              ],
            ),
          ),

          // Requirements list
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  /// Build the content based on state
  Widget _buildContent() {
    if (_isLoading) {
      return const LoadingView(message: 'Loading visa requirements...');
    }

    if (_error != null) {
      return ErrorState(
        message: _error!,
        onRetry: _loadRequirements,
      );
    }

    if (_requirements.isEmpty) {
      return EmptyState(
        icon: Icons.flight,
        title: 'No Visa Requirements',
        message:
            'We couldn\'t find any visa requirements for the selected country. Please try another country or check official sources.',
        actionText: 'Retry',
        onAction: _loadRequirements,
      );
    }

    // Filter requirements for the selected country
    final filteredRequirements = _requirements
        .where((req) => req.countryTo == _selectedCountry)
        .toList();

    if (filteredRequirements.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search,
              size: 48,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16),
            Text(
              'No visa requirements found for $_selectedCountry',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Please select another country',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: filteredRequirements.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: EdgeInsets.only(bottom: 16),
          child: VisaRequirementCard(
            thisequirement: filteredRequirements[index],
            onTap: () =>
                _showRequirementDetails(context, filteredRequirements[index]),
            onApply: filteredRequirements[index].applicationUrl != null
                ? () =>
                    _openApplicationUrl(context, filteredRequirements[index])
                : null,
          ),
        );
      },
    );
  }

  /// Show requirement details
  void _showRequirementDetails(
      BuildContext context, VisaRequirement requirement) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          padding: EdgeInsets.all(16),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Row(
                  children: [
                    Icon(
                      requirement.requirementType.icon,
                      color: requirement.requirementType.color,
                      size: 24,
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            requirement.requirementType.displayName,
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: requirement.requirementType.color,
                            ),
                          ),
                          Text(
                            '${requirement.countryFrom} → ${requirement.countryTo}',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 16),

                // Description
                Text(
                  'Description',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  requirement.description,
                  style: TextStyle(
                    fontSize: 14,
                  ),
                ),

                SizedBox(height: 16),

                // Details
                Text(
                  'Details',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                if (requirement.maxStayDuration != null)
                  _buildDetailRow(
                      'Maximum Stay', '${requirement.maxStayDuration} days'),
                if (requirement.processingTime != null)
                  _buildDetailRow(
                      'Processing Time', '${requirement.processingTime} days'),
                if (requirement.visaFee != null)
                  _buildDetailRow('Visa Fee',
                      '\$${requirement.visaFee!.toStringAsFixed(2)} USD'),

                SizedBox(height: 16),

                // Required documents
                Text(
                  'Required Documents',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                ...requirement.requiredDocuments.map((document) => Padding(
                      padding: EdgeInsets.only(bottom: 4),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Icon(
                            Icons.check_circle,
                            size: 16,
                            color: Colors.green,
                          ),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              document,
                              style: TextStyle(
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    )),

                if (requirement.notes != null) ...[
                  SizedBox(height: 16),

                  // Notes
                  Text(
                    'Notes',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    requirement.notes!,
                    style: TextStyle(
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],

                SizedBox(height: 24),

                // Apply button
                if (requirement.applicationUrl != null)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _openApplicationUrl(context, requirement);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Apply for Visa'),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Open the application URL
  void _openApplicationUrl(BuildContext context, VisaRequirement requirement) {
    // In a real app, this would open the application URL
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening visa application website...'),
      ),
    );
  }

  /// Build a detail row with label and value
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
