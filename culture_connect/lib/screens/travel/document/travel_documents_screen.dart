import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/document/travel_documents.dart';
import 'package:culture_connect/providers/travel/document/document_providers.dart';
import 'package:culture_connect/screens/travel/document/document_details_screen.dart';
import 'package:culture_connect/screens/travel/document/document_upload_screen.dart';
import 'package:culture_connect/screens/travel/document/document_reminders_screen.dart';
import 'package:culture_connect/screens/travel/document/visa_requirements_screen.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';
import 'package:culture_connect/widgets/common/empty_state.dart';
import 'package:culture_connect/widgets/common/error_state.dart';
import 'package:culture_connect/widgets/travel/document/document_widgets.dart';
import 'package:provider/provider.dart';

/// A screen for displaying travel documents
class TravelDocumentsScreen extends StatefulWidget {
  /// Creates a new travel documents screen
  const TravelDocumentsScreen({super.key});

  @override
  State<TravelDocumentsScreen> createState() => _TravelDocumentsScreenState();
}

class _TravelDocumentsScreenState extends State<TravelDocumentsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  final LoggingService _loggingService = LoggingService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Load data from providers
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final documentProvider =
          Provider.of<TravelDocumentProvider>(context, listen: false);
      final reminderProvider =
          Provider.of<DocumentReminderProvider>(context, listen: false);

      await Future.wait([
        documentProvider.initialize(),
        reminderProvider.initialize(),
      ]);

      setState(() {
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      _loggingService.error(
        'TravelDocumentsScreen',
        'Error loading data',
        e,
        stackTrace,
      );
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Travel Documents'),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const DocumentRemindersScreen(),
                ),
              );
            },
            tooltip: 'Reminders',
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // Default to user's country or United States
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const VisaRequirementsScreen(
                    countryFrom: 'United States',
                  ),
                ),
              );
            },
            tooltip: 'Visa Requirements',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Passports'),
            Tab(text: 'Visas'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildPassportsTab(),
                _buildVisasTab(),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showAddDocumentDialog();
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  /// Build the passports tab
  Widget _buildPassportsTab() {
    return Consumer<TravelDocumentProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return ErrorState(
            message: provider.error!,
            onRetry: () => provider.loadDocuments(),
          );
        }

        final passports = provider.passports;

        if (passports.isEmpty) {
          return EmptyState(
            icon: Icons.book,
            title: 'No Passports',
            message: 'You haven\'t added any passports yet.',
            actionText: 'Add Passport',
            onAction: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const DocumentUploadScreen(
                    documentType: TravelDocumentType.passport,
                  ),
                ),
              );
            },
          );
        }

        return RefreshIndicator(
          onRefresh: () => provider.loadDocuments(),
          child: ListView.builder(
            padding: EdgeInsets.all(16),
            itemCount: passports.length,
            itemBuilder: (context, index) {
              final passport = passports[index];
              return Padding(
                padding: EdgeInsets.only(bottom: 16),
                child: DocumentCard(
                  document: passport,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => DocumentDetailsScreen(
                          documentId: passport.id,
                        ),
                      ),
                    );
                  },
                  onEdit: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => DocumentUploadScreen(
                          documentType: TravelDocumentType.passport,
                          document: passport,
                        ),
                      ),
                    );
                  },
                  onDelete: () {
                    _deleteDocument(passport);
                  },
                ),
              );
            },
          ),
        );
      },
    );
  }

  /// Build the visas tab
  Widget _buildVisasTab() {
    return Consumer<TravelDocumentProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return ErrorState(
            message: provider.error!,
            onRetry: () => provider.loadDocuments(),
          );
        }

        final visas = provider.visas;

        if (visas.isEmpty) {
          return EmptyState(
            icon: Icons.verified,
            title: 'No Visas',
            message: 'You haven\'t added any visas yet.',
            actionText: 'Add Visa',
            onAction: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const DocumentUploadScreen(
                    documentType: TravelDocumentType.visa,
                  ),
                ),
              );
            },
          );
        }

        return RefreshIndicator(
          onRefresh: () => provider.loadDocuments(),
          child: ListView.builder(
            padding: EdgeInsets.all(16),
            itemCount: visas.length,
            itemBuilder: (context, index) {
              final visa = visas[index];
              return Padding(
                padding: EdgeInsets.only(bottom: 16),
                child: DocumentCard(
                  document: visa,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => DocumentDetailsScreen(
                          documentId: visa.id,
                        ),
                      ),
                    );
                  },
                  onEdit: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => DocumentUploadScreen(
                          documentType: TravelDocumentType.visa,
                          document: visa,
                        ),
                      ),
                    );
                  },
                  onDelete: () {
                    _deleteDocument(visa);
                  },
                ),
              );
            },
          ),
        );
      },
    );
  }

  /// Show a dialog to add a document
  void _showAddDocumentDialog() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Add Travel Document',
                style: AppTextStyles.headline6,
              ),
              const SizedBox(height: 16),
              ListTile(
                leading: Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: TravelDocumentType.passport.color
                        .withAlpha(51), // 0.2 * 255 = 51
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    TravelDocumentType.passport.icon,
                    color: TravelDocumentType.passport.color,
                  ),
                ),
                title: const Text('Passport'),
                subtitle: const Text('Add a passport document'),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const DocumentUploadScreen(
                        documentType: TravelDocumentType.passport,
                      ),
                    ),
                  );
                },
              ),
              ListTile(
                leading: Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: TravelDocumentType.visa.color
                        .withAlpha(51), // 0.2 * 255 = 51
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    TravelDocumentType.visa.icon,
                    color: TravelDocumentType.visa.color,
                  ),
                ),
                title: const Text('Visa'),
                subtitle: const Text('Add a visa document'),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const DocumentUploadScreen(
                        documentType: TravelDocumentType.visa,
                      ),
                    ),
                  );
                },
              ),
              ListTile(
                leading: Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: TravelDocumentType.idCard.color
                        .withAlpha(51), // 0.2 * 255 = 51
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    TravelDocumentType.idCard.icon,
                    color: TravelDocumentType.idCard.color,
                  ),
                ),
                title: const Text('ID Card'),
                subtitle: const Text('Add an ID card document'),
                onTap: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('ID Card support coming soon'),
                    ),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// Delete a document
  Future<void> _deleteDocument(TravelDocument document) async {
    final documentProvider =
        Provider.of<TravelDocumentProvider>(context, listen: false);
    final reminderProvider =
        Provider.of<DocumentReminderProvider>(context, listen: false);

    try {
      // Delete the document
      final success = await documentProvider.deleteDocument(document.id);

      if (success) {
        // Delete associated reminders
        await reminderProvider.deleteDocumentReminders(document.id);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${document.name} deleted'),
              backgroundColor: AppColors.success,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to delete ${document.name}'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'TravelDocumentsScreen',
        'Error deleting document',
        e,
        stackTrace,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }
}
