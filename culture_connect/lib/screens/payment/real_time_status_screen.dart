import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:async';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/payment/websocket_status_service.dart';
import 'package:culture_connect/services/payment/payment_config_service.dart';
import 'package:culture_connect/services/payment/payment_auth_service.dart';
import 'package:culture_connect/screens/payment/enhanced_success_screen.dart';

import 'package:culture_connect/services/logging_service.dart'
    hide loggingServiceProvider;

import 'package:culture_connect/providers/mascot_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/providers/services_providers.dart';
import 'package:culture_connect/providers/auth_provider.dart';

/// Real-time payment status monitoring screen with WebSocket integration
/// Provides live updates on payment progress with beautiful UI animations
/// Performance target: Real-time updates with <100ms latency
class RealTimeStatusScreen extends ConsumerStatefulWidget {
  final String transactionReference;
  final PaymentMethodType paymentMethod;
  final PaymentProvider provider;
  final double amount;
  final Booking booking;

  const RealTimeStatusScreen({
    super.key,
    required this.transactionReference,
    required this.paymentMethod,
    required this.provider,
    required this.amount,
    required this.booking,
  });

  @override
  ConsumerState<RealTimeStatusScreen> createState() =>
      _RealTimeStatusScreenState();
}

class _RealTimeStatusScreenState extends ConsumerState<RealTimeStatusScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _progressController;
  late AnimationController _statusController;

  // WebSocket and status tracking
  late WebSocketStatusService _webSocketService;
  StreamSubscription<PaymentStatusUpdate>? _statusSubscription;
  StreamSubscription<WebSocketConnectionState>? _connectionSubscription;

  // Current state
  PaymentStatus _currentStatus = PaymentStatus.pending;
  WebSocketConnectionState _connectionState =
      WebSocketConnectionState.disconnected;
  String? _statusMessage;
  String? _errorMessage;
  DateTime? _lastUpdateTime;
  bool _isTimeout = false;

  // Services
  late LoggingService _loggingService;
  late PaymentConfigService _configService;
  late PaymentAuthService _authService;

  // Timeout handling
  Timer? _timeoutTimer;
  static const Duration _paymentTimeout = Duration(minutes: 10);

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeServices();
    _startStatusMonitoring();
    _startTimeoutTimer();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _progressController.dispose();
    _statusController.dispose();
    _statusSubscription?.cancel();
    _connectionSubscription?.cancel();
    _timeoutTimer?.cancel();
    _webSocketService.dispose();
    super.dispose();
  }

  /// Initialize animation controllers
  void _initializeControllers() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    ).epeat();

    _progressController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );

    _statusController = AnimationController(
      duration: AppTheme.shortAnimation,
      vsync: this,
    );
  }

  /// Initialize payment services
  void _initializeServices() {
    _loggingService = refead(loggingServiceProvider);
    _configService = PaymentConfigService(loggingService: _loggingService);
    _authService = PaymentAuthService(
      authService: refead(authServiceProvider),
      loggingService: _loggingService,
    );

    _webSocketService = WebSocketStatusService(
      loggingService: _loggingService,
      configService: _configService,
      authService: _authService,
    );
  }

  /// Start real-time status monitoring
  /// TODO: Backend Integration - WebSocket Connection
  /// Endpoint: wss://api.cultureconnect.com/ws/payments/status
  /// Authentication: Bearer JWT token in query params
  /// Message Format: JSON with type, payload, timestamp
  /// Connection Management: Auto-reconnect with exponential backoff
  Future<void> _startStatusMonitoring() async {
    try {
      // Initialize WebSocket service
      await _webSocketService.initialize();

      // Setup connection state monitoring
      _connectionSubscription = _webSocketService.connectionState.listen(
        _handleConnectionStateChange,
        onError: _handleConnectionError,
      );

      // Setup status update monitoring
      _statusSubscription = _webSocketService.statusUpdates.listen(
        _handleStatusUpdate,
        onError: _handleStatusError,
      );

      // Start monitoring for this transaction
      await _webSocketService.startMonitoring(widget.transactionReference);

      _loggingService.info(
        'RealTimeStatusScreen',
        'Started real-time status monitoring',
        {'transaction_reference': widget.transactionReference},
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'RealTimeStatusScreen',
        'Failed to start status monitoring',
        {'error': e.toString()},
        stackTrace,
      );

      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to connect to payment monitoring service';
        });
      }
    }
  }

  /// Handle WebSocket connection state changes
  void _handleConnectionStateChange(WebSocketConnectionState state) {
    if (!mounted) return;

    setState(() {
      _connectionState = state;
    });

    // Update mascot expression based on connection state
    final mascotActions = refead(mascotActionsProvider);
    switch (state) {
      case WebSocketConnectionState.connecting:
        mascotActions.showLoading(message: 'Connecting to payment service...');
        break;
      case WebSocketConnectionState.connected:
        // Don't change mascot state for successful connection
        break;
      case WebSocketConnectionState.disconnected:
      case WebSocketConnectionState.error:
        mascotActions.showError(
          errorMessage: 'Connection lost',
          errorType: 'connection_error',
        );
        break;
    }

    _loggingService.info(
      'RealTimeStatusScreen',
      'Connection state changed',
      {'state': state.name},
    );
  }

  /// Handle payment status updates from WebSocket
  void _handleStatusUpdate(PaymentStatusUpdate update) {
    if (!mounted) return;

    setState(() {
      _currentStatus = update.status;
      _lastUpdateTime = update.timestamp;
      _statusMessage = _getStatusMessage(update.status);
      _errorMessage = null;
    });

    // Trigger status animation
    _statusController.forward().then((_) => _statusControllereset());

    // Update mascot expression based on status
    final mascotActions = refead(mascotActionsProvider);

    switch (update.status) {
      case PaymentStatus.processing:
        mascotActions.showLoading(message: 'Processing payment...');
        _progressController.animateTo(0.7);
        break;
      case PaymentStatus.successful:
        mascotActions.celebrateBooking(
          bookingType: 'Payment',
          bookingId: widget.transactionReference,
        );
        _progressController.animateTo(1.0);
        HapticFeedbackeavyImpact();
        _navigateToSuccess();
        break;
      case PaymentStatus.failed:
      case PaymentStatus.cancelled:
        mascotActions.showError(
          errorMessage: 'Payment ${update.status.name}',
          errorType: 'payment_failed',
        );
        HapticFeedbackeavyImpact();
        _handlePaymentFailure(update.status);
        break;
      case PaymentStatus.pending:
        mascotActions.showLoading(message: 'Initializing payment...');
        _progressController.animateTo(0.3);
        break;
      case PaymentStatus.expired:
        mascotActions.showError(
          errorMessage: 'Payment session expired',
          errorType: 'payment_expired',
        );
        _handlePaymentTimeout();
        break;
    }

    // TODO: Backend Integration - Analytics API
    // Track payment status progression
    // refead(analyticsServiceProvider).logEvent(
    //   'payment_status_update',
    //   parameters: {
    //     'transaction_reference': update.transactionReference,
    //     'status': update.status.name,
    //     'provider': widget.provider.name,
    //     'method': widget.paymentMethod.name,
    //   },
    // );

    _loggingService.info(
      'RealTimeStatusScreen',
      'Payment status updated',
      {
        'transaction_reference': update.transactionReference,
        'status': update.status.name,
        'timestamp': update.timestamp.toIso8601String(),
      },
    );
  }

  /// Handle connection errors
  void _handleConnectionError(dynamic error) {
    _loggingService.error(
      'RealTimeStatusScreen',
      'WebSocket connection error',
      {'error': error.toString()},
    );

    if (mounted) {
      setState(() {
        _errorMessage = 'Connection lost. Attempting to reconnect...';
      });
    }
  }

  /// Handle status update errors
  void _handleStatusError(dynamic error) {
    _loggingService.error(
      'RealTimeStatusScreen',
      'Status update error',
      {'error': error.toString()},
    );
  }

  /// Start payment timeout timer
  void _startTimeoutTimer() {
    _timeoutTimer = Timer(_paymentTimeout, () {
      if (mounted && _currentStatus == PaymentStatus.pending) {
        _handlePaymentTimeout();
      }
    });
  }

  /// Handle payment timeout
  void _handlePaymentTimeout() {
    if (!mounted) return;

    setState(() {
      _isTimeout = true;
      _errorMessage = 'Payment timed out. Please try again.';
    });

    HapticFeedbackeavyImpact();
    refead(mascotActionsProvider).showError(
          errorMessage: 'Payment timed out',
          errorType: 'payment_timeout',
        );
  }

  /// Handle payment failure
  void _handlePaymentFailure(PaymentStatus status) {
    if (!mounted) return;

    setState(() {
      _errorMessage = _getErrorMessage(status);
    });
  }

  /// Navigate to success screen
  void _navigateToSuccess() {
    // Small delay for celebration animation
    Timer(const Duration(milliseconds: 1000), () {
      if (mounted) {
        // Navigate to enhanced success screen
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => EnhancedSuccessScreen(
              transactionReference: widget.transactionReference,
              amount: widget.amount,
              paymentMethod: widget.paymentMethod,
              provider: widget.provider,
              booking: widget.booking,
            ),
          ),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Payment Status',
          style: theme.textThemeeadlineSmall?.copyWith(
            fontWeight: FontWeight600,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: _currentStatus == PaymentStatus.successful
            ? null
            : IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => _showCancelDialog(),
              ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Connection status indicator
            _buildConnectionIndicator(theme),

            // Main status content
            Expanded(
              child: _buildStatusContent(theme),
            ),

            // Action buttons
            if (_errorMessage != null || _isTimeout) _buildActionButtons(theme),
          ],
        ),
      ),
    );
  }

  /// Build connection status indicator
  Widget _buildConnectionIndicator(ThemeData theme) {
    Color indicatorColor;
    String statusText;

    switch (_connectionState) {
      case WebSocketConnectionState.connected:
        indicatorColor = Colors.green;
        statusText = 'Connected';
        break;
      case WebSocketConnectionState.connecting:
        indicatorColor = Colors.orange;
        statusText = 'Connecting...';
        break;
      case WebSocketConnectionState.disconnected:
      case WebSocketConnectionState.error:
        indicatorColor = Colorsed;
        statusText = 'Disconnected';
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppThemeacingMedium,
        vertical: AppThemeacingSmall,
      ),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: indicatorColor,
              shape: BoxShape.circle,
            ),
          ).animate(onPlay: (controller) => controllerepeat()).scale(
                begin: const Offset(1.0, 1.0),
                end: const Offset(1.2, 1.2),
                duration: const Duration(milliseconds: 1000),
              ),
          const SizedBox(width: AppThemeacingSmall),
          Text(
            statusText,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceithAlpha(179),
            ),
          ),
          const Spacer(),
          if (_lastUpdateTime != null)
            Text(
              'Last update: ${_formatTime(_lastUpdateTime!)}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceithAlpha(128),
              ),
            ),
        ],
      ),
    );
  }

  /// Build main status content
  Widget _buildStatusContent(ThemeData theme) {
    return Padding(
      padding: EdgeInsets.all(AppThemeacingLarge),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Status icon with animation
          _buildStatusIcon(theme)
              .animate(controller: _statusController)
              .scale(begin: const Offset(1.0, 1.0), end: const Offset(1.1, 1.1))
              .then()
              .scale(
                  begin: const Offset(1.1, 1.1), end: const Offset(1.0, 1.0)),

          const SizedBox(height: AppThemeacingLarge),

          // Status message
          Text(
            _statusMessage ?? _getStatusMessage(_currentStatus),
            style: theme.textThemeeadlineSmall?.copyWith(
              fontWeight: FontWeight600,
              color: _getStatusColor(theme),
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppThemeacingMedium),

          // Progress indicator
          if (_currentStatus != PaymentStatus.successful &&
              _currentStatus != PaymentStatus.failed &&
              _currentStatus != PaymentStatus.cancelled)
            _buildProgressIndicator(theme),

          const SizedBox(height: AppThemeacingMedium),

          // Transaction details
          _buildTransactionDetails(theme),

          // Error message
          if (_errorMessage != null)
            Padding(
              padding: EdgeInsets.only(top: AppThemeacingMedium),
              child: Container(
                padding: EdgeInsets.all(AppThemeacingMedium),
                decoration: BoxDecoration(
                  color: theme.colorScheme.errorContainerithAlpha(51),
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusMedium),
                  border: Border.all(
                    color: theme.colorScheme.errorithAlpha(77),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: theme.colorScheme.error,
                      size: 20,
                    ),
                    const SizedBox(width: AppThemeacingSmall),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.error,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Build status icon with appropriate animation
  Widget _buildStatusIcon(ThemeData theme) {
    IconData iconData;
    Color iconColor;

    switch (_currentStatus) {
      case PaymentStatus.pending:
        iconData = Icons.schedule;
        iconColor = theme.colorScheme.primary;
        break;
      case PaymentStatus.processing:
        iconData = Icons.sync;
        iconColor = theme.colorScheme.primary;
        break;
      case PaymentStatus.successful:
        iconData = Icons.check_circle;
        iconColor = Colors.green;
        break;
      case PaymentStatus.failed:
      case PaymentStatus.cancelled:
      case PaymentStatus.expired:
        iconData = Icons.error;
        iconColor = theme.colorScheme.error;
        break;
    }

    Widget icon = Icon(
      iconData,
      size: 80,
      color: iconColor,
    );

    // Add rotation animation for processing status
    if (_currentStatus == PaymentStatus.processing) {
      icon = icon
          .animate(onPlay: (controller) => controllerepeat())
          otate(duration: const Duration(seconds: 2));
    }

    // Add pulse animation for pending status
    if (_currentStatus == PaymentStatus.pending) {
      icon = icon
          .animate(controller: _pulseController)
          .scale(
            begin: const Offset(1.0, 1.0),
            end: const Offset(1.1, 1.1),
            duration: const Duration(milliseconds: 750),
          )
          .then()
          .scale(
            begin: const Offset(1.1, 1.1),
            end: const Offset(1.0, 1.0),
            duration: const Duration(milliseconds: 750),
          );
    }

    return icon;
  }

  /// Build progress indicator
  Widget _buildProgressIndicator(ThemeData theme) {
    return Column(
      children: [
        SizedBox(
          width: 200,
          child: AnimatedBuilder(
            animation: _progressController,
            builder: (context, child) {
              return LinearProgressIndicator(
                value: _progressController.value,
                backgroundColor: theme.colorScheme.surfaceContainerHighest,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.primary,
                ),
              );
            },
          ),
        ),
        const SizedBox(height: AppThemeacingSmall),
        Text(
          '${(_progressController.value * 100).toInt()}% Complete',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceithAlpha(179),
          ),
        ),
      ],
    );
  }

  /// Build transaction details
  Widget _buildTransactionDetails(ThemeData theme) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        side: BorderSide(
          color: theme.colorScheme.outlineithAlpha(51),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(AppThemeacingMedium),
        child: Column(
          children: [
            _buildDetailRow(
                theme, 'Transaction ID', widget.transactionReference),
            const SizedBox(height: AppThemeacingSmall),
            _buildDetailRow(
                theme, 'Amount', '\$${widget.amount.toStringAsFixed(2)}'),
            const SizedBox(height: AppThemeacingSmall),
            _buildDetailRow(theme, 'Payment Method',
                _getPaymentMethodName(widget.paymentMethod)),
            const SizedBox(height: AppThemeacingSmall),
            _buildDetailRow(
                theme, 'Provider', widget.provider.name.toUpperCase()),
          ],
        ),
      ),
    );
  }

  /// Build detail row
  Widget _buildDetailRow(ThemeData theme, String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignmentaceBetween,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceithAlpha(179),
          ),
        ),
        Text(
          value,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight500,
          ),
        ),
      ],
    );
  }

  /// Build action buttons
  Widget _buildActionButtons(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(AppThemeacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outlineithAlpha(51),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel Payment'),
            ),
          ),
          const SizedBox(width: AppThemeacingMedium),
          Expanded(
            child: FilledButton(
              onPressed: _retryPayment,
              child: const Text('Retry Payment'),
            ),
          ),
        ],
      ),
    );
  }

  /// Show cancel confirmation dialog
  void _showCancelDialog() {
    showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Payment'),
        content: const Text(
          'Are you sure you want to cancel this payment? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Continue Payment'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop(true);
              Navigator.of(context).pop(false);
            },
            child: const Text('Cancel Payment'),
          ),
        ],
      ),
    );
  }

  /// Retry payment
  void _retryPayment() {
    Navigator.of(context).pop(false);
  }

  /// Get status message for current payment status
  String _getStatusMessage(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.pending:
        return 'Initializing payment...';
      case PaymentStatus.processing:
        return 'Processing your payment...';
      case PaymentStatus.successful:
        return 'Payment successful!';
      case PaymentStatus.failed:
        return 'Payment failed';
      case PaymentStatus.cancelled:
        return 'Payment cancelled';
      case PaymentStatus.expired:
        return 'Payment expired';
    }
  }

  /// Get status color for current payment status
  Color _getStatusColor(ThemeData theme) {
    switch (_currentStatus) {
      case PaymentStatus.successful:
        return Colors.green;
      case PaymentStatus.failed:
      case PaymentStatus.cancelled:
      case PaymentStatus.expired:
        return theme.colorScheme.error;
      default:
        return theme.colorScheme.onSurface;
    }
  }

  /// Get error message for payment status
  String _getErrorMessage(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.failed:
        return 'Payment processing failed. Please check your payment details and try again.';
      case PaymentStatus.cancelled:
        return 'Payment was cancelled. You can retry or choose a different payment method.';
      case PaymentStatus.expired:
        return 'Payment session expired. Please start a new payment.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  /// Get payment method display name
  String _getPaymentMethodName(PaymentMethodType method) {
    switch (method) {
      case PaymentMethodType.card:
        return 'Credit/Debit Card';
      case PaymentMethodType.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethodType.ussd:
        return 'USSD';
      case PaymentMethodType.mobileMoney:
        return 'Mobile Money';
      case PaymentMethodType.crypto:
        return 'Cryptocurrency';
    }
  }

  /// Format time for display
  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inSeconds < 60) {
      return '${difference.inSeconds}s ago';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else {
      return '${timeour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    }
  }
}
