import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:image_picker/image_picker.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/chat_provider.dart';
import 'package:culture_connect/screens/messaging/enhanced_forward_message_screen.dart';
import 'package:culture_connect/services/offline_message_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/messaging/message_report_dialog.dart';
import 'package:culture_connect/widgets/messaging/message_reactions.dart';
import 'package:culture_connect/widgets/messaging/message_translation_settings.dart';
import 'package:culture_connect/widgets/messaging/message_translation_toggle.dart';
import 'package:culture_connect/widgets/messaging/translated_message_bubble.dart';
import 'package:culture_connect/widgets/messaging/offline_message_status.dart';

class ChatScreen extends ConsumerStatefulWidget {
  final String chatId;
  final UserModel recipient;

  const ChatScreen({
    super.key,
    required this.chatId,
    required this.recipient,
  });

  @override
  ConsumerState<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends ConsumerState<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  bool _isAttachmentMenuOpen = false;
  bool _isRecording = false;
  bool _isTranslating = false;
  String _selectedLanguage = 'en';
  Timer? _typingTimer;
  bool _isTyping = false;

  // Translation-related state
  final Map<String, bool> _translatedMessageIds = {};

  @override
  void initState() {
    super.initState();
    _messageController.addListener(_onTextChanged);

    // Initialize auto-translate setting
    // Auto-translate is disabled by default

    // Listen for translation events
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // In a real implementation, we would listen to translation events
      // and update the UI accordingly
    });
  }

  @override
  void dispose() {
    _messageController.removeListener(_onTextChanged);
    _messageController.dispose();
    _scrollController.dispose();
    _typingTimer?.cancel();
    // Clear typing status when leaving the chat
    ref.read(chatProvider.notifier).clearTypingStatus(widget.chatId);

    // In a real implementation, we would clean up any translation-related resources

    super.dispose();
  }

  void _onTextChanged() {
    final text = _messageController.text.trim();

    if (text.isNotEmpty && !_isTyping) {
      // User started typing
      _isTyping = true;
      ref.read(chatProvider.notifier).setTypingStatus(widget.chatId, true);

      // Reset the timer
      _typingTimer?.cancel();
      _typingTimer = Timer(const Duration(seconds: 5), () {
        if (mounted) {
          _isTyping = false;
          ref.read(chatProvider.notifier).setTypingStatus(widget.chatId, false);
        }
      });
    } else if (text.isEmpty && _isTyping) {
      // User stopped typing
      _isTyping = false;
      ref.read(chatProvider.notifier).setTypingStatus(widget.chatId, false);
      _typingTimer?.cancel();
    } else if (text.isNotEmpty) {
      // User is still typing, reset the timer
      _typingTimer?.cancel();
      _typingTimer = Timer(const Duration(seconds: 5), () {
        if (mounted) {
          _isTyping = false;
          ref.read(chatProvider.notifier).setTypingStatus(widget.chatId, false);
        }
      });
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  Future<void> _sendMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    final currentUser = await ref.read(currentUserModelProvider.future);
    if (currentUser == null) return;

    final isOnline = ref.read(isOnlineProvider);
    final status = isOnline ? MessageStatus.sending : MessageStatus.pending;

    final message = MessageModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      chatId: widget.chatId,
      senderId: currentUser.id,
      recipientId: widget.recipient.id,
      text: text,
      timestamp: DateTime.now(),
      status: status,
      type: MessageType.text,
    );

    try {
      await ref.read(chatProvider.notifier).sendMessage(message);
      _messageController.clear();
      _scrollToBottom();

      // Show a snackbar if offline
      if (!isOnline && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Message saved and will be sent when you reconnect'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send message: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _pickImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 1024,
      maxHeight: 1024,
      imageQuality: 85,
    );

    if (image != null) {
      final currentUser = await ref.read(currentUserModelProvider.future);
      if (currentUser == null) return;

      final isOnline = ref.read(isOnlineProvider);
      final status = isOnline ? MessageStatus.sending : MessageStatus.pending;

      final message = MessageModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        chatId: widget.chatId,
        senderId: currentUser.id,
        recipientId: widget.recipient.id,
        text: '',
        timestamp: DateTime.now(),
        status: status,
        type: MessageType.image,
        mediaUrl: image.path,
      );

      try {
        await ref
            .read(chatProvider.notifier)
            .sendMediaMessage(message, File(image.path));
        _scrollToBottom();

        // Show a snackbar if offline
        if (!isOnline && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Image saved and will be sent when you reconnect'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 2),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to send image: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _takePhoto() async {
    final ImagePicker picker = ImagePicker();
    final XFile? photo = await picker.pickImage(
      source: ImageSource.camera,
      maxWidth: 1024,
      maxHeight: 1024,
      imageQuality: 85,
    );

    if (photo != null) {
      final currentUser = await ref.read(currentUserModelProvider.future);
      if (currentUser == null) return;

      final isOnline = ref.read(isOnlineProvider);
      final status = isOnline ? MessageStatus.sending : MessageStatus.pending;

      final message = MessageModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        chatId: widget.chatId,
        senderId: currentUser.id,
        recipientId: widget.recipient.id,
        text: '',
        timestamp: DateTime.now(),
        status: status,
        type: MessageType.image,
        mediaUrl: photo.path,
      );

      try {
        await ref
            .read(chatProvider.notifier)
            .sendMediaMessage(message, File(photo.path));
        _scrollToBottom();

        // Show a snackbar if offline
        if (!isOnline && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Photo saved and will be sent when you reconnect'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 2),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to send photo: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _toggleAttachmentMenu() {
    setState(() {
      _isAttachmentMenuOpen = !_isAttachmentMenuOpen;
    });
  }

  void _toggleRecording() {
    setState(() {
      _isRecording = !_isRecording;
    });
    // Implement voice recording logic
  }

  void _toggleTranslation() {
    setState(() {
      _isTranslating = !_isTranslating;
    });

    // Show language selection dialog
    if (_isTranslating) {
      _showTranslationSettings();
    } else {
      // Clear all translations when disabling
      setState(() {
        _translatedMessageIds.clear();
      });
    }
  }

  void _showTranslationSettings() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => const MessageTranslationSettings(),
    );
  }

  void _showLanguageSelectionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Translation Language'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildLanguageOption('en', 'English'),
            _buildLanguageOption('fr', 'French'),
            _buildLanguageOption('yo', 'Yoruba'),
            _buildLanguageOption('ig', 'Igbo'),
            _buildLanguageOption('ha', 'Hausa'),
            _buildLanguageOption('sw', 'Swahili'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageOption(String code, String name) {
    return RadioListTile<String>(
      title: Text(name),
      value: code,
      groupValue: _selectedLanguage,
      onChanged: (value) {
        setState(() {
          _selectedLanguage = value!;
        });
        Navigator.pop(context);
      },
    );
  }

  String _getLanguageName(String code) {
    switch (code) {
      case 'en':
        return 'English';
      case 'fr':
        return 'French';
      case 'yo':
        return 'Yoruba';
      case 'ig':
        return 'Igbo';
      case 'ha':
        return 'Hausa';
      case 'sw':
        return 'Swahili';
      default:
        return code;
    }
  }

  @override
  Widget build(BuildContext context) {
    final messagesAsync = ref.watch(chatMessagesProvider(widget.chatId));
    final currentUserAsync = ref.watch(currentUserModelProvider);
    final isOnline = ref.watch(isOnlineProvider);

    // Watch typing status
    final typingStatusAsync = ref.watch(typingStatusProvider({
      'chatId': widget.chatId,
      'userId': widget.recipient.id,
    }));

    // Determine subtitle text based on typing status and connectivity
    final subtitleText = !isOnline
        ? 'Offline'
        : typingStatusAsync.when(
            data: (isTyping) => isTyping ? 'Typing...' : 'Online',
            loading: () => 'Online',
            error: (_, __) => 'Online',
          );

    return Scaffold(
      appBar: CustomAppBar(
        title: widget.recipient.firstName,
        subTitle: subtitleText,
        showBackButton: true,
        actions: [
          IconButton(
            icon: Icon(
              _isTranslating ? Icons.translate : Icons.translate_outlined,
              color: _isTranslating ? AppTheme.primaryColor : null,
            ),
            onPressed: _toggleTranslation,
          ),
          if (_isTranslating)
            IconButton(
              icon: const Icon(Icons.rate_review_outlined),
              tooltip: 'Translation Feedback',
              onPressed: () {
                Navigator.pushNamed(context, '/translation_feedback');
              },
            ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              // Show chat options menu
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Offline message status
          const OfflineMessageStatus(),

          // Translation banner
          if (_isTranslating)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              color: AppTheme.primaryColor.withAlpha(30),
              child: Row(
                children: [
                  const Icon(
                    Icons.translate,
                    size: 18,
                    color: AppTheme.primaryColor,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Translation enabled: ${_getLanguageName(_selectedLanguage)}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: _showLanguageSelectionDialog,
                    child: const Text(
                      'Change',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // Messages list
          Expanded(
            child: messagesAsync.when(
              data: (messages) {
                if (messages.isEmpty) {
                  return _buildEmptyChat();
                }

                return currentUserAsync.when(
                  data: (currentUser) {
                    if (currentUser == null) {
                      return const Center(child: Text('User not found'));
                    }

                    return ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 16),
                      itemCount: messages.length,
                      itemBuilder: (context, index) {
                        final message = messages[index];
                        final isMe = message.senderId == currentUser.id;
                        final showDate = index == 0 ||
                            !_isSameDay(messages[index - 1].timestamp,
                                message.timestamp);

                        return Column(
                          children: [
                            if (showDate)
                              _buildDateSeparator(message.timestamp),
                            _buildMessageBubble(message, isMe),
                          ],
                        );
                      },
                    );
                  },
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Center(
                    child: Text('Error loading user: $error'),
                  ),
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Text('Error loading messages: $error'),
              ),
            ),
          ),

          // Attachment menu
          if (_isAttachmentMenuOpen) _buildAttachmentMenu(),

          // Message input
          _buildMessageInput(),
        ],
      ),
    );
  }

  Widget _buildEmptyChat() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 80,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 16),
          const Text(
            'No messages yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start the conversation with ${widget.recipient.firstName}',
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateSeparator(DateTime date) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          Expanded(child: Divider(color: Colors.grey[300])),
          const SizedBox(width: 8),
          Text(
            _formatDate(date),
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(width: 8),
          Expanded(child: Divider(color: Colors.grey[300])),
        ],
      ),
    );
  }

  void _showReactionSelector(MessageModel message, Offset position) {
    // Store reaction state locally
    // MessageModel? reactionMessage = message; // Unused variable removed
    // Offset reactionPosition = position; // Unused variable removed

    // Show reaction selector as an overlay
    OverlayState? overlayState = Overlay.of(context);
    OverlayEntry? overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) {
        return Stack(
          children: [
            // Transparent background that dismisses when tapped
            GestureDetector(
              onTap: () {
                overlayEntry?.remove();
              },
              child: Container(
                color: Colors.transparent,
                width: double.infinity,
                height: double.infinity,
              ),
            ),

            // Reaction selector positioned above the message
            Positioned(
              left: position.dx,
              top: position.dy - 60, // Position above the message
              child: MessageReactionSelector(
                message: message,
                onClose: () {
                  overlayEntry?.remove();
                },
              ),
            ),
          ],
        );
      },
    );

    overlayState.insert(overlayEntry);
  }

  Future<void> _showMessageOptions(MessageModel message, bool isMe) async {
    final result = await showModalBottomSheet<String>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.copy),
                title: const Text('Copy'),
                onTap: () {
                  Navigator.pop(context, 'copy');
                },
              ),
              ListTile(
                leading: const Icon(Icons.emoji_emotions),
                title: const Text('React'),
                onTap: () {
                  Navigator.pop(context, 'react');
                },
              ),
              if (!isMe) ...[
                ListTile(
                  leading: const Icon(Icons.flag, color: Colors.red),
                  title: const Text('Report Message'),
                  onTap: () {
                    Navigator.pop(context, 'report');
                  },
                ),
              ],
              if (isMe) ...[
                ListTile(
                  leading: const Icon(Icons.delete),
                  title: const Text('Delete'),
                  onTap: () {
                    Navigator.pop(context, 'delete');
                  },
                ),
              ],
              ListTile(
                leading: const Icon(Icons.forward),
                title: const Text('Forward'),
                onTap: () {
                  Navigator.pop(context, 'forward');
                },
              ),
            ],
          ),
        );
      },
    );

    if (result == null) return;

    switch (result) {
      case 'copy':
        // Copy message text to clipboard
        break;
      case 'react':
        // Show reaction selector at the center of the screen
        if (mounted) {
          final size = MediaQuery.of(context).size;
          _showReactionSelector(
              message,
              Offset(
                size.width / 2,
                size.height / 2,
              ));
        }
        break;
      case 'report':
        _reportMessage(message);
        break;
      case 'delete':
        // Delete message
        break;
      case 'forward':
        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  EnhancedForwardMessageScreen(message: message),
            ),
          );
        }
        break;
    }
  }

  Future<void> _reportMessage(MessageModel message) async {
    final report = await MessageReportDialog.show(
      context: context,
      message: message,
      senderName: widget.recipient.firstName,
      onReportSubmitted: (report) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Report submitted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      },
    );

    if (report != null) {
      // Message has been reported
    }
  }

  /// Toggle translation for a specific message
  void _toggleMessageTranslation(String messageId) {
    setState(() {
      if (_translatedMessageIds.containsKey(messageId)) {
        _translatedMessageIds.remove(messageId);
      } else {
        _translatedMessageIds[messageId] = true;

        // In a real implementation, we would call the message translation service
        // to translate the message and update the message model
        // For now, we'll just update the UI state
      }
    });
  }

  Widget _buildMessageBubble(MessageModel message, bool isMe) {
    // Check if this message should be translated
    final shouldTranslate = _isTranslating &&
        !isMe &&
        message.type == MessageType.text &&
        message.originalLanguage != _selectedLanguage;

    // Handle reported or blocked messages
    if (message.status == MessageStatus.blocked) {
      return Align(
        alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
        child: Container(
          margin: EdgeInsets.only(
            bottom: 8,
            left: isMe ? 64 : 0,
            right: isMe ? 0 : 64,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.block,
                    size: 14,
                    color: Colors.red,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'This message has been removed',
                    style: TextStyle(
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                _formatTime(message.timestamp),
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
        ),
      );
    }

    // If translation is enabled for this message, show the translated version
    if (shouldTranslate && _translatedMessageIds.containsKey(message.id)) {
      // For now, we'll just show a mock translation
      // In a real implementation, we would fetch the translation from the service
      return _buildTranslatedMessageBubble(message, isMe);
    }

    // If translation is enabled but this message is not yet translated,
    // add a translation toggle button
    if (shouldTranslate && !_translatedMessageIds.containsKey(message.id)) {
      return Column(
        crossAxisAlignment:
            isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          // Regular message bubble
          _buildRegularMessageBubble(message, isMe),

          // Translation toggle
          Padding(
            padding: EdgeInsets.only(
              left: isMe ? 0 : 12,
              right: isMe ? 12 : 0,
              bottom: 8,
            ),
            child: MessageTranslationToggle(
              message: message,
              isTranslated: false,
              onToggle: (_) => _toggleMessageTranslation(message.id),
            ),
          ),
        ],
      );
    }

    // Default case: return the regular message bubble
    return _buildRegularMessageBubble(message, isMe);
  }

  /// Build a regular message bubble without translation
  Widget _buildRegularMessageBubble(MessageModel message, bool isMe) {
    return Column(
      crossAxisAlignment:
          isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onLongPress: () => _showMessageOptions(message, isMe),
          onDoubleTap: () {
            // Show reaction selector at the position of the message
            final RenderBox box = context.findRenderObject() as RenderBox;
            final position = box.localToGlobal(Offset.zero);
            _showReactionSelector(message, position);
          },
          child: Align(
            alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
            child: Container(
              margin: EdgeInsets.only(
                bottom:
                    message.reactions != null && message.reactions!.isNotEmpty
                        ? 0
                        : 8,
                left: isMe ? 64 : 0,
                right: isMe ? 0 : 64,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              decoration: BoxDecoration(
                color: isMe ? AppTheme.primaryColor : Colors.grey[200],
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (message.status == MessageStatus.reported && !isMe)
                    const Padding(
                      padding: EdgeInsets.only(bottom: 4),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.report_problem,
                            size: 12,
                            color: Colors.orange,
                          ),
                          SizedBox(width: 4),
                          Text(
                            'Reported',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.orange,
                            ),
                          ),
                        ],
                      ),
                    ),
                  if (message.isForwarded)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.forward,
                            size: 12,
                            color: isMe
                                ? Colors.white.withAlpha(180)
                                : Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Forwarded',
                            style: TextStyle(
                              fontSize: 10,
                              fontStyle: FontStyle.italic,
                              color: isMe
                                  ? Colors.white.withAlpha(180)
                                  : Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  if (message.type == MessageType.text)
                    Text(
                      message.text,
                      style: TextStyle(
                        fontSize: 16,
                        color: isMe ? Colors.white : AppTheme.textPrimaryColor,
                      ),
                    )
                  else if (message.type == MessageType.image)
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: message.mediaUrl.startsWith('http')
                          ? Image.network(
                              message.mediaUrl,
                              width: 200,
                              fit: BoxFit.cover,
                              loadingBuilder:
                                  (context, child, loadingProgress) {
                                if (loadingProgress == null) return child;
                                return Container(
                                  width: 200,
                                  height: 150,
                                  color: Colors.grey[300],
                                  child: const Center(
                                    child: CircularProgressIndicator(),
                                  ),
                                );
                              },
                            )
                          : Image.file(
                              File(message.mediaUrl),
                              width: 200,
                              fit: BoxFit.cover,
                            ),
                    ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _formatTime(message.timestamp),
                        style: TextStyle(
                          fontSize: 10,
                          color: isMe
                              ? Colors.white.withAlpha(180)
                              : Colors.grey[500],
                        ),
                      ),
                      const SizedBox(width: 4),
                      if (isMe) _buildMessageStatusIndicator(message.status),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),

        // Display reactions if any
        if (message.reactions != null && message.reactions!.isNotEmpty)
          MessageReactionsWidget(
            message: message,
            isMe: isMe,
            onReactionAdded: (reactionType) {
              ref
                  .read(chatProvider.notifier)
                  .addReaction(message, reactionType);
            },
            onReactionRemoved: () {
              ref.read(chatProvider.notifier).removeReaction(message);
            },
          ),
      ],
    );
  }

  Widget _buildAttachmentMenu() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildAttachmentOption(
            icon: Icons.photo_library,
            label: 'Gallery',
            onTap: _pickImage,
          ),
          _buildAttachmentOption(
            icon: Icons.camera_alt,
            label: 'Camera',
            onTap: _takePhoto,
          ),
          _buildAttachmentOption(
            icon: Icons.location_on,
            label: 'Location',
            onTap: () {
              // Implement location sharing
            },
          ),
          _buildAttachmentOption(
            icon: Icons.contact_page,
            label: 'Contact',
            onTap: () {
              // Implement contact sharing
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAttachmentOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withAlpha(30),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: AppTheme.primaryColor,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 * 255 ≈ 13
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            icon: Icon(
              _isAttachmentMenuOpen ? Icons.close : Icons.attach_file,
              color: AppTheme.textSecondaryColor,
            ),
            onPressed: _toggleAttachmentMenu,
          ),
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: 'Type a message...',
                hintStyle: const TextStyle(
                  color: AppTheme.textSecondaryColor,
                  fontSize: 16,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          IconButton(
            icon: Icon(
              _isRecording ? Icons.stop : Icons.mic_none,
              color: _isRecording ? Colors.red : AppTheme.textSecondaryColor,
            ),
            onPressed: _toggleRecording,
          ),
          IconButton(
            icon: const Icon(
              Icons.send,
              color: AppTheme.primaryColor,
            ),
            onPressed: _sendMessage,
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    if (date.year == now.year &&
        date.month == now.month &&
        date.day == now.day) {
      return 'Today';
    } else if (date.year == now.year &&
        date.month == now.month &&
        date.day == now.day - 1) {
      return 'Yesterday';
    } else {
      return DateFormat('MMMM d, yyyy').format(date);
    }
  }

  String _formatTime(DateTime dateTime) {
    return DateFormat('HH:mm').format(dateTime);
  }

  /// Build a translated message bubble
  Widget _buildTranslatedMessageBubble(MessageModel message, bool isMe) {
    // Mock translation metadata for demo purposes
    // In a real implementation, we would fetch this from the message translation service
    final translationMetadata = MessageTranslationMetadata(
      originalText: message.text,
      translatedText: _getMockTranslation(message.text, _selectedLanguage),
      sourceLanguage: message.originalLanguage,
      targetLanguage: _selectedLanguage,
      translatedAt: DateTime.now(),
      isAudioAvailable: true,
    );

    return TranslatedMessageBubble(
      message: message,
      translationMetadata: translationMetadata,
      isMe: isMe,
    );
  }

  /// Get a mock translation for demo purposes
  String _getMockTranslation(String text, String targetLanguage) {
    // In a real implementation, this would call the translation service
    switch (targetLanguage) {
      case 'fr':
        return 'Bonjour, comment allez-vous aujourd\'hui?';
      case 'yo':
        return 'Bawo ni, bawo ni o se wa loni?';
      case 'ig':
        return 'Kedu, kedu ka ị mere taa?';
      case 'ha':
        return 'Sannu, yaya kake yau?';
      case 'sw':
        return 'Habari, habari yako leo?';
      default:
        return 'Hello, how are you today?';
    }
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  Widget _buildMessageStatusIndicator(MessageStatus status) {
    switch (status) {
      case MessageStatus.pending:
        return Icon(
          Icons.access_time,
          size: 12,
          color: Colors.white.withAlpha(180),
        );
      case MessageStatus.sending:
        return Icon(
          Icons.arrow_upward,
          size: 12,
          color: Colors.white.withAlpha(180),
        );
      case MessageStatus.sent:
        return Icon(
          Icons.done,
          size: 12,
          color: Colors.white.withAlpha(180),
        );
      case MessageStatus.delivered:
        return Icon(
          Icons.done_all,
          size: 12,
          color: Colors.white.withAlpha(180),
        );
      case MessageStatus.read:
        return Icon(
          Icons.done_all,
          size: 12,
          color: Colors.blue[300],
        );
      case MessageStatus.failed:
        return Icon(
          Icons.error_outline,
          size: 12,
          color: Colors.red[300],
        );
      default:
        return Icon(
          Icons.done,
          size: 12,
          color: Colors.white.withAlpha(180),
        );
    }
  }
}
