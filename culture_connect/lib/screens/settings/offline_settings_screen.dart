import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/models/offline/offline_content.dart';
import 'package:culture_connect/models/offline/sync_status.dart';
import 'package:culture_connect/providers/offline_mode_provider.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/offline/sync_status_indicator.dart';

/// A screen for managing offline settings
class OfflineSettingsScreen extends ConsumerStatefulWidget {
  /// Creates a new offline settings screen
  const OfflineSettingsScreen({super.key});

  @override
  ConsumerState<OfflineSettingsScreen> createState() =>
      _OfflineSettingsScreenState();
}

class _OfflineSettingsScreenState extends ConsumerState<OfflineSettingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Offline Settings',
        showBackButton: true,
      ),
      body: Column(
        children: [
          // Tab bar
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'Offline Content'),
              Tab(text: 'Sync Settings'),
            ],
            labelColor: AppColors.primary,
            unselectedLabelColor: Colors.grey,
            indicatorColor: AppColors.primary,
          ),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: const [
                // Offline content tab
                _OfflineContentTab(),

                // Sync settings tab
                _SyncSettingsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// A tab for managing offline content
class _OfflineContentTab extends ConsumerWidget {
  /// Creates a new offline content tab
  const _OfflineContentTab();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the offline content
    final offlineContentAsync = ref.watch(offlineContentNotifierProvider);

    // Get the total size of offline content
    final totalSizeAsync = ref.watch(totalOfflineContentSizeProvider);

    return offlineContentAsync.when(
      data: (offlineContent) {
        if (offlineContent.isEmpty) {
          return _buildEmptyState();
        }

        return Column(
          children: [
            // Storage usage
            totalSizeAsync.when(
              data: (totalSize) => _buildStorageUsage(totalSize),
              loading: () => const SizedBox.shrink(),
              error: (_, __) => const SizedBox.shrink(),
            ),

            // Content list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: offlineContent.length,
                itemBuilder: (context, index) {
                  final content = offlineContent[index];
                  return _buildContentItem(context, ref, content);
                },
              ),
            ),

            // Clear all button
            Padding(
              padding: const EdgeInsets.all(16),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => _showClearAllDialog(context, ref),
                  icon: const Icon(Icons.delete),
                  label: const Text('Clear All Offline Content'),
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.white,
                    backgroundColor: Colors.red,
                  ),
                ),
              ),
            ),
          ],
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => Center(
        child: ErrorView(
          error: error.toString(),
          onRetry: () => ref.refresh(offlineContentNotifierProvider),
        ),
      ),
    );
  }

  /// Build the empty state
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.offline_bolt,
              size: 80,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'No Offline Content',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'You haven\'t saved any content for offline use yet. '
              'Look for the download button when viewing content to save it for offline use.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build the storage usage widget
  Widget _buildStorageUsage(int totalSize) {
    final formattedSize = _formatSize(totalSize);

    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[100],
      child: Row(
        children: [
          const Icon(
            Icons.storage,
            color: AppColors.primary,
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Storage Usage',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Total offline content: $formattedSize',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build a content item
  Widget _buildContentItem(
      BuildContext context, WidgetRef ref, OfflineContent content) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and type
            Row(
              children: [
                Icon(
                  IconData(
                    int.parse('0xe${content.contentTypeIcon}', radix: 16),
                    fontFamily: 'MaterialIcons',
                  ),
                  color: AppColors.primary,
                  size: 24,
                ),

                const SizedBox(width: 16),

                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        content.title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        content.contentTypeDisplayName,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),

                // Sync status
                SyncStatusIndicator(
                  status: content.syncStatus,
                  size: 20,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Details
            Row(
              children: [
                // Size
                if (content.contentSize != null) ...[
                  Icon(
                    Icons.sd_storage,
                    color: Colors.grey[600],
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    content.formattedSize,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(width: 16),
                ],

                // Last sync time
                Icon(
                  Icons.access_time,
                  color: Colors.grey[600],
                  size: 16,
                ),

                const SizedBox(width: 4),

                Text(
                  'Synced ${content.formattedLastSyncTime}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Actions
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Remove button
                TextButton.icon(
                  onPressed: () => _showRemoveDialog(context, ref, content),
                  icon: const Icon(Icons.delete),
                  label: const Text('Remove'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                ),

                const SizedBox(width: 16),

                // Sync button
                if (content.syncStatus != SyncStatus.synced)
                  TextButton.icon(
                    onPressed: () => _syncContent(ref),
                    icon: const Icon(Icons.sync),
                    label: const Text('Sync'),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.primary,
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Show a dialog to confirm removing content
  Future<void> _showRemoveDialog(
      BuildContext context, WidgetRef ref, OfflineContent content) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove from Offline Storage'),
        content: Text(
            'Are you sure you want to remove "${content.title}" from offline storage?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Remove'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // Remove the content from offline storage
      await ref
          .read(offlineContentNotifierProvider.notifier)
          .removeOfflineContent(content.id);
    }
  }

  /// Show a dialog to confirm clearing all content
  Future<void> _showClearAllDialog(BuildContext context, WidgetRef ref) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Offline Content'),
        content: const Text(
            'Are you sure you want to clear all offline content? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // Clear all offline content
      await ref
          .read(offlineContentNotifierProvider.notifier)
          .clearAllOfflineContent();
    }
  }

  /// Sync all pending offline content
  Future<void> _syncContent(WidgetRef ref) async {
    await ref
        .read(offlineContentNotifierProvider.notifier)
        .syncOfflineContent();
  }

  /// Format a size in bytes to a human-readable string
  String _formatSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      final kb = bytes / 1024;
      return '${kb.toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      final mb = bytes / (1024 * 1024);
      return '${mb.toStringAsFixed(1)} MB';
    } else {
      final gb = bytes / (1024 * 1024 * 1024);
      return '${gb.toStringAsFixed(1)} GB';
    }
  }
}

/// A tab for managing sync settings
class _SyncSettingsTab extends ConsumerStatefulWidget {
  /// Creates a new sync settings tab
  const _SyncSettingsTab();

  @override
  ConsumerState<_SyncSettingsTab> createState() => _SyncSettingsTabState();
}

class _SyncSettingsTabState extends ConsumerState<_SyncSettingsTab> {
  String _syncFrequency = 'daily';
  bool _syncWifiOnly = true;
  bool _syncRequiresCharging = false;

  @override
  void initState() {
    super.initState();

    // Load the current sync settings
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final syncSettings = ref.read(syncSettingsProvider);
      setState(() {
        _syncFrequency = syncSettings['syncFrequency'];
        _syncWifiOnly = syncSettings['syncWifiOnly'];
        _syncRequiresCharging = syncSettings['syncRequiresCharging'];
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Sync frequency
        _buildSectionHeader('Sync Frequency'),

        _buildRadioTile(
          title: 'Hourly',
          subtitle: 'Sync content every hour',
          value: 'hourly',
          groupValue: _syncFrequency,
          onChanged: (value) => _updateSyncFrequency(value!),
        ),

        _buildRadioTile(
          title: 'Daily',
          subtitle: 'Sync content once a day',
          value: 'daily',
          groupValue: _syncFrequency,
          onChanged: (value) => _updateSyncFrequency(value!),
        ),

        _buildRadioTile(
          title: 'Weekly',
          subtitle: 'Sync content once a week',
          value: 'weekly',
          groupValue: _syncFrequency,
          onChanged: (value) => _updateSyncFrequency(value!),
        ),

        const SizedBox(height: 24),

        // Sync conditions
        _buildSectionHeader('Sync Conditions'),

        SwitchListTile(
          title: const Text('Wi-Fi Only'),
          subtitle: const Text('Only sync when connected to Wi-Fi'),
          value: _syncWifiOnly,
          onChanged: _updateSyncWifiOnly,
          activeColor: AppColors.primary,
        ),

        SwitchListTile(
          title: const Text('While Charging'),
          subtitle: const Text('Only sync when the device is charging'),
          value: _syncRequiresCharging,
          onChanged: _updateSyncRequiresCharging,
          activeColor: AppColors.primary,
        ),

        const SizedBox(height: 24),

        // Manual sync button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _syncNow,
            icon: const Icon(Icons.sync),
            label: const Text('Sync Now'),
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: AppColors.primary,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  /// Build a section header
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColors.primary,
        ),
      ),
    );
  }

  /// Build a radio list tile
  Widget _buildRadioTile({
    required String title,
    required String subtitle,
    required String value,
    required String groupValue,
    required Function(String?) onChanged,
  }) {
    return RadioListTile<String>(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      groupValue: groupValue,
      onChanged: onChanged,
      activeColor: AppColors.primary,
    );
  }

  /// Update the sync frequency
  Future<void> _updateSyncFrequency(String value) async {
    setState(() {
      _syncFrequency = value;
    });

    await ref.read(offlineContentNotifierProvider.notifier).updateSyncSettings(
          syncFrequency: value,
        );
  }

  /// Update the sync Wi-Fi only setting
  Future<void> _updateSyncWifiOnly(bool value) async {
    setState(() {
      _syncWifiOnly = value;
    });

    await ref.read(offlineContentNotifierProvider.notifier).updateSyncSettings(
          syncWifiOnly: value,
        );
  }

  /// Update the sync requires charging setting
  Future<void> _updateSyncRequiresCharging(bool value) async {
    setState(() {
      _syncRequiresCharging = value;
    });

    await ref.read(offlineContentNotifierProvider.notifier).updateSyncSettings(
          syncRequiresCharging: value,
        );
  }

  /// Sync all pending offline content now
  Future<void> _syncNow() async {
    await ref
        .read(offlineContentNotifierProvider.notifier)
        .syncOfflineContent();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Sync started'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }
}
