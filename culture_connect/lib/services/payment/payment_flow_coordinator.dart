import 'package:flutter/material.dart';
import 'dart:async';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/payment/payment_config_service.dart';
import 'package:culture_connect/services/payment/payment_auth_service.dart';
import 'package:culture_connect/services/payment/real_payment_api_service.dart';
import 'package:culture_connect/services/payment/websocket_status_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/screens/payment/production_payment_screen.dart';

/// Orchestrates the entire payment process from initialization to completion
/// Manages state, error recovery, provider switching, and navigation coordination
/// Performance target: <200ms coordination overhead with efficient state management
class PaymentFlowCoordinator {
  static const Duration _coordinationTimeout = Duration(minutes: 15);
  static const Duration _providerSwitchDelay = Duration(seconds: 3);
  static const int _maxRetryAttempts = 3;
  static const int _maxProviderSwitches = 2;

  final LoggingService _loggingService;
  final PaymentConfigService _configService;
  final PaymentAuthService _authService;
  final RealPaymentApiService _apiService;
  final WebSocketStatusService _webSocketService;

  // Flow state management
  PaymentFlowState _currentState = PaymentFlowState.idle;
  String? _currentTransactionReference;
  PaymentProvider? _currentProvider;
  PaymentProvider? _originalProvider;
  int _retryAttempts = 0;
  int _providerSwitches = 0;
  DateTime? _flowStartTime;

  // Flow data
  Booking? _currentBooking;
  String? _userEmail;
  String? _userName;
  String? _userPhone;
  PaymentInitResponse? _paymentInitData;

  // Coordination streams
  final StreamController<PaymentFlowState> _stateController =
      StreamController<PaymentFlowState>.broadcast();
  final StreamController<PaymentFlowEvent> _eventController =
      StreamController<PaymentFlowEvent>.broadcast();

  // Timers and subscriptions
  Timer? _coordinationTimer;
  StreamSubscription<PaymentStatusUpdate>? _statusSubscription;

  PaymentFlowCoordinator({
    required LoggingService loggingService,
    required PaymentConfigService configService,
    required PaymentAuthService authService,
    required RealPaymentApiService apiService,
    required WebSocketStatusService webSocketService,
  })  : _loggingService = loggingService,
        _configService = configService,
        _authService = authService,
        _apiService = apiService,
        _webSocketService = webSocketService;

  /// Stream of payment flow state changes
  Stream<PaymentFlowState> get stateStream => _stateController.stream;

  /// Stream of payment flow events
  Stream<PaymentFlowEvent> get eventStream => _eventController.stream;

  /// Current payment flow state
  PaymentFlowState get currentState => _currentState;

  /// Current transaction reference
  String? get currentTransactionReference => _currentTransactionReference;

  /// Initialize payment flow coordinator
  /// TODO: Backend Integration - Flow Coordination API
  /// Endpoint: POST /api/payments/flows/initialize
  /// Headers: Authorization: Bearer {jwt_token}
  /// Request: { booking_id, user_context, flow_preferences }
  /// Response: { flow_id, coordination_token, timeout_settings }
  Future<void> initialize() async {
    try {
      await _configService.initialize();
      await _authService.initialize();
      await _apiService.initialize();
      await _webSocketService.initialize();

      _loggingService.info(
        'PaymentFlowCoordinator',
        'Payment flow coordinator initialized successfully',
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'PaymentFlowCoordinator',
        'Failed to initialize payment flow coordinator',
        {'error': e.toString()},
        stackTrace,
      );
      rethrow;
    }
  }

  /// Start complete payment flow
  /// Coordinates from ProductionPaymentScreen → RealTimeStatusScreen → EnhancedSuccessScreen
  /// Performance target: <200ms coordination overhead
  Future<PaymentFlowResult> startPaymentFlow({
    required BuildContext context,
    required Booking booking,
    required String userEmail,
    required String userName,
    String? userPhone,
  }) async {
    final startTime = DateTime.now();
    _flowStartTime = startTime;

    try {
      // Reset flow state
      _resetFlowState();

      // Store flow data
      _currentBooking = booking;
      _userEmail = userEmail;
      _userName = userName;
      _userPhone = userPhone;

      // Update state to initializing
      _updateState(PaymentFlowState.initializing);

      // Start coordination timeout
      _startCoordinationTimer();

      // Navigate to production payment screen
      final paymentResult = await _navigateToPaymentScreen(context);

      if (paymentResult.success) {
        _updateState(PaymentFlowState.completed);

        // Calculate coordination overhead
        final coordinationTime = DateTime.now().difference(startTime);

        // TODO: Backend Integration - Analytics API
        // Track flow completion metrics
        _loggingService.info(
          'PaymentFlowCoordinator',
          'Payment flow completed successfully',
          {
            'transaction_reference': _currentTransactionReference,
            'provider': _currentProvider?.name,
            'coordination_time_ms': coordinationTime.inMilliseconds,
            'retry_attempts': _retryAttempts,
            'provider_switches': _providerSwitches,
          },
        );

        return PaymentFlowResult(
          success: true,
          transactionReference: _currentTransactionReference,
          provider: _currentProvider,
          coordinationTime: coordinationTime,
        );
      } else {
        _updateState(PaymentFlowState.failed);
        return PaymentFlowResult(
          success: false,
          errorMessage: paymentResult.errorMessage,
          coordinationTime: DateTime.now().difference(startTime),
        );
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'PaymentFlowCoordinator',
        'Payment flow failed with exception',
        {'error': e.toString()},
        stackTrace,
      );

      _updateState(PaymentFlowState.failed);
      return PaymentFlowResult(
        success: false,
        errorMessage: 'Payment flow failed: ${e.toString()}',
        coordinationTime: DateTime.now().difference(startTime),
      );
    } finally {
      _cleanup();
    }
  }

  /// Navigate to production payment screen and handle flow
  Future<PaymentFlowResult> _navigateToPaymentScreen(
      BuildContext context) async {
    try {
      _updateState(PaymentFlowState.paymentScreen);

      final result = await Navigator.of(context).push<bool>(
        MaterialPageRoute(
          builder: (context) => ProductionPaymentScreen(
            booking: _currentBooking!,
            userEmail: _userEmail!,
            userName: _userName!,
            userPhone: _userPhone,
          ),
        ),
      );

      if (result == true) {
        return PaymentFlowResult(
          success: true,
          coordinationTime: Duration.zero,
        );
      } else {
        return PaymentFlowResult(
          success: false,
          errorMessage: 'Payment was cancelled or failed',
          coordinationTime: Duration.zero,
        );
      }
    } catch (e) {
      return PaymentFlowResult(
        success: false,
        errorMessage: 'Navigation to payment screen failed: ${e.toString()}',
        coordinationTime: Duration.zero,
      );
    }
  }

  /// Handle payment provider failure and attempt recovery
  /// TODO: Backend Integration - Provider Failover API
  /// Endpoint: POST /api/payments/provider/failover
  /// Headers: Authorization: Bearer {jwt_token}
  /// Request: { transaction_reference, failed_provider, reason }
  /// Response: { alternative_provider, new_transaction_reference, retry_config }
  Future<bool> handleProviderFailure({
    required PaymentProvider failedProvider,
    required String reason,
    String? transactionReference,
  }) async {
    if (_providerSwitches >= _maxProviderSwitches) {
      _loggingServicearning(
        'PaymentFlowCoordinator',
        'Maximum provider switches reached',
        {
          'failed_provider': failedProvider.name,
          'switches_attempted': _providerSwitches,
          'max_switches': _maxProviderSwitches,
        },
      );
      return false;
    }

    try {
      _updateState(PaymentFlowStateecovering);
      _providerSwitches++;

      // Determine alternative provider
      final alternativeProvider = _getAlternativeProvider(failedProvider);
      if (alternativeProvider == null) {
        _loggingServicearning(
          'PaymentFlowCoordinator',
          'No alternative provider available',
          {'failed_provider': failedProvider.name},
        );
        return false;
      }

      // Wait before switching to allow for graceful transition
      await Future.delayed(_providerSwitchDelay);

      // Update current provider
      _currentProvider = alternativeProvider;

      _loggingService.info(
        'PaymentFlowCoordinator',
        'Provider switched successfully',
        {
          'from_provider': failedProvider.name,
          'to_provider': alternativeProvider.name,
          'reason': reason,
          'switch_count': _providerSwitches,
        },
      );

      _updateState(PaymentFlowStateetrying);
      return true;
    } catch (e, stackTrace) {
      _loggingService.error(
        'PaymentFlowCoordinator',
        'Provider failover failed',
        {
          'failed_provider': failedProvider.name,
          'error': e.toString(),
        },
        stackTrace,
      );
      return false;
    }
  }

  /// Get alternative payment provider for failover
  PaymentProvider? _getAlternativeProvider(PaymentProvider failedProvider) {
    final availableProviders = PaymentProvider.values
        here((provider) => provider != failedProvider)
        .toList();

    if (availableProviders.isEmpty) return null;

    // Prioritize providers based on reliability and user location
    // TODO: Backend Integration - Provider Health API
    // Get real-time provider health status for intelligent selection
    switch (failedProvider) {
      case PaymentProvider.stripe:
        return PaymentProvider.paystack; // Fallback to regional provider
      case PaymentProvider.paystack:
        return PaymentProvider.stripe; // Fallback to international provider
      case PaymentProvider.busha:
        return PaymentProvider.paystack; // Fallback to traditional payment
    }
  }

  /// Update payment flow state and notify listeners
  void _updateState(PaymentFlowState newState) {
    if (_currentState == newState) return;

    final previousState = _currentState;
    _currentState = newState;

    _stateController.add(newState);

    _loggingService.info(
      'PaymentFlowCoordinator',
      'Payment flow state updated',
      {
        'previous_state': previousState.name,
        'new_state': newState.name,
        'transaction_reference': _currentTransactionReference,
      },
    );
  }

  /// Emit payment flow event
  void _emitEvent(PaymentFlowEvent event) {
    _eventController.add(event);

    _loggingService.info(
      'PaymentFlowCoordinator',
      'Payment flow event emitted',
      {
        'event_type': event.type.name,
        'event_data': event.data,
      },
    );
  }

  /// Start coordination timeout timer
  void _startCoordinationTimer() {
    _coordinationTimer?.cancel();
    _coordinationTimer = Timer(_coordinationTimeout, () {
      _loggingServicearning(
        'PaymentFlowCoordinator',
        'Payment flow coordination timeout',
        {
          'timeout_duration': _coordinationTimeout.inMinutes,
          'current_state': _currentState.name,
        },
      );

      _updateState(PaymentFlowState.timeout);
      _emitEvent(PaymentFlowEvent(
        type: PaymentFlowEventType.timeout,
        data: {'timeout_duration': _coordinationTimeout.inMinutes},
      ));
    });
  }

  /// Reset flow state for new payment
  void _resetFlowState() {
    _currentState = PaymentFlowState.idle;
    _currentTransactionReference = null;
    _currentProvider = null;
    _originalProvider = null;
    _retryAttempts = 0;
    _providerSwitches = 0;
    _flowStartTime = null;
    _currentBooking = null;
    _userEmail = null;
    _userName = null;
    _userPhone = null;
    _paymentInitData = null;
  }

  /// Cleanup resources and subscriptions
  void _cleanup() {
    _coordinationTimer?.cancel();
    _statusSubscription?.cancel();
    _coordinationTimer = null;
    _statusSubscription = null;
  }

  /// Dispose coordinator and cleanup resources
  void dispose() {
    _cleanup();
    _stateController.close();
    _eventController.close();

    _loggingService.info(
      'PaymentFlowCoordinator',
      'Payment flow coordinator disposed',
    );
  }
}

/// Payment flow state enumeration
enum PaymentFlowState {
  idle,
  initializing,
  paymentScreen,
  processing,
  monitoring,
  success,
  recovering,
  retrying,
  failed,
  timeout,
  completed,
}

/// Payment flow event types
enum PaymentFlowEventType {
  stateChanged,
  providerSwitched,
  retryAttempted,
  timeout,
  error,
  success,
}

/// Payment flow event data
class PaymentFlowEvent {
  final PaymentFlowEventType type;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  PaymentFlowEvent({
    required this.type,
    required this.data,
  }) : timestamp = DateTime.now();
}

/// Payment flow result
class PaymentFlowResult {
  final bool success;
  final String? transactionReference;
  final PaymentProvider? provider;
  final String? errorMessage;
  final Duration coordinationTime;
  final int retryAttempts;
  final int providerSwitches;

  const PaymentFlowResult({
    required this.success,
    this.transactionReference,
    this.provider,
    this.errorMessage,
    required this.coordinationTime,
    thisetryAttempts = 0,
    this.providerSwitches = 0,
  });

  /// Convert to JSON for logging and analytics
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'transaction_reference': transactionReference,
      'provider': provider?.name,
      'error_message': errorMessage,
      'coordination_time_ms': coordinationTime.inMilliseconds,
      'retry_attempts': retryAttempts,
      'provider_switches': providerSwitches,
    };
  }
}
