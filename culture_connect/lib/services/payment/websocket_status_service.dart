import 'dart:async';
import 'dart:convert';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/payment/payment_config_service.dart';
import 'package:culture_connect/services/payment/payment_auth_service.dart';
import 'package:culture_connect/utils/exceptions/payment_exceptions.dart';

/// Real-time payment status updates via WebSocket connection
class WebSocketStatusService {
  static const Duration _connectionTimeout = Duration(seconds: 10);
  static const Duration _heartbeatInterval = Duration(seconds: 30);
  static const Duration _reconnectDelay = Duration(seconds: 5);
  static const int _maxReconnectAttempts = 5;

  final LoggingService _loggingService;
  final PaymentConfigService _configService;
  final PaymentAuthService _authService;

  WebSocketChannel? _channel;
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  
  bool _isInitialized = false;
  bool _isConnected = false;
  int _reconnectAttempts = 0;
  String? _currentTransactionReference;

  final StreamController<PaymentStatusUpdate> _statusController = 
      StreamController<PaymentStatusUpdate>.broadcast();
  final StreamController<WebSocketConnectionState> _connectionController = 
      StreamController<WebSocketConnectionState>.broadcast();

  WebSocketStatusService({
    required LoggingService loggingService,
    required PaymentConfigService configService,
    required PaymentAuthService authService,
  })  : _loggingService = loggingService,
        _configService = configService,
        _authService = authService;

  /// Stream of payment status updates
  Stream<PaymentStatusUpdate> get statusUpdates => _statusController.stream;

  /// Stream of WebSocket connection state changes
  Stream<WebSocketConnectionState> get connectionState => _connectionController.stream;

  /// Current connection status
  bool get isConnected => _isConnected;

  /// Initialize the WebSocket status service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _isInitialized = true;

      _loggingService.info(
        'WebSocketStatusService',
        'WebSocket status service initialized',
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'WebSocketStatusService',
        'Failed to initialize WebSocket status service',
        {'error': e.toString()},
        stackTrace,
      );
      rethrow;
    }
  }

  /// Start monitoring payment status for a transaction
  Future<void> startMonitoring(String transactionReference) async {
    if (!_isInitialized) {
      throw PaymentException(
        'WebSocket status service not initialized',
        code: 'WEBSOCKET_NOT_INITIALIZED',
      );
    }

    try {
      _currentTransactionReference = transactionReference;
      await _connect();

      _loggingService.info(
        'WebSocketStatusService',
        'Started monitoring payment status',
        {'transaction_reference': transactionReference},
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'WebSocketStatusService',
        'Failed to start payment monitoring',
        {
          'transaction_reference': transactionReference,
          'error': e.toString(),
        },
        stackTrace,
      );
      rethrow;
    }
  }

  /// Stop monitoring payment status
  Future<void> stopMonitoring() async {
    try {
      _currentTransactionReference = null;
      await _disconnect();

      _loggingService.info(
        'WebSocketStatusService',
        'Stopped monitoring payment status',
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'WebSocketStatusService',
        'Error stopping payment monitoring',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Connect to WebSocket server
  Future<void> _connect() async {
    if (_isConnected) return;

    try {
      _connectionController.add(WebSocketConnectionState.connecting);

      // Get authentication token
      final token = await _authService.getPaymentToken();
      if (token == null) {
        throw PaymentException(
          'No authentication token available',
          code: 'NO_AUTH_TOKEN',
        );
      }

      // Build WebSocket URL with authentication
      final wsUrl = Uri.parse(_configServiceebsocketUrl)eplace(
        path: '/ws/payments/status',
        queryParameters: {
          'token': token,
          if (_currentTransactionReference != null)
            'transaction_reference': _currentTransactionReference!,
        },
      );

      _loggingService.info(
        'WebSocketStatusService',
        'Connecting to WebSocket',
        {'url': wsUrl.toString()eplaceAll(RegExp(r'token=[^&]*'), 'token=***')},
      );

      // Create WebSocket connection
      _channel = WebSocketChannel.connect(wsUrl);

      // Wait for connection with timeout
      await _channel!eady.timeout(_connectionTimeout);

      _isConnected = true;
      _reconnectAttempts = 0;
      _connectionController.add(WebSocketConnectionState.connected);

      // Setup message handling
      _setupMessageHandling();

      // Start heartbeat
      _startHeartbeat();

      _loggingService.info(
        'WebSocketStatusService',
        'WebSocket connected successfully',
      );

    } catch (e, stackTrace) {
      _isConnected = false;
      _connectionController.add(WebSocketConnectionState.disconnected);

      _loggingService.error(
        'WebSocketStatusService',
        'Failed to connect to WebSocket',
        {'error': e.toString()},
        stackTrace,
      );

      // Schedule reconnection attempt
      _scheduleReconnect();
      rethrow;
    }
  }

  /// Disconnect from WebSocket server
  Future<void> _disconnect() async {
    try {
      _heartbeatTimer?.cancel();
      _reconnectTimer?.cancel();

      if (_channel != null) {
        await _channel!.sink.close(status.normalClosure);
        _channel = null;
      }

      _isConnected = false;
      _connectionController.add(WebSocketConnectionState.disconnected);

      _loggingService.info(
        'WebSocketStatusService',
        'WebSocket disconnected',
      );
    } catch (e) {
      _loggingServicearning(
        'WebSocketStatusService',
        'Error during WebSocket disconnect',
        {'error': e.toString()},
      );
    }
  }

  /// Setup message handling for WebSocket
  void _setupMessageHandling() {
    _channel!.stream.listen(
      (message) {
        try {
          _handleMessage(message);
        } catch (e, stackTrace) {
          _loggingService.error(
            'WebSocketStatusService',
            'Error handling WebSocket message',
            {'message': message.toString(), 'error': e.toString()},
            stackTrace,
          );
        }
      },
      onError: (error) {
        _loggingService.error(
          'WebSocketStatusService',
          'WebSocket stream error',
          {'error': error.toString()},
        );
        _handleConnectionError();
      },
      onDone: () {
        _loggingService.info(
          'WebSocketStatusService',
          'WebSocket stream closed',
        );
        _handleConnectionClosed();
      },
    );
  }

  /// Handle incoming WebSocket message
  void _handleMessage(dynamic message) {
    try {
      final data = jsonDecode(message as String) as Map<String, dynamic>;
      final messageType = data['type'] as String?;

      switch (messageType) {
        case 'payment_status_update':
          _handlePaymentStatusUpdate(data);
          break;
        case 'heartbeat_response':
          _handleHeartbeatResponse(data);
          break;
        case 'error':
          _handleErrorMessage(data);
          break;
        default:
          _loggingServicearning(
            'WebSocketStatusService',
            'Unknown message type received',
            {'type': messageType, 'data': data},
          );
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'WebSocketStatusService',
        'Failed to parse WebSocket message',
        {'message': message.toString(), 'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Handle payment status update message
  void _handlePaymentStatusUpdate(Map<String, dynamic> data) {
    try {
      final update = PaymentStatusUpdate.fromJson(data['payload']);
      
      _statusController.add(update);

      _loggingService.info(
        'WebSocketStatusService',
        'Payment status update received',
        {
          'transaction_reference': update.transactionReference,
          'status': update.status.name,
          'timestamp': update.timestamp.toIso8601String(),
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'WebSocketStatusService',
        'Failed to handle payment status update',
        {'data': data, 'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Handle heartbeat response
  void _handleHeartbeatResponse(Map<String, dynamic> data) {
    _loggingService.verbose(
      'WebSocketStatusService',
      'Heartbeat response received',
      {'timestamp': data['timestamp']},
    );
  }

  /// Handle error message
  void _handleErrorMessage(Map<String, dynamic> data) {
    final errorMessage = data['message'] as String? ?? 'Unknown error';
    
    _loggingService.error(
      'WebSocketStatusService',
      'WebSocket error message received',
      {'error_message': errorMessage, 'data': data},
    );

    // Add error to status stream
    _statusController.addError(
      PaymentException(errorMessage, code: 'WEBSOCKET_ERROR'),
    );
  }

  /// Handle connection error
  void _handleConnectionError() {
    _isConnected = false;
    _connectionController.add(WebSocketConnectionState.error);
    _scheduleReconnect();
  }

  /// Handle connection closed
  void _handleConnectionClosed() {
    _isConnected = false;
    _connectionController.add(WebSocketConnectionState.disconnected);
    
    // Reconnect if we were monitoring a transaction
    if (_currentTransactionReference != null) {
      _scheduleReconnect();
    }
  }

  /// Schedule reconnection attempt
  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      _loggingService.error(
        'WebSocketStatusService',
        'Max reconnection attempts reached, giving up',
        {'attempts': _reconnectAttempts},
      );
      return;
    }

    _reconnectAttempts++;
    final delay = _reconnectDelay * _reconnectAttempts; // Exponential backoff

    _loggingService.info(
      'WebSocketStatusService',
      'Scheduling reconnection attempt',
      {'attempt': _reconnectAttempts, 'delay_seconds': delay.inSeconds},
    );

    _reconnectTimer = Timer(delay, () async {
      try {
        await _connect();
      } catch (e) {
        _loggingServicearning(
          'WebSocketStatusService',
          'Reconnection attempt failed',
          {'attempt': _reconnectAttempts, 'error': e.toString()},
        );
      }
    });
  }

  /// Start heartbeat to keep connection alive
  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (_) {
      if (_isConnected && _channel != null) {
        try {
          _channel!.sink.add(jsonEncode({
            'type': 'heartbeat',
            'timestamp': DateTime.now().toIso8601String(),
          }));
        } catch (e) {
          _loggingServicearning(
            'WebSocketStatusService',
            'Failed to send heartbeat',
            {'error': e.toString()},
          );
        }
      }
    });
  }

  /// Dispose resources
  void dispose() {
    _heartbeatTimer?.cancel();
    _reconnectTimer?.cancel();
    _disconnect();
    _statusController.close();
    _connectionController.close();
    _isInitialized = false;
  }
}

/// WebSocket connection state
enum WebSocketConnectionState {
  disconnected,
  connecting,
  connected,
  error,
}

/// Payment status update from WebSocket
class PaymentStatusUpdate {
  final String transactionReference;
  final PaymentStatus status;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  const PaymentStatusUpdate({
    required this.transactionReference,
    required this.status,
    required this.timestamp,
    this.metadata,
  });

  factory PaymentStatusUpdate.fromJson(Map<String, dynamic> json) {
    return PaymentStatusUpdate(
      transactionReference: json['transaction_reference'],
      status: PaymentStatus.values.firstWhere(
        (s) => s.name == json['status'],
      ),
      timestamp: DateTime.parse(json['timestamp']),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
}
