import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/utils/exceptions/payment_exceptions.dart';

/// Service for managing payment provider configurations and environment settings
class PaymentConfigService {
  static const String _configCacheKey = 'payment_provider_configs';
  static const String _environmentKey = 'payment_environment';
  static const Duration _configCacheExpiry = Duration(hours: 24);

  final LoggingService _loggingService;
  final FlutterSecureStorage _secureStorage;
  late final SharedPreferences _prefs;

  bool _isInitialized = false;
  PaymentEnvironment _currentEnvironment = PaymentEnvironment.production;
  Map<PaymentProvider, PaymentProviderConfiguration> _providerConfigs = {};
  DateTime? _lastConfigUpdate;

  PaymentConfigService({
    required LoggingService loggingService,
    FlutterSecureStorage? secureStorage,
  })  : _loggingService = loggingService,
        _secureStorage = secureStorage ?? const FlutterSecureStorage();

  /// Initialize the configuration service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadEnvironmentSettings();
      await _loadCachedConfigurations();
      
      _isInitialized = true;

      _loggingService.info(
        'PaymentConfigService',
        'Configuration service initialized',
        {
          'environment': _currentEnvironment.name,
          'cached_providers': _providerConfigs.keys.map((p) => p.name).toList(),
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'PaymentConfigService',
        'Failed to initialize configuration service',
        {'error': e.toString()},
        stackTrace,
      );
      rethrow;
    }
  }

  /// Get provider configuration for a specific provider
  PaymentProviderConfiguration? getProviderConfig(PaymentProvider provider) {
    if (!_isInitialized) {
      throw PaymentException(
        'Configuration service not initialized',
        code: 'CONFIG_NOT_INITIALIZED',
      );
    }

    return _providerConfigs[provider];
  }

  /// Get all available provider configurations
  Map<PaymentProvider, PaymentProviderConfiguration> getAllProviderConfigs() {
    if (!_isInitialized) {
      throw PaymentException(
        'Configuration service not initialized',
        code: 'CONFIG_NOT_INITIALIZED',
      );
    }

    return Map.unmodifiable(_providerConfigs);
  }

  /// Update provider configurations from backend
  Future<void> updateProviderConfigurations(
    Map<PaymentProvider, PaymentProviderConfiguration> configs,
  ) async {
    if (!_isInitialized) {
      throw PaymentException(
        'Configuration service not initialized',
        code: 'CONFIG_NOT_INITIALIZED',
      );
    }

    try {
      _providerConfigs = Map.from(configs);
      _lastConfigUpdate = DateTime.now();

      // Cache configurations securely
      await _cacheConfigurations();

      _loggingService.info(
        'PaymentConfigService',
        'Provider configurations updated',
        {
          'providers': configs.keys.map((p) => p.name).toList(),
          'environment': _currentEnvironment.name,
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'PaymentConfigService',
        'Failed to update provider configurations',
        {'error': e.toString()},
        stackTrace,
      );
      rethrow;
    }
  }

  /// Get current payment environment
  PaymentEnvironment get currentEnvironment => _currentEnvironment;

  /// Set payment environment (development/staging/production)
  Future<void> setEnvironment(PaymentEnvironment environment) async {
    if (_currentEnvironment == environment) return;

    try {
      _currentEnvironment = environment;
      await _prefs.setString(_environmentKey, environment.name);

      // Clear cached configurations when environment changes
      _providerConfigs.clear();
      await _clearCachedConfigurations();

      _loggingService.info(
        'PaymentConfigService',
        'Payment environment changed',
        {'new_environment': environment.name},
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'PaymentConfigService',
        'Failed to set payment environment',
        {'environment': environment.name, 'error': e.toString()},
        stackTrace,
      );
      rethrow;
    }
  }

  /// Check if configurations need refresh
  bool get needsConfigRefresh {
    if (_lastConfigUpdate == null) return true;
    return DateTime.now().difference(_lastConfigUpdate!) > _configCacheExpiry;
  }

  /// Get API base URL for current environment
  String get apiBaseUrl {
    switch (_currentEnvironment) {
      case PaymentEnvironment.development:
        return 'https://api.dev.cultureconnect.com';
      case PaymentEnvironment.staging:
        return 'https://api.staging.cultureconnect.com';
      case PaymentEnvironment.production:
        return 'https://api.cultureconnect.com';
    }
  }

  /// Get WebSocket URL for current environment
  String get websocketUrl {
    switch (_currentEnvironment) {
      case PaymentEnvironment.development:
        return 'wss://ws.dev.cultureconnect.com';
      case PaymentEnvironment.staging:
        return 'wss://ws.staging.cultureconnect.com';
      case PaymentEnvironment.production:
        return 'wss://ws.cultureconnect.com';
    }
  }

  /// Load environment settings from storage
  Future<void> _loadEnvironmentSettings() async {
    try {
      final environmentName = _prefs.getString(_environmentKey);
      if (environmentName != null) {
        _currentEnvironment = PaymentEnvironment.values.firstWhere(
          (env) => env.name == environmentName,
          orElse: () => kDebugMode 
              ? PaymentEnvironment.development 
              : PaymentEnvironment.production,
        );
      } else {
        // Set default environment based on build mode
        _currentEnvironment = kDebugMode 
            ? PaymentEnvironment.development 
            : PaymentEnvironment.production;
        await _prefs.setString(_environmentKey, _currentEnvironment.name);
      }
    } catch (e) {
      _loggingServicearning(
        'PaymentConfigService',
        'Failed to load environment settings, using default',
        {'error': e.toString()},
      );
      _currentEnvironment = kDebugMode 
          ? PaymentEnvironment.development 
          : PaymentEnvironment.production;
    }
  }

  /// Load cached configurations from secure storage
  Future<void> _loadCachedConfigurations() async {
    try {
      final cachedData = await _secureStorageead(key: _configCacheKey);
      if (cachedData == null) return;

      final Map<String, dynamic> data = jsonDecode(cachedData);
      final cacheTime = DateTime.parse(data['timestamp']);
      
      // Check if cache is still valid
      if (DateTime.now().difference(cacheTime) > _configCacheExpiry) {
        await _clearCachedConfigurations();
        return;
      }

      // Load configurations
      final configs = data['configurations'] as Map<String, dynamic>;
      _providerConfigs = configs.map((key, value) {
        final provider = PaymentProvider.values.firstWhere(
          (p) => p.name == key,
        );
        return MapEntry(
          provider,
          PaymentProviderConfiguration.fromJson(value),
        );
      });

      _lastConfigUpdate = cacheTime;

      _loggingService.info(
        'PaymentConfigService',
        'Loaded cached configurations',
        {'providers': _providerConfigs.keys.map((p) => p.name).toList()},
      );
    } catch (e) {
      _loggingServicearning(
        'PaymentConfigService',
        'Failed to load cached configurations',
        {'error': e.toString()},
      );
      await _clearCachedConfigurations();
    }
  }

  /// Cache configurations securely
  Future<void> _cacheConfigurations() async {
    try {
      final data = {
        'timestamp': DateTime.now().toIso8601String(),
        'configurations': _providerConfigs.map(
          (provider, config) => MapEntry(provider.name, config.toJson()),
        ),
      };

      await _secureStoragerite(
        key: _configCacheKey,
        value: jsonEncode(data),
      );
    } catch (e) {
      _loggingServicearning(
        'PaymentConfigService',
        'Failed to cache configurations',
        {'error': e.toString()},
      );
    }
  }

  /// Clear cached configurations
  Future<void> _clearCachedConfigurations() async {
    try {
      await _secureStorage.delete(key: _configCacheKey);
      _providerConfigs.clear();
      _lastConfigUpdate = null;
    } catch (e) {
      _loggingServicearning(
        'PaymentConfigService',
        'Failed to clear cached configurations',
        {'error': e.toString()},
      );
    }
  }

  /// Dispose resources
  void dispose() {
    _isInitialized = false;
    _providerConfigs.clear();
  }
}

/// Payment environment enumeration
enum PaymentEnvironment {
  development,
  staging,
  production,
}

/// Payment provider configuration model
class PaymentProviderConfiguration {
  final PaymentProvider provider;
  final String publicKey;
  final String? merchantId;
  final String? merchantDisplayName;
  final bool isEnabled;
  final Map<String, dynamic> additionalConfig;

  const PaymentProviderConfiguration({
    required this.provider,
    required this.publicKey,
    this.merchantId,
    this.merchantDisplayName,
    this.isEnabled = true,
    this.additionalConfig = const {},
  });

  factory PaymentProviderConfiguration.fromJson(Map<String, dynamic> json) {
    return PaymentProviderConfiguration(
      provider: PaymentProvider.values.firstWhere(
        (p) => p.name == json['provider'],
      ),
      publicKey: json['public_key'],
      merchantId: json['merchant_id'],
      merchantDisplayName: json['merchant_display_name'],
      isEnabled: json['is_enabled'] ?? true,
      additionalConfig: Map<String, dynamic>.from(
        json['additional_config'] ?? {},
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'provider': provider.name,
      'public_key': publicKey,
      if (merchantId != null) 'merchant_id': merchantId,
      if (merchantDisplayName != null) 'merchant_display_name': merchantDisplayName,
      'is_enabled': isEnabled,
      'additional_config': additionalConfig,
    };
  }
}
