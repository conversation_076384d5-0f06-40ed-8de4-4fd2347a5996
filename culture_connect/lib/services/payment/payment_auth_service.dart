import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/services/auth_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/utils/exceptions/payment_exceptions.dart';

/// Service for managing payment-specific authentication and user context
class PaymentAuthService {
  static const String _paymentTokenKey = 'payment_auth_token';
  static const String _userContextKey = 'payment_user_context';
  static const Duration _tokenRefreshThreshold = Duration(minutes: 5);

  final AuthService _authService;
  final LoggingService _loggingService;
  final FlutterSecureStorage _secureStorage;

  bool _isInitialized = false;
  String? _currentPaymentToken;
  PaymentUserContext? _userContext;
  Timer? _tokenRefreshTimer;

  PaymentAuthService({
    required AuthService authService,
    required LoggingService loggingService,
    FlutterSecureStorage? secureStorage,
  })  : _authService = authService,
        _loggingService = loggingService,
        _secureStorage = secureStorage ?? const FlutterSecureStorage();

  /// Initialize the payment authentication service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadStoredToken();
      await _loadUserContext();
      _setupTokenRefreshTimer();
      
      _isInitialized = true;

      _loggingService.info(
        'PaymentAuthService',
        'Payment authentication service initialized',
        {
          'has_token': _currentPaymentToken != null,
          'has_user_context': _userContext != null,
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'PaymentAuthService',
        'Failed to initialize payment authentication service',
        {'error': e.toString()},
        stackTrace,
      );
      rethrow;
    }
  }

  /// Get current payment authentication token
  Future<String?> getPaymentToken() async {
    if (!_isInitialized) {
      throw PaymentException(
        'Payment authentication service not initialized',
        code: 'PAYMENT_AUTH_NOT_INITIALIZED',
      );
    }

    // Check if token needs refresh
    if (_currentPaymentToken != null && _isTokenExpiringSoon(_currentPaymentToken!)) {
      await _refreshPaymentToken();
    }

    return _currentPaymentToken;
  }

  /// Get current user context for payments
  PaymentUserContext? get userContext => _userContext;

  /// Authenticate user for payment operations
  Future<PaymentAuthResult> authenticateForPayment({
    required double amount,
    required String currency,
    String? bookingId,
  }) async {
    if (!_isInitialized) {
      throw PaymentException(
        'Payment authentication service not initialized',
        code: 'PAYMENT_AUTH_NOT_INITIALIZED',
      );
    }

    try {
      // Check if user is authenticated with main auth service
      final currentUser = await _authService.currentUserModel;
      if (currentUser == null) {
        return PaymentAuthResult(
          success: false,
          errorMessage: 'User not authenticated',
          requiresLogin: true,
        );
      }

      // Generate payment-specific token
      await _generatePaymentToken(currentUser, amount, currency, bookingId);

      // Update user context
      await _updateUserContext(currentUser);

      _loggingService.info(
        'PaymentAuthService',
        'User authenticated for payment',
        {
          'user_id': currentUser.id,
          'amount': amount,
          'currency': currency,
          'booking_id': bookingId,
        },
      );

      return PaymentAuthResult(
        success: true,
        paymentToken: _currentPaymentToken!,
        userContext: _userContext!,
      );

    } catch (e, stackTrace) {
      _loggingService.error(
        'PaymentAuthService',
        'Failed to authenticate user for payment',
        {
          'amount': amount,
          'currency': currency,
          'error': e.toString(),
        },
        stackTrace,
      );

      return PaymentAuthResult(
        success: false,
        errorMessage: 'Authentication failed: $e',
      );
    }
  }

  /// Refresh payment token
  Future<void> _refreshPaymentToken() async {
    try {
      final currentUser = await _authService.currentUserModel;
      if (currentUser == null) {
        await _clearPaymentAuth();
        return;
      }

      // TODO: Backend Integration Required
      // Call backend to refresh payment token
      // final response = await _apiServiceefreshPaymentToken();
      
      // Mock implementation for now
      final mockToken = _generateMockPaymentToken(currentUser);
      await _storePaymentToken(mockToken);

      _loggingService.info(
        'PaymentAuthService',
        'Payment token refreshed',
        {'user_id': currentUser.id},
      );

    } catch (e, stackTrace) {
      _loggingService.error(
        'PaymentAuthService',
        'Failed to refresh payment token',
        {'error': e.toString()},
        stackTrace,
      );
      
      // Clear invalid token
      await _clearPaymentAuth();
    }
  }

  /// Generate payment-specific token
  Future<void> _generatePaymentToken(
    UserModel user,
    double amount,
    String currency,
    String? bookingId,
  ) async {
    try {
      // TODO: Backend Integration Required
      // Call backend to generate payment token with specific permissions
      // final response = await _apiService.generatePaymentToken({
      //   'user_id': user.id,
      //   'amount': amount,
      //   'currency': currency,
      //   'booking_id': bookingId,
      //   'permissions': ['payment_process', 'payment_verify'],
      // });

      // Mock implementation for now
      final mockToken = _generateMockPaymentToken(user, amount, currency, bookingId);
      await _storePaymentToken(mockToken);

    } catch (e, stackTrace) {
      _loggingService.error(
        'PaymentAuthService',
        'Failed to generate payment token',
        {'user_id': user.id, 'error': e.toString()},
        stackTrace,
      );
      rethrow;
    }
  }

  /// Update user context for payments
  Future<void> _updateUserContext(UserModel user) async {
    try {
      _userContext = PaymentUserContext(
        userId: user.id,
        email: user.email,
        fullName: user.fullName,
        phoneNumber: user.phoneNumber,
        isVerified: user.isVerified,
        verificationLevel: user.verificationLevel,
        preferredCurrency: 'USD', // TODO: Get from user preferences
        countryCode: 'US', // TODO: Get from user location or preferences
      );

      // Cache user context
      await _secureStoragerite(
        key: _userContextKey,
        value: jsonEncode(_userContext!.toJson()),
      );

    } catch (e, stackTrace) {
      _loggingService.error(
        'PaymentAuthService',
        'Failed to update user context',
        {'user_id': user.id, 'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Load stored payment token
  Future<void> _loadStoredToken() async {
    try {
      _currentPaymentToken = await _secureStorageead(key: _paymentTokenKey);
      
      // Validate token if exists
      if (_currentPaymentToken != null && _isTokenExpired(_currentPaymentToken!)) {
        await _clearPaymentAuth();
      }
    } catch (e) {
      _loggingServicearning(
        'PaymentAuthService',
        'Failed to load stored payment token',
        {'error': e.toString()},
      );
      await _clearPaymentAuth();
    }
  }

  /// Load user context from storage
  Future<void> _loadUserContext() async {
    try {
      final contextData = await _secureStorageead(key: _userContextKey);
      if (contextData != null) {
        final json = jsonDecode(contextData);
        _userContext = PaymentUserContext.fromJson(json);
      }
    } catch (e) {
      _loggingServicearning(
        'PaymentAuthService',
        'Failed to load user context',
        {'error': e.toString()},
      );
    }
  }

  /// Store payment token securely
  Future<void> _storePaymentToken(String token) async {
    try {
      _currentPaymentToken = token;
      await _secureStoragerite(key: _paymentTokenKey, value: token);
    } catch (e) {
      _loggingService.error(
        'PaymentAuthService',
        'Failed to store payment token',
        {'error': e.toString()},
      );
    }
  }

  /// Clear payment authentication data
  Future<void> _clearPaymentAuth() async {
    try {
      _currentPaymentToken = null;
      _userContext = null;
      await _secureStorage.delete(key: _paymentTokenKey);
      await _secureStorage.delete(key: _userContextKey);
    } catch (e) {
      _loggingServicearning(
        'PaymentAuthService',
        'Failed to clear payment auth data',
        {'error': e.toString()},
      );
    }
  }

  /// Check if token is expired
  bool _isTokenExpired(String token) {
    try {
      return JwtDecoder.isExpired(token);
    } catch (e) {
      return true; // Treat invalid tokens as expired
    }
  }

  /// Check if token is expiring soon
  bool _isTokenExpiringSoon(String token) {
    try {
      final expiryDate = JwtDecoder.getExpirationDate(token);
      final timeUntilExpiry = expiryDate.difference(DateTime.now());
      return timeUntilExpiry <= _tokenRefreshThreshold;
    } catch (e) {
      return true; // Treat invalid tokens as expiring
    }
  }

  /// Setup automatic token refresh timer
  void _setupTokenRefreshTimer() {
    _tokenRefreshTimer?.cancel();
    _tokenRefreshTimer = Timer.periodic(
      const Duration(minutes: 1),
      (_) async {
        if (_currentPaymentToken != null && _isTokenExpiringSoon(_currentPaymentToken!)) {
          await _refreshPaymentToken();
        }
      },
    );
  }

  /// Generate mock payment token (for development)
  String _generateMockPaymentToken(
    UserModel user, [
    double? amount,
    String? currency,
    String? bookingId,
  ]) {
    final header = base64Encode(utf8.encode(jsonEncode({
      'alg': 'HS256',
      'typ': 'JWT',
    })));

    final payload = base64Encode(utf8.encode(jsonEncode({
      'sub': user.id,
      'email': user.email,
      'iat': DateTime.now().millisecondsSinceEpoch ~/ 1000,
      'exp': DateTime.now().add(const Duration(hours: 1)).millisecondsSinceEpoch ~/ 1000,
      'scope': ['payment_process', 'payment_verify'],
      'payment_context': {
        if (amount != null) 'amount': amount,
        if (currency != null) 'currency': currency,
        if (bookingId != null) 'booking_id': bookingId,
      },
    })));

    final signature = base64Encode(utf8.encode('mock_signature'));
    return '$header.$payload.$signature';
  }

  /// Dispose resources
  void dispose() {
    _tokenRefreshTimer?.cancel();
    _isInitialized = false;
    _currentPaymentToken = null;
    _userContext = null;
  }
}

/// Payment authentication result
class PaymentAuthResult {
  final bool success;
  final String? paymentToken;
  final PaymentUserContext? userContext;
  final String? errorMessage;
  final bool requiresLogin;

  const PaymentAuthResult({
    required this.success,
    this.paymentToken,
    this.userContext,
    this.errorMessage,
    thisequiresLogin = false,
  });
}

/// Payment user context
class PaymentUserContext {
  final String userId;
  final String email;
  final String fullName;
  final String phoneNumber;
  final bool isVerified;
  final int verificationLevel;
  final String preferredCurrency;
  final String countryCode;

  const PaymentUserContext({
    required this.userId,
    required this.email,
    required this.fullName,
    required this.phoneNumber,
    required this.isVerified,
    required this.verificationLevel,
    required this.preferredCurrency,
    required this.countryCode,
  });

  factory PaymentUserContext.fromJson(Map<String, dynamic> json) {
    return PaymentUserContext(
      userId: json['user_id'],
      email: json['email'],
      fullName: json['full_name'],
      phoneNumber: json['phone_number'],
      isVerified: json['is_verified'],
      verificationLevel: json['verification_level'],
      preferredCurrency: json['preferred_currency'],
      countryCode: json['country_code'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'email': email,
      'full_name': fullName,
      'phone_number': phoneNumber,
      'is_verified': isVerified,
      'verification_level': verificationLevel,
      'preferred_currency': preferredCurrency,
      'country_code': countryCode,
    };
  }
}
