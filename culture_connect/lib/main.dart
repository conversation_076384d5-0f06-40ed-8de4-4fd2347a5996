import 'dart:async';

import 'package:culture_connect/screens/verification_screen.dart';
import 'package:culture_connect/screens/verification/user_verification_level_screen.dart';
import 'package:culture_connect/screens/verification/background_check_screen.dart';
import 'package:culture_connect/screens/verification/verification_request_screen.dart';
import 'package:culture_connect/screens/settings/refresh_animation_settings_screen.dart';
import 'package:culture_connect/screens/main_navigation.dart';
import 'package:culture_connect/screens/splash_screen.dart';
import 'package:culture_connect/screens/registration_screen.dart';
import 'package:culture_connect/screens/login_screen.dart';
import 'package:culture_connect/screens/booking_management_screen.dart';
import 'package:culture_connect/screens/loyalty/loyalty_dashboard_screen.dart';
import 'package:culture_connect/screens/loyalty/loyalty_points_history_screen.dart';
import 'package:culture_connect/screens/loyalty/loyalty_rewards_screen.dart';
import 'package:culture_connect/screens/travel/price_alerts_screen.dart';
import 'package:culture_connect/screens/voice_translation/voice_translation_screen.dart';
import 'package:culture_connect/screens/travel/itinerary/itinerary_builder_screen.dart';
import 'package:culture_connect/screens/travel/timeline/timeline_screen.dart';

import 'package:culture_connect/screens/settings/offline_settings_screen.dart';
import 'package:culture_connect/screens/offline/offline_content_management_screen.dart';
import 'package:culture_connect/screens/translation/translation_feedback_screen.dart';
import 'package:culture_connect/screens/translation/cultural_context_settings_screen.dart';
import 'package:culture_connect/screens/translation/slang_idiom_settings_screen.dart';
import 'package:culture_connect/screens/translation/pronunciation_settings_screen.dart';
import 'package:culture_connect/screens/messaging/group_translation_settings_screen.dart';
import 'package:culture_connect/screens/voice_translation/custom_vocabulary_screen.dart';
import 'package:culture_connect/screens/voice_translation/custom_vocabulary_form_screen.dart';
import 'package:culture_connect/screens/voice_translation/custom_vocabulary_detail_screen.dart';
import 'package:culture_connect/screens/translation/image_text_translation_screen.dart';
import 'package:culture_connect/screens/translation/image_text_translation_history_screen.dart';
import 'package:culture_connect/screens/settings/rtl_settings_screen.dart';
import 'package:culture_connect/screens/currency/currency_conversion_screen.dart';
import 'package:culture_connect/screens/currency/currency_preferences_screen.dart';
import 'package:culture_connect/models/translation/custom_vocabulary_model.dart';
import 'package:culture_connect/models/verification_model.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/providers/preferences_provider.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart'
    as sp;
import 'package:culture_connect/routes/insurance_routes.dart';

import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:culture_connect/services/startup_optimization_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/error_handling_service.dart';
import 'package:culture_connect/services/analytics_service.dart';
import 'package:culture_connect/services/performance_monitoring_service.dart';
import 'package:culture_connect/services/crash_reporting_service.dart';
import 'package:flutter/material.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

Future<void> main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive for local storage
  await Hive.initFlutter();
  await Hive.openBox<String>('messages');
  await Hive.openBox<String>('pending_messages');
  await Hive.openBox<String>('exchange_rate_cache');
  await Hive.openBox<String>('historical_rate_cache');

  // Initialize the app with optimized startup
  final startupService = StartupOptimizationService();

  // Start initialization process
  unawaited(startupService.initializeApp());

  // Get SharedPreferences instance
  final sharedPreferences = await SharedPreferences.getInstance();

  // Create a container for initializing services
  final container = ProviderContainer(
    overrides: [
      // Override the sharedPreferencesProvider with the instance
      sp.sharedPreferencesProvider.overrideWithValue(sharedPreferences),
    ],
  );

  // Initialize services
  await _initializeServices(container);

  // Run the app
  runApp(
    UncontrolledProviderScope(
      container: container,
      child: const CultureConnectApp(),
    ),
  );
}

/// Initialize all services
Future<void> _initializeServices(ProviderContainer container) async {
  try {
    // Initialize logging service first
    final loggingService = container.read(loggingServiceProvider);
    await loggingService.initialize();

    // Initialize error handling service
    final errorHandlingService = container.read(errorHandlingServiceProvider);
    await errorHandlingService.initialize();

    // Initialize crash reporting service
    final crashReportingService = container.read(crashReportingServiceProvider);
    await crashReportingService.initialize();

    // Initialize analytics service
    final analyticsService = container.read(analyticsServiceProvider);
    await analyticsService.initialize();

    // Initialize performance monitoring service
    final performanceMonitoringService =
        container.read(performanceMonitoringServiceProvider);
    await performanceMonitoringService.initialize();

    // Log successful initialization
    loggingService.info('App', 'All services initialized successfully');
  } catch (e, stackTrace) {
    // If logging service fails, fall back to print
    debugPrint('Error initializing services: $e');
    debugPrint('Stack trace: $stackTrace');
  }
}

class CultureConnectApp extends ConsumerWidget {
  const CultureConnectApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use the theme mode from preferences provider
    final themeMode = ref.watch(themeModeProvider);

    return MaterialApp(
      title: 'CultureConnect',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeMode,
      // Set the default locale
      locale: const Locale('en', 'US'),
      // Add support for RTL languages
      localizationsDelegates: const [
        GlobalWidgetsLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en', 'US'), // English
        Locale('ar', 'EG'), // Arabic (Egypt)
        Locale('ar', 'SA'), // Arabic (Saudi Arabia)
        Locale('he', 'IL'), // Hebrew
        Locale('fa', 'IR'), // Persian
      ],
      home: const SplashScreen(),
      routes: {
        '/login': (context) => const LoginScreen(),
        '/register': (context) => const RegistrationScreen(),
        '/verify': (context) => const VerificationScreen(),
        '/home': (context) => const MainNavigation(),
        '/booking-management': (context) => const BookingManagementScreen(),
        '/price-alerts': (context) => const PriceAlertsScreen(),
        '/itinerary/builder': (context) => const ItineraryBuilderScreen(),
        '/timeline': (context) {
          // Get the timeline ID from arguments
          final args = ModalRoute.of(context)!.settings.arguments
              as Map<String, dynamic>?;
          final timelineId = args?['timelineId'] as String? ?? '';
          return TimelineScreen(timelineId: timelineId);
        },

        '/offline/settings': (context) => const OfflineSettingsScreen(),
        '/offline/content': (context) => const OfflineContentManagementScreen(),
        '/loyalty': (context) => const LoyaltyDashboardScreen(),
        '/loyalty/points': (context) {
          // Get the loyalty program from arguments
          final args = ModalRoute.of(context)!.settings.arguments
              as Map<String, dynamic>?;
          final loyaltyProgram = args?['loyaltyProgram'];
          return LoyaltyPointsHistoryScreen(loyaltyProgram: loyaltyProgram);
        },
        '/loyalty/rewards': (context) {
          // Get the loyalty program from arguments
          final args = ModalRoute.of(context)!.settings.arguments
              as Map<String, dynamic>?;
          final loyaltyProgram = args?['loyaltyProgram'];
          return LoyaltyRewardsScreen(loyaltyProgram: loyaltyProgram);
        },
        '/voice-translation': (context) => const VoiceTranslationScreen(),
        '/translation_feedback': (context) => const TranslationFeedbackScreen(),
        '/cultural_context_settings': (context) =>
            const CulturalContextSettingsScreen(),
        '/slang_idiom_settings': (context) => const SlangIdiomSettingsScreen(),
        '/pronunciation_settings': (context) =>
            const PronunciationSettingsScreen(),
        '/group_translation_settings': (context) {
          final args = ModalRoute.of(context)!.settings.arguments as String;
          return GroupTranslationSettingsScreen(groupId: args);
        },
        '/custom-vocabulary': (context) => const CustomVocabularyScreen(),
        '/custom-vocabulary/add': (context) =>
            const CustomVocabularyFormScreen(),
        '/custom-vocabulary/edit': (context) {
          final term = ModalRoute.of(context)!.settings.arguments
              as CustomVocabularyModel;
          return CustomVocabularyFormScreen(term: term);
        },
        '/custom-vocabulary/view': (context) {
          final term = ModalRoute.of(context)!.settings.arguments
              as CustomVocabularyModel;
          return CustomVocabularyDetailScreen(term: term);
        },
        '/image-text-translation': (context) =>
            const ImageTextTranslationScreen(),
        '/image-text-translation/history': (context) =>
            const ImageTextTranslationHistoryScreen(),
        '/rtl-settings': (context) => const RTLSettingsScreen(),
        '/currency-converter': (context) => const CurrencyConversionScreen(),
        '/currency-preferences': (context) => const CurrencyPreferencesScreen(),
        '/verification/levels': (context) =>
            const UserVerificationLevelScreen(),
        '/verification/background-check': (context) =>
            const BackgroundCheckScreen(),
        '/verification/request': (context) {
          final type =
              ModalRoute.of(context)!.settings.arguments as VerificationType;
          return VerificationRequestScreen(verificationType: type);
        },
        '/settings/refresh-animations': (context) =>
            const RefreshAnimationSettingsScreen(),

        // Add insurance routes
        ...insuranceRoutes,
      },
    );
  }
}
