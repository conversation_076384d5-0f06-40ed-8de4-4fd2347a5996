import 'package:cloud_firestore/cloud_firestore.dart';

/// Enum representing the different types of verification
enum VerificationType {
  /// Identity verification (ID card, passport)
  identity,

  /// Professional certification
  professional,

  /// Background check
  background,

  /// Address verification
  address,

  /// Phone verification
  phone,

  /// Email verification
  email,

  /// Social media verification
  social,

  /// Government license
  license,
}

/// Enum representing the status of a verification
enum VerificationStatus {
  /// Verification is pending review
  pending,

  /// Verification is approved
  approved,

  /// Verification is rejected
  rejected,

  /// Verification is expired
  expired,
}

/// Model representing a verification badge for a guide
class VerificationBadge {
  /// Unique identifier for the verification badge
  final String id;

  /// ID of the user this badge belongs to
  final String userId;

  /// Type of verification
  final VerificationType type;

  /// Status of the verification
  final VerificationStatus status;

  /// Name of the verification badge
  final String name;

  /// Description of the verification badge
  final String description;

  /// Icon URL for the verification badge
  final String? iconUrl;

  /// Date when the verification was issued
  final DateTime issuedAt;

  /// Date when the verification expires (if applicable)
  final DateTime? expiresAt;

  /// ID of the admin who approved the verification
  final String? approvedBy;

  /// Date when the verification was approved
  final DateTime? approvedAt;

  /// Rejection reason (if applicable)
  final String? rejectionReason;

  /// Additional metadata for the verification
  final Map<String, dynamic>? metadata;

  /// Creates a new verification badge
  const VerificationBadge({
    required this.id,
    required this.userId,
    required this.type,
    required this.status,
    required this.name,
    required this.description,
    this.iconUrl,
    required this.issuedAt,
    this.expiresAt,
    this.approvedBy,
    this.approvedAt,
    this.rejectionReason,
    this.metadata,
  });

  /// Creates a verification badge from a JSON map
  factory VerificationBadge.fromJson(Map<String, dynamic> json) {
    return VerificationBadge(
      id: json['id'] as String,
      userId: json['userId'] as String,
      type: VerificationType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => VerificationType.identity,
      ),
      status: VerificationStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => VerificationStatus.pending,
      ),
      name: json['name'] as String,
      description: json['description'] as String,
      iconUrl: json['iconUrl'] as String?,
      issuedAt: (json['issuedAt'] as Timestamp).toDate(),
      expiresAt: json['expiresAt'] != null
          ? (json['expiresAt'] as Timestamp).toDate()
          : null,
      approvedBy: json['approvedBy'] as String?,
      approvedAt: json['approvedAt'] != null
          ? (json['approvedAt'] as Timestamp).toDate()
          : null,
      rejectionReason: json['rejectionReason'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Converts this verification badge to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'name': name,
      'description': description,
      'iconUrl': iconUrl,
      'issuedAt': Timestamp.fromDate(issuedAt),
      if (expiresAt != null) 'expiresAt': Timestamp.fromDate(expiresAt!),
      'approvedBy': approvedBy,
      if (approvedAt != null) 'approvedAt': Timestamp.fromDate(approvedAt!),
      'rejectionReason': rejectionReason,
      'metadata': metadata,
    };
  }

  /// Creates a copy of this verification badge with the given fields replaced with the new values
  VerificationBadge copyWith({
    String? id,
    String? userId,
    VerificationType? type,
    VerificationStatus? status,
    String? name,
    String? description,
    String? iconUrl,
    DateTime? issuedAt,
    DateTime? expiresAt,
    String? approvedBy,
    DateTime? approvedAt,
    String? rejectionReason,
    Map<String, dynamic>? metadata,
  }) {
    return VerificationBadge(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      status: status ?? this.status,
      name: name ?? this.name,
      description: description ?? this.description,
      iconUrl: iconUrl ?? this.iconUrl,
      issuedAt: issuedAt ?? this.issuedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      approvedBy: approvedBy ?? this.approvedBy,
      approvedAt: approvedAt ?? this.approvedAt,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Returns whether this verification badge is valid (approved and not expired)
  bool get isValid {
    if (status != VerificationStatus.approved) return false;
    if (expiresAt != null && expiresAt!.isBefore(DateTime.now())) return false;
    return true;
  }

  /// Returns the display name for this verification type
  String get typeDisplayName {
    switch (type) {
      case VerificationType.identity:
        return 'Identity Verified';
      case VerificationType.professional:
        return 'Professional Certification';
      case VerificationType.background:
        return 'Background Checked';
      case VerificationType.address:
        return 'Address Verified';
      case VerificationType.phone:
        return 'Phone Verified';
      case VerificationType.email:
        return 'Email Verified';
      case VerificationType.social:
        return 'Social Media Verified';
      case VerificationType.license:
        return 'Licensed Guide';
    }
  }

  /// Returns the display name for this verification status
  String get statusDisplayName {
    switch (status) {
      case VerificationStatus.pending:
        return 'Pending';
      case VerificationStatus.approved:
        return 'Approved';
      case VerificationStatus.rejected:
        return 'Rejected';
      case VerificationStatus.expired:
        return 'Expired';
    }
  }
}

/// Model representing a verification request
class VerificationRequest {
  /// Unique identifier for the verification request
  final String id;

  /// ID of the user who submitted the request
  final String userId;

  /// Type of verification requested
  final VerificationType type;

  /// Status of the verification request
  final VerificationStatus status;

  /// Date when the request was submitted
  final DateTime submittedAt;

  /// Date when the request was processed
  final DateTime? processedAt;

  /// ID of the admin who processed the request
  final String? processedBy;

  /// Rejection reason (if applicable)
  final String? rejectionReason;

  /// Document URLs submitted for verification
  final List<String> documentUrls;

  /// Additional notes from the user
  final String? notes;

  /// Additional metadata for the request
  final Map<String, dynamic>? metadata;

  /// Creates a new verification request
  const VerificationRequest({
    required this.id,
    required this.userId,
    required this.type,
    required this.status,
    required this.submittedAt,
    this.processedAt,
    this.processedBy,
    this.rejectionReason,
    required this.documentUrls,
    this.notes,
    this.metadata,
  });

  /// Creates a verification request from a JSON map
  factory VerificationRequest.fromJson(Map<String, dynamic> json) {
    return VerificationRequest(
      id: json['id'] as String,
      userId: json['userId'] as String,
      type: VerificationType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => VerificationType.identity,
      ),
      status: VerificationStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => VerificationStatus.pending,
      ),
      submittedAt: (json['submittedAt'] as Timestamp).toDate(),
      processedAt: json['processedAt'] != null
          ? (json['processedAt'] as Timestamp).toDate()
          : null,
      processedBy: json['processedBy'] as String?,
      rejectionReason: json['rejectionReason'] as String?,
      documentUrls: List<String>.from(json['documentUrls'] as List),
      notes: json['notes'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Converts this verification request to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'submittedAt': Timestamp.fromDate(submittedAt),
      if (processedAt != null) 'processedAt': Timestamp.fromDate(processedAt!),
      'processedBy': processedBy,
      'rejectionReason': rejectionReason,
      'documentUrls': documentUrls,
      'notes': notes,
      'metadata': metadata,
    };
  }
}
