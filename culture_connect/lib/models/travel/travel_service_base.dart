import 'package:flutter/material.dart';
import 'package:culture_connect/models/location/geo_location.dart';

/// Base class for all travel services
abstract class TravelService {
  /// Unique identifier for the travel service
  final String id;

  /// Name of the travel service
  final String name;

  /// Description of the travel service
  final String description;

  /// Price of the travel service
  final double price;

  /// Currency of the price
  final String currency;

  /// Rating of the travel service (0-5)
  final double rating;

  /// Number of reviews
  final int reviewCount;

  /// URL to the image of the travel service
  final String imageUrl;

  /// List of additional images
  final List<String> additionalImages;

  /// Provider of the travel service
  final String provider;

  /// Location of the travel service
  final String location;

  /// Coordinates of the travel service
  final GeoLocation coordinates;

  /// Whether the travel service is available
  final bool isAvailable;

  /// Whether the travel service is featured
  final bool isFeatured;

  /// Whether the travel service is on sale
  final bool isOnSale;

  /// Original price before sale
  final double? originalPrice;

  /// Discount percentage
  final double? discountPercentage;

  /// Tags associated with the travel service
  final List<String> tags;

  /// Amenities included with the travel service
  final List<String> amenities;

  /// Cancellation policy
  final String cancellationPolicy;

  /// When the travel service was created
  final DateTime createdAt;

  /// When the travel service was last updated
  final DateTime updatedAt;

  /// Creates a new travel service
  const TravelService({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.currency,
    required this.rating,
    required this.reviewCount,
    required this.imageUrl,
    required this.additionalImages,
    required this.provider,
    required this.location,
    required this.coordinates,
    required this.isAvailable,
    required this.isFeatured,
    required this.isOnSale,
    this.originalPrice,
    this.discountPercentage,
    required this.tags,
    required this.amenities,
    required this.cancellationPolicy,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Get the formatted price
  String get formattedPrice {
    return '$currency${price.toStringAsFixed(2)}';
  }

  /// Get the formatted original price
  String? get formattedOriginalPrice {
    if (originalPrice == null) return null;
    return '$currency${originalPrice!.toStringAsFixed(2)}';
  }

  /// Get the discount amount
  double? get discountAmount {
    if (originalPrice == null) return null;
    return originalPrice! - price;
  }

  /// Get the formatted discount amount
  String? get formattedDiscountAmount {
    if (discountAmount == null) return null;
    return '$currency${discountAmount!.toStringAsFixed(2)}';
  }

  /// Get the icon for the travel service
  IconData get icon;

  /// Get the color for the travel service
  Color get color;
}

/// Enum for travel service types
enum TravelServiceType {
  carRental,
  privateSecurity,
  hotel,
  restaurant,
  flight,
  cruise,
  insurance,
  visa,
}

/// Extension for travel service types
extension TravelServiceTypeExtension on TravelServiceType {
  /// Get the display name for the travel service type
  String get displayName {
    switch (this) {
      case TravelServiceType.carRental:
        return 'Car Rental';
      case TravelServiceType.privateSecurity:
        return 'Private Security';
      case TravelServiceType.hotel:
        return 'Hotel';
      case TravelServiceType.restaurant:
        return 'Restaurant';
      case TravelServiceType.flight:
        return 'Flight';
      case TravelServiceType.cruise:
        return 'Cruise';
      case TravelServiceType.insurance:
        return 'Travel Insurance';
      case TravelServiceType.visa:
        return 'Visa Services';
    }
  }

  /// Get the icon for the travel service type
  IconData get icon {
    switch (this) {
      case TravelServiceType.carRental:
        return Icons.directions_car;
      case TravelServiceType.privateSecurity:
        return Icons.security;
      case TravelServiceType.hotel:
        return Icons.hotel;
      case TravelServiceType.restaurant:
        return Icons.restaurant;
      case TravelServiceType.flight:
        return Icons.flight;
      case TravelServiceType.cruise:
        return Icons.directions_boat;
      case TravelServiceType.insurance:
        return Icons.shield;
      case TravelServiceType.visa:
        return Icons.assignment;
    }
  }

  /// Get the color for the travel service type
  Color get color {
    switch (this) {
      case TravelServiceType.carRental:
        return Colors.blue;
      case TravelServiceType.privateSecurity:
        return Colors.red;
      case TravelServiceType.hotel:
        return Colors.purple;
      case TravelServiceType.restaurant:
        return Colors.orange;
      case TravelServiceType.flight:
        return Colors.lightBlue;
      case TravelServiceType.cruise:
        return Colors.teal;
      case TravelServiceType.insurance:
        return Colors.green;
      case TravelServiceType.visa:
        return Colors.indigo;
    }
  }
}
