import 'package:flutter/material.dart';
import 'travel_service_base.dart';

/// Enum for hotel star ratings
enum HotelStarRating {
  oneStar,
  twoStar,
  threeStar,
  fourStar,
  fiveStar,
}

/// Extension for hotel star ratings
extension HotelStarRatingExtension on HotelStarRating {
  /// Get the display name for the hotel star rating
  String get displayName {
    switch (this) {
      case HotelStarRating.oneStar:
        return '1 Star';
      case HotelStarRating.twoStar:
        return '2 Stars';
      case HotelStarRating.threeStar:
        return '3 Stars';
      case HotelStarRating.fourStar:
        return '4 Stars';
      case HotelStarRating.fiveStar:
        return '5 Stars';
    }
  }

  /// Get the numeric value for the hotel star rating
  int get value {
    switch (this) {
      case HotelStarRating.oneStar:
        return 1;
      case HotelStarRating.twoStar:
        return 2;
      case HotelStarRating.threeStar:
        return 3;
      case HotelStarRating.fourStar:
        return 4;
      case HotelStarRating.fiveStar:
        return 5;
    }
  }
}

/// Enum for room types
enum RoomType {
  single,
  double,
  twin,
  triple,
  quad,
  queen,
  king,
  suite,
  deluxe,
  executive,
  presidential,
}

/// Extension for room types
extension RoomTypeExtension on RoomType {
  /// Get the display name for the room type
  String get displayName {
    switch (this) {
      case RoomType.single:
        return 'Single Room';
      case RoomType.double:
        return 'Double Room';
      case RoomType.twin:
        return 'Twin Room';
      case RoomType.triple:
        return 'Triple Room';
      case RoomType.quad:
        return 'Quad Room';
      case RoomType.queen:
        return 'Queen Room';
      case RoomType.king:
        return 'King Room';
      case RoomType.suite:
        return 'Suite';
      case RoomType.deluxe:
        return 'Deluxe Room';
      case RoomType.executive:
        return 'Executive Room';
      case RoomType.presidential:
        return 'Presidential Suite';
    }
  }
}

/// A model representing a hotel room
class HotelRoom {
  /// Unique identifier for the room
  final String id;

  /// Type of room
  final RoomType type;

  /// Description of the room
  final String description;

  /// Price per night
  final double pricePerNight;

  /// Currency of the price
  final String currency;

  /// Maximum number of guests
  final int maxGuests;

  /// Number of beds
  final int bedCount;

  /// Type of beds
  final String bedType;

  /// Size of the room in square meters/feet
  final String roomSize;

  /// Whether the room has a view
  final bool hasView;

  /// Type of view (if applicable)
  final String? viewType;

  /// Whether the room has a balcony
  final bool hasBalcony;

  /// Whether the room has a private bathroom
  final bool hasPrivateBathroom;

  /// Whether the room has air conditioning
  final bool hasAirConditioning;

  /// Whether the room has a TV
  final bool hasTV;

  /// Whether the room has a minibar
  final bool hasMinibar;

  /// Whether the room has a safe
  final bool hasSafe;

  /// Whether the room has free WiFi
  final bool hasFreeWifi;

  /// Whether the room is non-smoking
  final bool isNonSmoking;

  /// Whether the room is accessible
  final bool isAccessible;

  /// URL to the image of the room
  final String imageUrl;

  /// List of additional images
  final List<String> additionalImages;

  /// Creates a new hotel room
  const HotelRoom({
    required this.id,
    required this.type,
    required this.description,
    required this.pricePerNight,
    required this.currency,
    required this.maxGuests,
    required this.bedCount,
    required this.bedType,
    required thisoomSize,
    required thisasView,
    this.viewType,
    required thisasBalcony,
    required thisasPrivateBathroom,
    required thisasAirConditioning,
    required thisasTV,
    required thisasMinibar,
    required thisasSafe,
    required thisasFreeWifi,
    required this.isNonSmoking,
    required this.isAccessible,
    required this.imageUrl,
    required this.additionalImages,
  });

  /// Get the formatted price per night
  String get formattedPricePerNight {
    return '$currency${pricePerNight.toStringAsFixed(2)}';
  }
}

/// A model representing a hotel
class Hotel extends TravelService {
  /// Star rating of the hotel
  final HotelStarRating starRating;

  /// Available rooms in the hotel
  final List<HotelRoom> rooms;

  /// Check-in time
  final TimeOfDay checkInTime;

  /// Check-out time
  final TimeOfDay checkOutTime;

  /// Whether the hotel has a restaurant
  final bool hasRestaurant;

  /// Whether the hotel has a bar
  final bool hasBar;

  /// Whether the hotel has a pool
  final bool hasPool;

  /// Whether the hotel has a spa
  final bool hasSpa;

  /// Whether the hotel has a gym
  final bool hasGym;

  /// Whether the hotel has free WiFi
  final bool hasFreeWifi;

  /// Whether the hotel has free parking
  final bool hasFreeParking;

  /// Whether the hotel has room service
  final bool hasRoomService;

  /// Whether the hotel has a business center
  final bool hasBusinessCenter;

  /// Whether the hotel has a conference room
  final bool hasConferenceRoom;

  /// Whether the hotel has a kids club
  final bool hasKidsClub;

  /// Whether the hotel has a concierge service
  final bool hasConciergeService;

  /// Whether the hotel has a laundry service
  final bool hasLaundryService;

  /// Whether the hotel has a shuttle service
  final bool hasShuttleService;

  /// Whether the hotel has a 24-hour front desk
  final bool has24HrFrontDesk;

  /// Distance from city center in kilometers
  final double distanceFromCityCenter;

  /// Distance from nearest airport in kilometers
  final double distanceFromAirport;

  /// Name of the nearest airport
  final String nearestAirport;

  /// Creates a new hotel
  const Hotel({
    required super.id,
    required super.name,
    required super.description,
    required super.price,
    required super.currency,
    required super.rating,
    required super.reviewCount,
    required super.imageUrl,
    required super.additionalImages,
    required super.provider,
    required super.location,
    required super.coordinates,
    required super.isAvailable,
    required super.isFeatured,
    required super.isOnSale,
    super.originalPrice,
    super.discountPercentage,
    required super.tags,
    required super.amenities,
    required super.cancellationPolicy,
    required super.createdAt,
    required super.updatedAt,
    required this.starRating,
    required this.rooms,
    required this.checkInTime,
    required this.checkOutTime,
    required this.hasRestaurant,
    required this.hasBar,
    required this.hasPool,
    required this.hasSpa,
    required this.hasGym,
    required this.hasFreeWifi,
    required this.hasFreeParking,
    required this.hasRoomService,
    required this.hasBusinessCenter,
    required this.hasConferenceRoom,
    required this.hasKidsClub,
    required this.hasConciergeService,
    required this.hasLaundryService,
    required this.hasShuttleService,
    required this.has24HrFrontDesk,
    required this.distanceFromCityCenter,
    required this.distanceFromAirport,
    required this.nearestAirport,
  });

  @override
  IconData get icon => Icons.hotel;

  @override
  Color get color => Colors.purple;

  /// Get the formatted check-in time
  String get formattedCheckInTime {
    final hour = checkInTime.hourOfPeriod;
    final minute = checkInTime.minute.toString().padLeft(2, '0');
    final period = checkInTime.period == DayPeriod.am ? 'AM' : 'PM';
    return '$hour:$minute $period';
  }

  /// Get the formatted check-out time
  String get formattedCheckOutTime {
    final hour = checkOutTime.hourOfPeriod;
    final minute = checkOutTime.minute.toString().padLeft(2, '0');
    final period = checkOutTime.period == DayPeriod.am ? 'AM' : 'PM';
    return '$hour:$minute $period';
  }

  /// Get the formatted distance from city center
  String get formattedDistanceFromCityCenter {
    return '${distanceFromCityCenter.toStringAsFixed(1)} km from city center';
  }

  /// Get the formatted distance from airport
  String get formattedDistanceFromAirport {
    return '${distanceFromAirport.toStringAsFixed(1)} km from $nearestAirport';
  }
}
