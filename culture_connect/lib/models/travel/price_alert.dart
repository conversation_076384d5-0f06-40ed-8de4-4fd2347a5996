import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/travel_service_base.dart';

/// Alert frequency for price alerts
enum AlertFrequency {
  /// Immediate alert
  immediate,

  /// Daily alert
  daily,

  /// Weekly alert
  weekly,
}

/// Extension for alert frequency
extension AlertFrequencyExtension on AlertFrequency {
  /// Get the display name for the alert frequency
  String get displayName {
    switch (this) {
      case AlertFrequency.immediate:
        return 'Immediate';
      case AlertFrequency.daily:
        return 'Daily';
      case AlertFrequency.weekly:
        return 'Weekly';
    }
  }

  /// Get the icon for the alert frequency
  IconData get icon {
    switch (this) {
      case AlertFrequency.immediate:
        return Icons.notifications_active;
      case AlertFrequency.daily:
        return Icons.today;
      case AlertFrequency.weekly:
        return Icons.date_range;
    }
  }

  /// Get the description for the alert frequency
  String get description {
    switch (this) {
      case AlertFrequency.immediate:
        return 'Notify me as soon as the price drops';
      case AlertFrequency.daily:
        return 'Send me a daily summary of price changes';
      case AlertFrequency.weekly:
        return 'Send me a weekly summary of price changes';
    }
  }
}

/// Status of a price alert
enum PriceAlertStatus {
  /// Active alert
  active,

  /// Triggered alert
  triggered,

  /// Expired alert
  expired,

  /// Deleted alert
  deleted,
}

/// Extension for price alert status
extension PriceAlertStatusExtension on PriceAlertStatus {
  /// Get the display name for the price alert status
  String get displayName {
    switch (this) {
      case PriceAlertStatus.active:
        return 'Active';
      case PriceAlertStatus.triggered:
        return 'Triggered';
      case PriceAlertStatus.expired:
        return 'Expired';
      case PriceAlertStatus.deleted:
        return 'Deleted';
    }
  }

  /// Get the color for the price alert status
  Color get color {
    switch (this) {
      case PriceAlertStatus.active:
        return Colors.green;
      case PriceAlertStatus.triggered:
        return Colors.orange;
      case PriceAlertStatus.expired:
        return Colors.grey;
      case PriceAlertStatus.deleted:
        return Colors.red;
    }
  }

  /// Get the icon for the price alert status
  IconData get icon {
    switch (this) {
      case PriceAlertStatus.active:
        return Icons.notifications_active;
      case PriceAlertStatus.triggered:
        return Icons.notifications;
      case PriceAlertStatus.expired:
        return Icons.notifications_off;
      case PriceAlertStatus.deleted:
        return Icons.delete;
    }
  }
}

/// A model representing a price alert
class PriceAlert {
  /// Alert ID
  final String id;

  /// User ID
  final String userId;

  /// Travel service ID
  final String travelServiceId;

  /// Travel service type
  final TravelServiceType travelServiceType;

  /// Travel service name
  final String travelServiceName;

  /// Current price
  final double currentPrice;

  /// Target price
  final double targetPrice;

  /// Currency
  final String currency;

  /// Alert frequency
  final AlertFrequency frequency;

  /// Alert status
  final PriceAlertStatus status;

  /// Start date
  final DateTime startDate;

  /// End date
  final DateTime endDate;

  /// Created at
  final DateTime createdAt;

  /// Updated at
  final DateTime updatedAt;

  /// Last checked at
  final DateTime? lastCheckedAt;

  /// Last price
  final double? lastPrice;

  /// Price history
  final List<PriceHistoryPoint> priceHistory;

  /// Creates a new price alert
  const PriceAlert({
    required this.id,
    required this.userId,
    required this.travelServiceId,
    required this.travelServiceType,
    required this.travelServiceName,
    required this.currentPrice,
    required this.targetPrice,
    required this.currency,
    required this.frequency,
    required this.status,
    required this.startDate,
    required this.endDate,
    required this.createdAt,
    required this.updatedAt,
    this.lastCheckedAt,
    this.lastPrice,
    this.priceHistory = const [],
  });

  /// Create a copy with some fields replaced
  PriceAlert copyWith({
    String? id,
    String? userId,
    String? travelServiceId,
    TravelServiceType? travelServiceType,
    String? travelServiceName,
    double? currentPrice,
    double? targetPrice,
    String? currency,
    AlertFrequency? frequency,
    PriceAlertStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastCheckedAt,
    double? lastPrice,
    List<PriceHistoryPoint>? priceHistory,
  }) {
    return PriceAlert(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      travelServiceId: travelServiceId ?? this.travelServiceId,
      travelServiceType: travelServiceType ?? this.travelServiceType,
      travelServiceName: travelServiceName ?? this.travelServiceName,
      currentPrice: currentPrice ?? this.currentPrice,
      targetPrice: targetPrice ?? this.targetPrice,
      currency: currency ?? this.currency,
      frequency: frequency ?? this.frequency,
      status: status ?? this.status,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastCheckedAt: lastCheckedAt ?? this.lastCheckedAt,
      lastPrice: lastPrice ?? this.lastPrice,
      priceHistory: priceHistory ?? this.priceHistory,
    );
  }

  /// Get the formatted current price
  String get formattedCurrentPrice {
    return '$currency${currentPrice.toStringAsFixed(2)}';
  }

  /// Get the formatted target price
  String get formattedTargetPrice {
    return '$currency${targetPrice.toStringAsFixed(2)}';
  }

  /// Get the price difference
  double get priceDifference {
    return currentPrice - targetPrice;
  }

  /// Get the price difference percentage
  double get priceDifferencePercentage {
    return (priceDifference / currentPrice) * 100;
  }

  /// Get the formatted price difference
  String get formattedPriceDifference {
    final diff = priceDifference;
    final sign = diff > 0 ? '-' : '+';
    return '$sign$currency${diff.abs().toStringAsFixed(2)}';
  }

  /// Get the formatted price difference percentage
  String get formattedPriceDifferencePercentage {
    final diff = priceDifferencePercentage;
    final sign = diff > 0 ? '-' : '+';
    return '$sign${diff.abs().toStringAsFixed(1)}%';
  }

  /// Check if the alert is active
  bool get isActive {
    return status == PriceAlertStatus.active;
  }

  /// Check if the alert is triggered
  bool get isTriggered {
    return status == PriceAlertStatus.triggered;
  }

  /// Check if the alert is expired
  bool get isExpired {
    return status == PriceAlertStatus.expired;
  }

  /// Check if the alert is deleted
  bool get isDeleted {
    return status == PriceAlertStatus.deleted;
  }
}

/// A model representing a price history point
class PriceHistoryPoint {
  /// Date
  final DateTime date;

  /// Price
  final double price;

  /// Creates a new price history point
  const PriceHistoryPoint({
    required this.date,
    required this.price,
  });
}
