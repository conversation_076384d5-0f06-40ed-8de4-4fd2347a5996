import 'package:flutter/material.dart';

/// Represents the different types of insurance coverage
enum InsuranceCoverageType {
  /// Medical coverage for illness or injury
  medical(
    displayName: 'Medical Coverage',
    description: 'Coverage for medical expenses due to illness or injury',
    icon: Icons.medical_services,
  ),

  /// Coverage for trip cancellation
  cancellation(
    displayName: 'Trip Cancellation',
    description:
        'Reimbursement for prepaid, nonrefundable trip costs if you need to cancel',
    icon: Icons.cancel,
  ),

  /// Coverage for trip interruption
  interruption(
    displayName: 'Trip Interruption',
    description: 'Reimbursement if your trip is interrupted after departure',
    icon: Icons.block,
  ),

  /// Coverage for delayed baggage
  baggageDelay(
    displayName: 'Baggage Delay',
    description: 'Coverage for essential items if your baggage is delayed',
    icon: Icons.schedule,
  ),

  /// Coverage for lost or damaged baggage
  baggageLoss(
    displayName: 'Baggage Loss/Damage',
    description: 'Coverage for lost, stolen, or damaged baggage',
    icon: Icons.luggage,
  ),

  /// Coverage for travel delays
  travelDelay(
    displayName: 'Travel Delay',
    description: 'Coverage for additional expenses due to travel delays',
    icon: Icons.hourglass_empty,
  ),

  /// Coverage for missed connections
  missedConnection(
    displayName: 'Missed Connection',
    description: 'Coverage if you miss a connection due to a covered delay',
    icon: Icons.connecting_airports,
  ),

  /// Coverage for emergency evacuation
  evacuation(
    displayName: 'Emergency Evacuation',
    description: 'Coverage for emergency medical evacuation and repatriation',
    icon: Icons.flight_takeoff,
  ),

  /// Coverage for accidental death and dismemberment
  accidentalDeath(
    displayName: 'Accidental Death',
    description:
        'Coverage for accidental death or dismemberment during your trip',
    icon: Icons.warning,
  ),

  /// Coverage for rental car damage
  rentalCar(
    displayName: 'Rental Car Damage',
    description: 'Coverage for damage to a rental car during your trip',
    icon: Icons.car_rental,
  ),

  /// Coverage for adventure activities
  adventure(
    displayName: 'Adventure Activities',
    description: 'Coverage for injuries during adventure activities',
    icon: Icons.hiking,
  ),

  /// Coverage for pre-existing medical conditions
  preExisting(
    displayName: 'Pre-existing Conditions',
    description: 'Coverage for pre-existing medical conditions',
    icon: Icons.health_and_safety,
  );

  /// Creates a new insurance coverage type
  const InsuranceCoverageType({
    required this.displayName,
    required this.description,
    required this.icon,
  });

  /// The display name of the coverage type
  final String displayName;

  /// A description of the coverage type
  final String description;

  /// An icon representing the coverage type
  final IconData icon;

  /// Get a coverage type from its string representation
  static InsuranceCoverageType fromString(String value) {
    return InsuranceCoverageType.values.firstWhere(
      (type) => type.toString().split('.').last == value,
      orElse: () => InsuranceCoverageType.medical,
    );
  }

  /// Convert the coverage type to a string
  String toJson() => toString().split('.').last;

  /// Create a coverage type from JSON
  static InsuranceCoverageType fromJson(String json) => fromString(json);
}
