import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/insurance/insurance_model.dart';
import 'package:culture_connect/models/travel/insurance/insurance_coverage.dart';
import 'package:culture_connect/models/travel/insurance/insurance_provider.dart';

/// A model representing an insurance policy
class InsurancePolicy {
  /// Unique identifier for the policy
  final String id;

  /// Name of the policy
  final String name;

  /// Description of the policy
  final String description;

  /// Type of policy
  final InsurancePolicyType type;

  /// Provider of the policy
  final InsuranceProvider provider;

  /// Price of the policy
  final double price;

  /// Currency of the price
  final String currency;

  /// Coverage details
  final List<InsuranceCoverage> coverages;

  /// Start date of the policy
  final DateTime? startDate;

  /// End date of the policy
  final DateTime? endDate;

  /// Status of the policy
  final InsurancePolicyStatus status;

  /// Policy number
  final String? policyNumber;

  /// Destination countries
  final List<String> destinationCountries;

  /// Number of travelers
  final int travelerCount;

  /// Whether the policy is refundable
  final bool isRefundable;

  /// Refund policy details
  final String refundPolicy;

  /// Additional details about the policy
  final Map<String, dynamic> details;

  /// When the policy was created
  final DateTime createdAt;

  /// When the policy was last updated
  final DateTime updatedAt;

  /// Creates a new insurance policy
  const InsurancePolicy({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.provider,
    required this.price,
    required this.currency,
    required this.coverages,
    this.startDate,
    this.endDate,
    required this.status,
    this.policyNumber,
    required this.destinationCountries,
    required this.travelerCount,
    required this.isRefundable,
    required this.refundPolicy,
    this.details = const {},
    required this.createdAt,
    required this.updatedAt,
  });

  /// Get the formatted price
  String get formattedPrice {
    return '$currency${price.toStringAsFixed(2)}';
  }

  /// Get the duration in days
  int? get durationDays {
    if (startDate == null || endDate == null) return null;
    return endDate!.difference(startDate!).inDays + 1;
  }

  /// Get the formatted duration
  String? get formattedDuration {
    final days = durationDays;
    if (days == null) return null;
    return '$days ${days == 1 ? 'day' : 'days'}';
  }

  /// Get the formatted destination countries
  String get formattedDestinationCountries {
    if (destinationCountries.isEmpty) return 'Worldwide';
    if (destinationCountries.length == 1) return destinationCountries.first;
    return '${destinationCountries.length} countries';
  }

  /// Get the formatted traveler count
  String get formattedTravelerCount {
    return '$travelerCount ${travelerCount == 1 ? 'traveler' : 'travelers'}';
  }

  /// Check if the policy is active
  bool get isActive {
    return status == InsurancePolicyStatus.active;
  }

  /// Check if the policy is expired
  bool get isExpired {
    if (endDate == null) return false;
    return endDate!.isBefore(DateTime.now()) ||
        status == InsurancePolicyStatus.expired;
  }

  /// Get the icon for the policy
  IconData get icon {
    return type.icon;
  }

  /// Get the color for the policy
  Color get color {
    return status.color;
  }

  /// Creates a copy with some fields replaced
  InsurancePolicy copyWith({
    String? id,
    String? name,
    String? description,
    InsurancePolicyType? type,
    InsuranceProvider? provider,
    double? price,
    String? currency,
    List<InsuranceCoverage>? coverages,
    DateTime? startDate,
    DateTime? endDate,
    InsurancePolicyStatus? status,
    String? policyNumber,
    List<String>? destinationCountries,
    int? travelerCount,
    bool? isRefundable,
    String? refundPolicy,
    Map<String, dynamic>? details,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return InsurancePolicy(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      provider: provider ?? this.provider,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      coverages: coverages ?? this.coverages,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      status: status ?? this.status,
      policyNumber: policyNumber ?? this.policyNumber,
      destinationCountries: destinationCountries ?? this.destinationCountries,
      travelerCount: travelerCount ?? this.travelerCount,
      isRefundable: isRefundable ?? this.isRefundable,
      refundPolicy: refundPolicy ?? this.refundPolicy,
      details: details ?? this.details,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Creates from JSON
  factory InsurancePolicy.fromJson(Map<String, dynamic> json) {
    return InsurancePolicy(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: InsurancePolicyType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
      ),
      provider:
          InsuranceProvider.fromJson(json['provider'] as Map<String, dynamic>),
      price: json['price'] as double,
      currency: json['currency'] as String,
      coverages: (json['coverages'] as List<dynamic>)
          .map((e) => InsuranceCoverage.fromJson(e as Map<String, dynamic>))
          .toList(),
      startDate: json['startDate'] != null
          ? DateTime.parse(json['startDate'] as String)
          : null,
      endDate: json['endDate'] != null
          ? DateTime.parse(json['endDate'] as String)
          : null,
      status: InsurancePolicyStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
      ),
      policyNumber: json['policyNumber'] as String?,
      destinationCountries:
          (json['destinationCountries'] as List<dynamic>).cast<String>(),
      travelerCount: json['travelerCount'] as int,
      isRefundable: json['isRefundable'] as bool,
      refundPolicy: json['refundPolicy'] as String,
      details: json['details'] as Map<String, dynamic>? ?? {},
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.toString().split('.').last,
      'provider': provider.toJson(),
      'price': price,
      'currency': currency,
      'coverages': coverages.map((e) => e.toJson()).toList(),
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'status': status.toString().split('.').last,
      'policyNumber': policyNumber,
      'destinationCountries': destinationCountries,
      'travelerCount': travelerCount,
      'isRefundable': isRefundable,
      'refundPolicy': refundPolicy,
      'details': details,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}
