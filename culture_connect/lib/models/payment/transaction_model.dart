import 'package:culture_connect/models/payment/payment_method_model.dart';

/// Enum representing different transaction statuses
enum TransactionStatus {
  /// Transaction is pending
  pending,

  /// Transaction is processing
  processing,

  /// Transaction is completed
  completed,

  /// Transaction failed
  failed,

  /// Transaction is refunded
  refunded,

  /// Transaction is partially refunded
  partiallyRefunded,

  /// Transaction is cancelled
  cancelled,

  /// Transaction is disputed
  disputed,
}

/// Extension on TransactionStatus to provide additional functionality
extension TransactionStatusExtension on TransactionStatus {
  /// Get the display name of the transaction status
  String get displayName {
    switch (this) {
      case TransactionStatus.pending:
        return 'Pending';
      case TransactionStatus.processing:
        return 'Processing';
      case TransactionStatus.completed:
        return 'Completed';
      case TransactionStatus.failed:
        return 'Failed';
      case TransactionStatus.refunded:
        return 'Refunded';
      case TransactionStatus.partiallyRefunded:
        return 'Partially Refunded';
      case TransactionStatus.cancelled:
        return 'Cancelled';
      case TransactionStatus.disputed:
        return 'Disputed';
    }
  }

  /// Check if the transaction is in a final state
  bool get isFinal {
    return this == TransactionStatus.completed ||
        this == TransactionStatus.failed ||
        this == TransactionStatus.refunded ||
        this == TransactionStatus.partiallyRefunded ||
        this == TransactionStatus.cancelled ||
        this == TransactionStatus.disputed;
  }

  /// Check if the transaction is successful
  bool get isSuccessful {
    return this == TransactionStatus.completed;
  }
}

/// A model representing a transaction
class TransactionModel {
  /// Unique identifier for the transaction
  final String id;

  /// The payment method used for the transaction
  final PaymentMethodModel paymentMethod;

  /// The amount of the transaction
  final double amount;

  /// The currency of the transaction
  final String currency;

  /// The status of the transaction
  final TransactionStatus status;

  /// The date and time of the transaction
  final DateTime timestamp;

  /// The description of the transaction
  final String description;

  /// The reference ID for the transaction
  final String? referenceId;

  /// The receipt URL for the transaction
  final String? receiptUrl;

  /// The error message if the transaction failed
  final String? errorMessage;

  /// Additional details about the transaction
  final Map<String, dynamic> details;

  /// Creates a new transaction model
  const TransactionModel({
    required this.id,
    required this.paymentMethod,
    required this.amount,
    required this.currency,
    required this.status,
    required this.timestamp,
    required this.description,
    this.referenceId,
    this.receiptUrl,
    this.errorMessage,
    this.details = const {},
  });

  /// Creates a copy with some fields replaced
  TransactionModel copyWith({
    String? id,
    PaymentMethodModel? paymentMethod,
    double? amount,
    String? currency,
    TransactionStatus? status,
    DateTime? timestamp,
    String? description,
    String? referenceId,
    String? receiptUrl,
    String? errorMessage,
    Map<String, dynamic>? details,
  }) {
    return TransactionModel(
      id: id ?? this.id,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      description: description ?? this.description,
      referenceId: referenceId ?? this.referenceId,
      receiptUrl: receiptUrl ?? this.receiptUrl,
      errorMessage: errorMessage ?? this.errorMessage,
      details: details ?? this.details,
    );
  }

  /// Get the formatted amount
  String get formattedAmount {
    return '$currency${amount.toStringAsFixed(2)}';
  }

  /// Get the formatted date
  String get formattedDate {
    return '${timestamp.year}-${timestamp.month.toString().padLeft(2, '0')}-${timestamp.day.toString().padLeft(2, '0')}';
  }

  /// Get the formatted time
  String get formattedTime {
    return '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'paymentMethod': paymentMethod.toJson(),
      'amount': amount,
      'currency': currency,
      'status': status.index,
      'timestamp': timestamp.toIso8601String(),
      'description': description,
      'referenceId': referenceId,
      'receiptUrl': receiptUrl,
      'errorMessage': errorMessage,
      'details': details,
    };
  }

  /// Creates from JSON
  factory TransactionModel.fromJson(Map<String, dynamic> json) {
    return TransactionModel(
      id: json['id'] as String,
      paymentMethod: PaymentMethodModel.fromJson(
          json['paymentMethod'] as Map<String, dynamic>),
      amount: json['amount'] as double,
      currency: json['currency'] as String,
      status: TransactionStatus.values[json['status'] as int],
      timestamp: DateTime.parse(json['timestamp'] as String),
      description: json['description'] as String,
      referenceId: json['referenceId'] as String?,
      receiptUrl: json['receiptUrl'] as String?,
      errorMessage: json['errorMessage'] as String?,
      details: json['details'] as Map<String, dynamic>? ?? {},
    );
  }
}
