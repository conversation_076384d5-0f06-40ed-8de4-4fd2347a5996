import 'package:flutter/material.dart';

/// Enum representing different loyalty points transaction types
enum LoyaltyPointsTransactionType {
  /// Points earned from a booking
  booking,

  /// Points earned from a referral
  referral,

  /// Points earned from a promotion
  promotion,

  /// Points earned from a bonus
  bonus,

  /// Points earned from a review
  review,

  /// Points redeemed for a reward
  redemption,

  /// Points expired
  expiration,

  /// Points adjusted by admin
  adjustment,
}

/// Extension on LoyaltyPointsTransactionType to provide additional functionality
extension LoyaltyPointsTransactionTypeExtension
    on LoyaltyPointsTransactionType {
  /// Get the display name of the loyalty points transaction type
  String get displayName {
    switch (this) {
      case LoyaltyPointsTransactionType.booking:
        return 'Booking';
      case LoyaltyPointsTransactionType.referral:
        return 'Referral';
      case LoyaltyPointsTransactionType.promotion:
        return 'Promotion';
      case LoyaltyPointsTransactionType.bonus:
        return 'Bonus';
      case LoyaltyPointsTransactionType.review:
        return 'Review';
      case LoyaltyPointsTransactionType.redemption:
        return 'Redemption';
      case LoyaltyPointsTransactionType.expiration:
        return 'Expiration';
      case LoyaltyPointsTransactionType.adjustment:
        return 'Adjustment';
    }
  }

  /// Get the icon of the loyalty points transaction type
  IconData get icon {
    switch (this) {
      case LoyaltyPointsTransactionType.booking:
        return Icons.hotel;
      case LoyaltyPointsTransactionType.referral:
        return Icons.people;
      case LoyaltyPointsTransactionType.promotion:
        return Icons.local_offer;
      case LoyaltyPointsTransactionType.bonus:
        return Icons.card_giftcard;
      case LoyaltyPointsTransactionType.review:
        return Icons.rate_review;
      case LoyaltyPointsTransactionType.redemption:
        return Icons.redeem;
      case LoyaltyPointsTransactionType.expiration:
        return Icons.timer_off;
      case LoyaltyPointsTransactionType.adjustment:
        return Icons.settings;
    }
  }

  /// Check if the transaction is a points earning transaction
  bool get isEarning {
    return this != LoyaltyPointsTransactionType.redemption &&
        this != LoyaltyPointsTransactionType.expiration;
  }
}

/// A model representing a loyalty points transaction
class LoyaltyPointsTransaction {
  /// Unique identifier for the transaction
  final String id;

  /// Type of transaction
  final LoyaltyPointsTransactionType type;

  /// Points amount (positive for earning, negative for spending)
  final int points;

  /// Description of the transaction
  final String description;

  /// Reference ID (e.g., booking ID, reward ID)
  final String? referenceId;

  /// Transaction date
  final DateTime date;

  /// Creates a new loyalty points transaction
  const LoyaltyPointsTransaction({
    required this.id,
    required this.type,
    required this.points,
    required this.description,
    this.referenceId,
    required this.date,
  });

  /// Creates a copy with some fields replaced
  LoyaltyPointsTransaction copyWith({
    String? id,
    LoyaltyPointsTransactionType? type,
    int? points,
    String? description,
    String? referenceId,
    DateTime? date,
  }) {
    return LoyaltyPointsTransaction(
      id: id ?? this.id,
      type: type ?? this.type,
      points: points ?? this.points,
      description: description ?? this.description,
      referenceId: referenceId ?? this.referenceId,
      date: date ?? this.date,
    );
  }

  /// Check if the transaction is a points earning transaction
  bool get isEarning => points > 0;

  /// Get the formatted points amount
  String get formattedPoints {
    return isEarning ? '+$points' : '$points';
  }

  /// Get the formatted date
  String get formattedDate {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.index,
      'points': points,
      'description': description,
      'referenceId': referenceId,
      'date': date.toIso8601String(),
    };
  }

  /// Creates from JSON
  factory LoyaltyPointsTransaction.fromJson(Map<String, dynamic> json) {
    return LoyaltyPointsTransaction(
      id: json['id'] as String,
      type: LoyaltyPointsTransactionType.values[json['type'] as int],
      points: json['points'] as int,
      description: json['description'] as String,
      referenceId: json['referenceId'] as String?,
      date: DateTime.parse(json['date'] as String),
    );
  }
}
