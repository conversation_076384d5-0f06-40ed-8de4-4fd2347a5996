import 'package:flutter/foundation.dart';

/// A model representing an experience being created or edited by a guide.
///
/// This model contains all the information needed to create or update an experience.
class ExperienceCreationModel {
  final String? id;
  final String title;
  final String description;
  final String category;
  final List<String> tags;
  final double price;
  final String currency;
  final int durationMinutes;
  final int maxParticipants;
  final String meetingPoint;
  final Map<String, dynamic>? meetingPointCoordinates;
  final List<String> includedItems;
  final List<String> excludedItems;
  final List<String> requirements;
  final String cancellationPolicy;
  final List<String> languages;
  final List<String> accessibilityFeatures;
  final List<String> imageUrls;
  final String? videoUrl;
  final bool isPrivate;
  final bool isDraft;
  final String? guideId;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  /// Creates a new ExperienceCreationModel instance.
  ///
  /// All parameters except those with default values are required.
  ExperienceCreationModel({
    this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.tags,
    required this.price,
    required this.currency,
    required this.durationMinutes,
    required this.maxParticipants,
    required this.meetingPoint,
    this.meetingPointCoordinates,
    required this.includedItems,
    required this.excludedItems,
    required this.requirements,
    required this.cancellationPolicy,
    required this.languages,
    required this.accessibilityFeatures,
    required this.imageUrls,
    this.videoUrl,
    this.isPrivate = false,
    this.isDraft = true,
    this.guideId,
    this.createdAt,
    this.updatedAt,
  });

  /// Creates an ExperienceCreationModel from a JSON map.
  factory ExperienceCreationModel.fromJson(Map<String, dynamic> json) {
    return ExperienceCreationModel(
      id: json['id'] as String?,
      title: json['title'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
      tags: List<String>.from(json['tags'] as List),
      price: (json['price'] as num).toDouble(),
      currency: json['currency'] as String,
      durationMinutes: json['durationMinutes'] as int,
      maxParticipants: json['maxParticipants'] as int,
      meetingPoint: json['meetingPoint'] as String,
      meetingPointCoordinates:
          json['meetingPointCoordinates'] as Map<String, dynamic>?,
      includedItems: List<String>.from(json['includedItems'] as List),
      excludedItems: List<String>.from(json['excludedItems'] as List),
      requirements: List<String>.from(json['requirements'] as List),
      cancellationPolicy: json['cancellationPolicy'] as String,
      languages: List<String>.from(json['languages'] as List),
      accessibilityFeatures:
          List<String>.from(json['accessibilityFeatures'] as List),
      imageUrls: List<String>.from(json['imageUrls'] as List),
      videoUrl: json['videoUrl'] as String?,
      isPrivate: json['isPrivate'] as bool,
      isDraft: json['isDraft'] as bool,
      guideId: json['guideId'] as String?,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
    );
  }

  /// Converts this ExperienceCreationModel to a JSON map.
  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'title': title,
      'description': description,
      'category': category,
      'tags': tags,
      'price': price,
      'currency': currency,
      'durationMinutes': durationMinutes,
      'maxParticipants': maxParticipants,
      'meetingPoint': meetingPoint,
      if (meetingPointCoordinates != null)
        'meetingPointCoordinates': meetingPointCoordinates,
      'includedItems': includedItems,
      'excludedItems': excludedItems,
      'requirements': requirements,
      'cancellationPolicy': cancellationPolicy,
      'languages': languages,
      'accessibilityFeatures': accessibilityFeatures,
      'imageUrls': imageUrls,
      if (videoUrl != null) 'videoUrl': videoUrl,
      'isPrivate': isPrivate,
      'isDraft': isDraft,
      if (guideId != null) 'guideId': guideId,
      if (createdAt != null) 'createdAt': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
    };
  }

  /// Creates a copy of this ExperienceCreationModel with the given fields replaced with the new values.
  ExperienceCreationModel copyWith({
    String? id,
    String? title,
    String? description,
    String? category,
    List<String>? tags,
    double? price,
    String? currency,
    int? durationMinutes,
    int? maxParticipants,
    String? meetingPoint,
    Map<String, dynamic>? meetingPointCoordinates,
    List<String>? includedItems,
    List<String>? excludedItems,
    List<String>? requirements,
    String? cancellationPolicy,
    List<String>? languages,
    List<String>? accessibilityFeatures,
    List<String>? imageUrls,
    String? videoUrl,
    bool? isPrivate,
    bool? isDraft,
    String? guideId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ExperienceCreationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      maxParticipants: maxParticipants ?? this.maxParticipants,
      meetingPoint: meetingPoint ?? this.meetingPoint,
      meetingPointCoordinates:
          meetingPointCoordinates ?? this.meetingPointCoordinates,
      includedItems: includedItems ?? this.includedItems,
      excludedItems: excludedItems ?? this.excludedItems,
      requirements: requirements ?? this.requirements,
      cancellationPolicy: cancellationPolicy ?? this.cancellationPolicy,
      languages: languages ?? this.languages,
      accessibilityFeatures:
          accessibilityFeatures ?? this.accessibilityFeatures,
      imageUrls: imageUrls ?? this.imageUrls,
      videoUrl: videoUrl ?? this.videoUrl,
      isPrivate: isPrivate ?? this.isPrivate,
      isDraft: isDraft ?? this.isDraft,
      guideId: guideId ?? this.guideId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ExperienceCreationModel &&
        other.id == id &&
        other.title == title &&
        other.description == description &&
        other.category == category &&
        listEquals(other.tags, tags) &&
        other.price == price &&
        other.currency == currency &&
        other.durationMinutes == durationMinutes &&
        other.maxParticipants == maxParticipants &&
        other.meetingPoint == meetingPoint &&
        other.isPrivate == isPrivate &&
        other.isDraft == isDraft;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        title.hashCode ^
        description.hashCode ^
        category.hashCode ^
        tags.hashCode ^
        price.hashCode ^
        currency.hashCode ^
        durationMinutes.hashCode ^
        maxParticipants.hashCode ^
        meetingPoint.hashCode ^
        isPrivate.hashCode ^
        isDraft.hashCode;
  }
}
