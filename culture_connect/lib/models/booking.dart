import 'package:flutter/material.dart';

/// Status of a booking
enum BookingStatus {
  /// Booking is pending payment
  pending,

  /// Booking is confirmed
  confirmed,

  /// Booking is cancelled
  cancelled,

  /// Booking is completed
  completed,

  /// Booking is refunded
  refunded,
}

/// Represents a time slot for booking
class TimeSlot {
  /// Start time of the slot
  final DateTime startTime;

  /// End time of the slot
  final DateTime endTime;

  /// Whether the slot is available for booking
  final bool isAvailable;

  /// Creates a new time slot
  const TimeSlot({
    required this.startTime,
    required this.endTime,
    this.isAvailable = true,
  });

  /// Returns a formatted string representation of the time slot
  String get formattedTime {
    final start = TimeOfDay.fromDateTime(startTime);
    final end = TimeOfDay.fromDateTime(endTime);
    return '${_formatTimeOfDay(start)} - ${_formatTimeOfDay(end)}';
  }

  String _formatTimeOfDay(TimeOfDay timeOfDay) {
    final hour = timeOfDay.hourOfPeriod == 0 ? 12 : timeOfDay.hourOfPeriod;
    final minute = timeOfDay.minute.toString().padLeft(2, '0');
    final period = timeOfDay.period == DayPeriod.am ? 'AM' : 'PM';
    return '$hour:$minute $period';
  }
}

/// Represents a booking for an experience
class Booking {
  /// Unique identifier for the booking
  final String id;

  /// ID of the experience being booked
  final String experienceId;

  /// Date of the booking
  final DateTime date;

  /// Time slot for the booking
  final TimeSlot timeSlot;

  /// Number of participants
  final int participantCount;

  /// Total amount for the booking
  final double totalAmount;

  /// Status of the booking
  final BookingStatus status;

  /// Special requirements for the booking
  final String specialRequirements;

  /// When the booking was created
  final DateTime createdAt;

  /// When the booking was last updated
  final DateTime updatedAt;

  /// Creates a new booking
  const Booking({
    required this.id,
    required this.experienceId,
    required this.date,
    required this.timeSlot,
    required this.participantCount,
    required this.totalAmount,
    required this.status,
    this.specialRequirements = '',
    required this.createdAt,
    required this.updatedAt,
  });

  /// Creates a copy of this booking with the given fields replaced with the new values
  Booking copyWith({
    String? id,
    String? experienceId,
    DateTime? date,
    TimeSlot? timeSlot,
    int? participantCount,
    double? totalAmount,
    BookingStatus? status,
    String? specialRequirements,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Booking(
      id: id ?? this.id,
      experienceId: experienceId ?? this.experienceId,
      date: date ?? this.date,
      timeSlot: timeSlot ?? this.timeSlot,
      participantCount: participantCount ?? this.participantCount,
      totalAmount: totalAmount ?? this.totalAmount,
      status: status ?? this.status,
      specialRequirements: specialRequirements ?? this.specialRequirements,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
