import 'package:culture_connect/models/payment_transaction.dart';

/// Represents a receipt for a payment transaction
class Receipt {
  /// Unique identifier for the receipt
  final String id;

  /// ID of the transaction associated with this receipt
  final String transactionId;

  /// ID of the booking associated with this receipt
  final String bookingId;

  /// ID of the user who made the payment
  final String userId;

  /// ID of the guide who received the payment
  final String guideId;

  /// Name of the experience
  final String experienceName;

  /// Date of the experience
  final DateTime experienceDate;

  /// Number of participants
  final int participantCount;

  /// Amount of the transaction
  final double amount;

  /// Currency of the transaction
  final String currency;

  /// Tax amount
  final double? taxAmount;

  /// Tax rate
  final double? taxRate;

  /// Discount amount
  final double? discountAmount;

  /// Discount code
  final String? discountCode;

  /// Total amount after tax and discounts
  final double totalAmount;

  /// Payment method name
  final String paymentMethodName;

  /// Transaction status
  final TransactionStatus transactionStatus;

  /// Receipt number
  final String receiptNumber;

  /// When the receipt was created
  final DateTime createdAt;

  /// Creates a new receipt
  const Receipt({
    required this.id,
    required this.transactionId,
    required this.bookingId,
    required this.userId,
    required this.guideId,
    required this.experienceName,
    required this.experienceDate,
    required this.participantCount,
    required this.amount,
    required this.currency,
    this.taxAmount,
    this.taxRate,
    this.discountAmount,
    this.discountCode,
    required this.totalAmount,
    required this.paymentMethodName,
    required this.transactionStatus,
    required this.receiptNumber,
    required this.createdAt,
  });

  /// Creates a receipt from a JSON map
  factory Receipt.fromJson(Map<String, dynamic> json) {
    return Receipt(
      id: json['id'] as String,
      transactionId: json['transactionId'] as String,
      bookingId: json['bookingId'] as String,
      userId: json['userId'] as String,
      guideId: json['guideId'] as String,
      experienceName: json['experienceName'] as String,
      experienceDate: DateTime.parse(json['experienceDate'] as String),
      participantCount: json['participantCount'] as int,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      taxAmount: json['taxAmount'] != null
          ? (json['taxAmount'] as num).toDouble()
          : null,
      taxRate:
          json['taxRate'] != null ? (json['taxRate'] as num).toDouble() : null,
      discountAmount: json['discountAmount'] != null
          ? (json['discountAmount'] as num).toDouble()
          : null,
      discountCode: json['discountCode'] as String?,
      totalAmount: (json['totalAmount'] as num).toDouble(),
      paymentMethodName: json['paymentMethodName'] as String,
      transactionStatus: TransactionStatusExtension.fromString(
          json['transactionStatus'] as String),
      receiptNumber: json['receiptNumber'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  /// Converts this receipt to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'transactionId': transactionId,
      'bookingId': bookingId,
      'userId': userId,
      'guideId': guideId,
      'experienceName': experienceName,
      'experienceDate': experienceDate.toIso8601String(),
      'participantCount': participantCount,
      'amount': amount,
      'currency': currency,
      if (taxAmount != null) 'taxAmount': taxAmount,
      if (taxRate != null) 'taxRate': taxRate,
      if (discountAmount != null) 'discountAmount': discountAmount,
      if (discountCode != null) 'discountCode': discountCode,
      'totalAmount': totalAmount,
      'paymentMethodName': paymentMethodName,
      'transactionStatus': transactionStatus.toString().split('.').last,
      'receiptNumber': receiptNumber,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  /// Creates a copy of this receipt with the given fields replaced with the new values
  Receipt copyWith({
    String? id,
    String? transactionId,
    String? bookingId,
    String? userId,
    String? guideId,
    String? experienceName,
    DateTime? experienceDate,
    int? participantCount,
    double? amount,
    String? currency,
    double? taxAmount,
    double? taxRate,
    double? discountAmount,
    String? discountCode,
    double? totalAmount,
    String? paymentMethodName,
    TransactionStatus? transactionStatus,
    String? receiptNumber,
    DateTime? createdAt,
  }) {
    return Receipt(
      id: id ?? this.id,
      transactionId: transactionId ?? this.transactionId,
      bookingId: bookingId ?? this.bookingId,
      userId: userId ?? this.userId,
      guideId: guideId ?? this.guideId,
      experienceName: experienceName ?? this.experienceName,
      experienceDate: experienceDate ?? this.experienceDate,
      participantCount: participantCount ?? this.participantCount,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      taxAmount: taxAmount ?? this.taxAmount,
      taxRate: taxRate ?? this.taxRate,
      discountAmount: discountAmount ?? this.discountAmount,
      discountCode: discountCode ?? this.discountCode,
      totalAmount: totalAmount ?? this.totalAmount,
      paymentMethodName: paymentMethodName ?? this.paymentMethodName,
      transactionStatus: transactionStatus ?? this.transactionStatus,
      receiptNumber: receiptNumber ?? this.receiptNumber,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Returns a formatted amount with currency symbol
  String get formattedAmount {
    String symbol = _getCurrencySymbol(currency);
    return '$symbol${amount.toStringAsFixed(2)}';
  }

  /// Returns a formatted total amount with currency symbol
  String get formattedTotalAmount {
    String symbol = _getCurrencySymbol(currency);
    return '$symbol${totalAmount.toStringAsFixed(2)}';
  }

  /// Returns a formatted tax amount with currency symbol
  String? get formattedTaxAmount {
    if (taxAmount == null) return null;
    String symbol = _getCurrencySymbol(currency);
    return '$symbol${taxAmount!.toStringAsFixed(2)}';
  }

  /// Returns a formatted discount amount with currency symbol
  String? get formattedDiscountAmount {
    if (discountAmount == null) return null;
    String symbol = _getCurrencySymbol(currency);
    return '$symbol${discountAmount!.toStringAsFixed(2)}';
  }

  /// Returns a formatted date for the experience
  String get formattedExperienceDate {
    return '${experienceDate.day}/${experienceDate.month}/${experienceDate.year}';
  }

  /// Returns a currency symbol for the given currency code
  String _getCurrencySymbol(String currencyCode) {
    switch (currencyCode.toUpperCase()) {
      case 'USD':
        return '\$';
      case 'EUR':
        return '€';
      case 'GBP':
        return '£';
      case 'NGN':
        return '₦';
      case 'KES':
        return 'KSh';
      case 'ZAR':
        return 'R';
      default:
        return currencyCode;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Receipt && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
