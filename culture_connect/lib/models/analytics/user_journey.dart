import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';

/// Enumeration of journey step types
enum JourneyStepType {
  screenView('screen_view', 'Screen View'),
  buttonTap('button_tap', 'Button Tap'),
  formSubmission('form_submission', 'Form Submission'),
  search('search', 'Search'),
  filter('filter', 'Filter'),
  booking('booking', 'Booking'),
  payment('payment', 'Payment'),
  navigation('navigation', 'Navigation'),
  feature('feature', 'Feature Usage'),
  error('error', 'Error');

  const JourneyStepType(this.value, this.displayName);

  /// The string value of the step type
  final String value;

  /// The display name of the step type
  final String displayName;

  /// Convert from string value
  static JourneyStepType fromString(String value) {
    return JourneyStepType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => JourneyStepType.screenView,
    );
  }
}

/// Enumeration of journey outcomes
enum JourneyOutcome {
  completed('completed', 'Completed'),
  abandoned('abandoned', 'Abandoned'),
  error('error', 'Error'),
  timeout('timeout', 'Timeout'),
  ongoing('ongoing', 'Ongoing');

  const JourneyOutcome(this.value, this.displayName);

  /// The string value of the outcome
  final String value;

  /// The display name of the outcome
  final String displayName;

  /// Convert from string value
  static JourneyOutcome fromString(String value) {
    return JourneyOutcome.values.firstWhere(
      (outcome) => outcome.value == value,
      orElse: () => JourneyOutcome.ongoing,
    );
  }
}

/// Model representing a single step in a user journey
@immutable
class JourneyStep {
  /// Unique identifier for the step
  final String id;

  /// Type of journey step
  final JourneyStepType type;

  /// Timestamp when the step occurred
  final DateTime timestamp;

  /// Screen or page name where the step occurred
  final String screen;

  /// Action performed (e.g., button name, form field)
  final String action;

  /// Additional context data for the step
  final Map<String, dynamic> context;

  /// Duration spent on this step (if applicable)
  final Duration? duration;

  /// Whether this step was successful
  final bool isSuccessful;

  /// Error message if step failed
  final String? errorMessage;

  const JourneyStep({
    required this.id,
    required this.type,
    required this.timestamp,
    required this.screen,
    required this.action,
    this.context = const {},
    this.duration,
    this.isSuccessful = true,
    this.errorMessage,
  });

  /// Create a copy with updated values
  JourneyStep copyWith({
    String? id,
    JourneyStepType? type,
    DateTime? timestamp,
    String? screen,
    String? action,
    Map<String, dynamic>? context,
    Duration? duration,
    bool? isSuccessful,
    String? errorMessage,
  }) {
    return JourneyStep(
      id: id ?? this.id,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      screen: screen ?? this.screen,
      action: action ?? this.action,
      context: context ?? this.context,
      duration: duration ?? this.duration,
      isSuccessful: isSuccessful ?? this.isSuccessful,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// Convert to JSON for persistence
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.value,
      'timestamp': timestamp.toIso8601String(),
      'screen': screen,
      'action': action,
      'context': context,
      'duration': duration?.inMilliseconds,
      'isSuccessful': isSuccessful,
      'errorMessage': errorMessage,
    };
  }

  /// Create from JSON
  factory JourneyStep.fromJson(Map<String, dynamic> json) {
    return JourneyStep(
      id: json['id'] as String,
      type: JourneyStepType.fromString(json['type'] as String),
      timestamp: DateTime.parse(json['timestamp'] as String),
      screen: json['screen'] as String,
      action: json['action'] as String,
      context: Map<String, dynamic>.from(json['context'] as Map? ?? {}),
      duration: json['duration'] != null
          ? Duration(milliseconds: json['duration'] as int)
          : null,
      isSuccessful: json['isSuccessful'] as bool? ?? true,
      errorMessage: json['errorMessage'] as String?,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is JourneyStep &&
        other.id == id &&
        other.type == type &&
        other.timestamp == timestamp &&
        other.screen == screen &&
        other.action == action &&
        mapEquals(other.context, context) &&
        other.duration == duration &&
        other.isSuccessful == isSuccessful &&
        other.errorMessage == errorMessage;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      type,
      timestamp,
      screen,
      action,
      context,
      duration,
      isSuccessful,
      errorMessage,
    );
  }

  @override
  String toString() {
    return 'JourneyStep(id: $id, type: ${type.displayName}, '
        'screen: $screen, action: $action, successful: $isSuccessful)';
  }
}

/// Model representing a complete user journey
@immutable
class UserJourney {
  /// Unique identifier for the journey
  final String id;

  /// Session identifier for grouping journeys
  final String sessionId;

  /// User identifier (anonymized for privacy)
  final String? userId;

  /// Journey start timestamp
  final DateTime startTime;

  /// Journey end timestamp (null if ongoing)
  final DateTime? endTime;

  /// List of journey steps
  final List<JourneyStep> steps;

  /// Journey goals and their completion status
  final Map<String, bool> goals;

  /// Journey outcome
  final JourneyOutcome outcome;

  /// Additional metadata for the journey
  final Map<String, dynamic> metadata;

  /// Journey category (e.g., booking, exploration, support)
  final String category;

  const UserJourney({
    required this.id,
    required this.sessionId,
    this.userId,
    required this.startTime,
    this.endTime,
    this.steps = const [],
    this.goals = const {},
    this.outcome = JourneyOutcome.ongoing,
    this.metadata = const {},
    this.category = 'general',
  });

  /// Total journey duration
  Duration? get duration {
    if (endTime == null) return null;
    return endTime!.difference(startTime);
  }

  /// Number of steps in the journey
  int get stepCount => steps.length;

  /// Number of successful steps
  int get successfulSteps => steps.where((step) => step.isSuccessful).length;

  /// Success rate of the journey
  double get successRate {
    if (steps.isEmpty) return 1.0;
    return successfulSteps / steps.length;
  }

  /// Number of completed goals
  int get completedGoals => goals.values.where((completed) => completed).length;

  /// Goal completion rate
  double get goalCompletionRate {
    if (goals.isEmpty) return 1.0;
    return completedGoals / goals.length;
  }

  /// Whether the journey is completed
  bool get isCompleted => outcome != JourneyOutcome.ongoing;

  /// Create a copy with updated values
  UserJourney copyWith({
    String? id,
    String? sessionId,
    String? userId,
    DateTime? startTime,
    DateTime? endTime,
    List<JourneyStep>? steps,
    Map<String, bool>? goals,
    JourneyOutcome? outcome,
    Map<String, dynamic>? metadata,
    String? category,
  }) {
    return UserJourney(
      id: id ?? this.id,
      sessionId: sessionId ?? this.sessionId,
      userId: userId ?? this.userId,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      steps: steps ?? this.steps,
      goals: goals ?? this.goals,
      outcome: outcome ?? this.outcome,
      metadata: metadata ?? this.metadata,
      category: category ?? this.category,
    );
  }

  /// Add a step to the journey
  UserJourney addStep(JourneyStep step) {
    final updatedSteps = List<JourneyStep>.from(steps)..add(step);
    return copyWith(steps: updatedSteps);
  }

  /// Complete the journey with an outcome
  UserJourney complete(JourneyOutcome outcome) {
    return copyWith(
      endTime: DateTime.now(),
      outcome: outcome,
    );
  }

  /// Update goal completion status
  UserJourney updateGoal(String goalName, bool completed) {
    final updatedGoals = Map<String, bool>.from(goals);
    updatedGoals[goalName] = completed;
    return copyWith(goals: updatedGoals);
  }

  /// Convert to JSON for persistence
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sessionId': sessionId,
      'userId': userId,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'steps': steps.map((step) => step.toJson()).toList(),
      'goals': goals,
      'outcome': outcome.value,
      'metadata': metadata,
      'category': category,
    };
  }

  /// Create from JSON
  factory UserJourney.fromJson(Map<String, dynamic> json) {
    return UserJourney(
      id: json['id'] as String,
      sessionId: json['sessionId'] as String,
      userId: json['userId'] as String?,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: json['endTime'] != null
          ? DateTime.parse(json['endTime'] as String)
          : null,
      steps: (json['steps'] as List<dynamic>?)
              ?.map((stepJson) =>
                  JourneyStep.fromJson(stepJson as Map<String, dynamic>))
              .toList() ??
          [],
      goals: Map<String, bool>.from(json['goals'] as Map? ?? {}),
      outcome:
          JourneyOutcome.fromString(json['outcome'] as String? ?? 'ongoing'),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
      category: json['category'] as String? ?? 'general',
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserJourney &&
        other.id == id &&
        other.sessionId == sessionId &&
        other.userId == userId &&
        other.startTime == startTime &&
        other.endTime == endTime &&
        listEquals(other.steps, steps) &&
        mapEquals(other.goals, goals) &&
        other.outcome == outcome &&
        mapEquals(other.metadata, metadata) &&
        other.category == category;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      sessionId,
      userId,
      startTime,
      endTime,
      steps,
      goals,
      outcome,
      metadata,
      category,
    );
  }

  @override
  String toString() {
    return 'UserJourney(id: $id, category: $category, '
        'steps: ${steps.length}, outcome: ${outcome.displayName})';
  }
}

/// Model representing journey pattern analysis
@immutable
class JourneyPattern {
  /// Unique identifier for the pattern
  final String id;

  /// Pattern name or description
  final String name;

  /// Pattern category
  final String category;

  /// Frequency of this pattern occurrence
  final int frequency;

  /// Average duration for journeys following this pattern
  final Duration averageDuration;

  /// Success rate for this pattern
  final double successRate;

  /// Common steps in this pattern
  final List<String> commonSteps;

  /// Drop-off points in this pattern
  final List<String> dropOffPoints;

  /// Pattern insights and recommendations
  final List<String> insights;

  /// Last updated timestamp
  final DateTime lastUpdated;

  const JourneyPattern({
    required this.id,
    required this.name,
    required this.category,
    required this.frequency,
    required this.averageDuration,
    required this.successRate,
    this.commonSteps = const [],
    this.dropOffPoints = const [],
    this.insights = const [],
    required this.lastUpdated,
  });

  /// Convert to JSON for persistence
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'frequency': frequency,
      'averageDuration': averageDuration.inMilliseconds,
      'successRate': successRate,
      'commonSteps': commonSteps,
      'dropOffPoints': dropOffPoints,
      'insights': insights,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// Create from JSON
  factory JourneyPattern.fromJson(Map<String, dynamic> json) {
    return JourneyPattern(
      id: json['id'] as String,
      name: json['name'] as String,
      category: json['category'] as String,
      frequency: json['frequency'] as int,
      averageDuration: Duration(milliseconds: json['averageDuration'] as int),
      successRate: (json['successRate'] as num).toDouble(),
      commonSteps: List<String>.from(json['commonSteps'] as List? ?? []),
      dropOffPoints: List<String>.from(json['dropOffPoints'] as List? ?? []),
      insights: List<String>.from(json['insights'] as List? ?? []),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  @override
  String toString() {
    return 'JourneyPattern(name: $name, frequency: $frequency, '
        'successRate: ${(successRate * 100).toStringAsFixed(1)}%)';
  }
}

/// Model representing conversion funnel analysis
@immutable
class ConversionFunnel {
  /// Unique identifier for the funnel
  final String id;

  /// Funnel name
  final String name;

  /// Funnel stages with completion counts
  final Map<String, int> stages;

  /// Total users who entered the funnel
  final int totalUsers;

  /// Conversion rate for the entire funnel
  final double conversionRate;

  /// Drop-off rates between stages
  final Map<String, double> dropOffRates;

  /// Average time spent in each stage
  final Map<String, Duration> stageDurations;

  /// Analysis period
  final DateTime startDate;
  final DateTime endDate;

  const ConversionFunnel({
    required this.id,
    required this.name,
    required this.stages,
    required this.totalUsers,
    required this.conversionRate,
    this.dropOffRates = const {},
    this.stageDurations = const {},
    required this.startDate,
    required this.endDate,
  });

  /// Get completion count for a specific stage
  int getStageCompletion(String stage) {
    return stages[stage] ?? 0;
  }

  /// Get conversion rate for a specific stage
  double getStageConversionRate(String stage) {
    if (totalUsers == 0) return 0.0;
    return getStageCompletion(stage) / totalUsers;
  }

  /// Convert to JSON for persistence
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'stages': stages,
      'totalUsers': totalUsers,
      'conversionRate': conversionRate,
      'dropOffRates': dropOffRates,
      'stageDurations': stageDurations.map(
        (key, value) => MapEntry(key, value.inMilliseconds),
      ),
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
    };
  }

  /// Create from JSON
  factory ConversionFunnel.fromJson(Map<String, dynamic> json) {
    final stageDurationsJson =
        json['stageDurations'] as Map<String, dynamic>? ?? {};
    final stageDurations = stageDurationsJson.map(
      (key, value) => MapEntry(key, Duration(milliseconds: value as int)),
    );

    return ConversionFunnel(
      id: json['id'] as String,
      name: json['name'] as String,
      stages: Map<String, int>.from(json['stages'] as Map),
      totalUsers: json['totalUsers'] as int,
      conversionRate: (json['conversionRate'] as num).toDouble(),
      dropOffRates:
          Map<String, double>.from(json['dropOffRates'] as Map? ?? {}),
      stageDurations: stageDurations,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
    );
  }

  @override
  String toString() {
    return 'ConversionFunnel(name: $name, totalUsers: $totalUsers, '
        'conversionRate: ${(conversionRate * 100).toStringAsFixed(1)}%)';
  }
}

/// Factory class for creating journey-related objects
class JourneyFactory {
  static const _uuid = Uuid();

  /// Create a new journey step
  static JourneyStep createStep({
    required JourneyStepType type,
    required String screen,
    required String action,
    Map<String, dynamic>? context,
    Duration? duration,
    bool isSuccessful = true,
    String? errorMessage,
  }) {
    return JourneyStep(
      id: _uuid.v4(),
      type: type,
      timestamp: DateTime.now(),
      screen: screen,
      action: action,
      context: context ?? {},
      duration: duration,
      isSuccessful: isSuccessful,
      errorMessage: errorMessage,
    );
  }

  /// Create a new user journey
  static UserJourney createJourney({
    required String sessionId,
    String? userId,
    Map<String, bool>? goals,
    Map<String, dynamic>? metadata,
    String category = 'general',
  }) {
    return UserJourney(
      id: _uuid.v4(),
      sessionId: sessionId,
      userId: userId,
      startTime: DateTime.now(),
      goals: goals ?? {},
      metadata: metadata ?? {},
      category: category,
    );
  }

  /// Create a screen view step
  static JourneyStep createScreenViewStep({
    required String screen,
    Map<String, dynamic>? context,
  }) {
    return createStep(
      type: JourneyStepType.screenView,
      screen: screen,
      action: 'view',
      context: context,
    );
  }

  /// Create a button tap step
  static JourneyStep createButtonTapStep({
    required String screen,
    required String buttonName,
    Map<String, dynamic>? context,
  }) {
    return createStep(
      type: JourneyStepType.buttonTap,
      screen: screen,
      action: buttonName,
      context: context,
    );
  }

  /// Create an error step
  static JourneyStep createErrorStep({
    required String screen,
    required String action,
    required String errorMessage,
    Map<String, dynamic>? context,
  }) {
    return createStep(
      type: JourneyStepType.error,
      screen: screen,
      action: action,
      context: context,
      isSuccessful: false,
      errorMessage: errorMessage,
    );
  }
}
