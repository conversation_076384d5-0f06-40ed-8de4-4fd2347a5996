// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:uuid/uuid.dart';

// Project imports

/// Enum for travel service categories in analytics
enum TravelServiceCategory {
  /// Flight bookings
  flights,

  /// Travel insurance
  insurance,

  /// Visa services
  visa,

  /// Airport transfers
  transfers,

  /// Hotel bookings
  hotels,

  /// Car rentals
  carRentals,
}

/// Extension for travel service category
extension TravelServiceCategoryExtension on TravelServiceCategory {
  /// Get display name for the category
  String get displayName {
    switch (this) {
      case TravelServiceCategory.flights:
        return 'Flights';
      case TravelServiceCategory.insurance:
        return 'Insurance';
      case TravelServiceCategory.visa:
        return 'Visa Services';
      case TravelServiceCategory.transfers:
        return 'Transfers';
      case TravelServiceCategory.hotels:
        return 'Hotels';
      case TravelServiceCategory.carRentals:
        return 'Car Rentals';
    }
  }

  /// Get icon for the category
  IconData get icon {
    switch (this) {
      case TravelServiceCategory.flights:
        return Icons.flight;
      case TravelServiceCategory.insurance:
        return Icons.shield;
      case TravelServiceCategory.visa:
        return Icons.assignment;
      case TravelServiceCategory.transfers:
        return Icons.directions_car;
      case TravelServiceCategory.hotels:
        return Icons.hotel;
      case TravelServiceCategory.carRentals:
        return Icons.car_rental;
    }
  }

  /// Get color for the category
  Color get color {
    switch (this) {
      case TravelServiceCategory.flights:
        return Colors.blue;
      case TravelServiceCategory.insurance:
        return Colors.green;
      case TravelServiceCategory.visa:
        return Colors.indigo;
      case TravelServiceCategory.transfers:
        return Colors.orange;
      case TravelServiceCategory.hotels:
        return Colors.purple;
      case TravelServiceCategory.carRentals:
        return Colors.teal;
    }
  }
}

/// Model representing travel spending data
class TravelSpending {
  /// Unique identifier
  final String id;

  /// User ID
  final String userId;

  /// Total amount spent
  final double totalAmount;

  /// Currency code
  final String currency;

  /// Spending breakdown by category
  final Map<TravelServiceCategory, double> categoryBreakdown;

  /// Monthly spending data
  final Map<String, double> monthlySpending;

  /// Total savings compared to competitors
  final double totalSavings;

  /// Number of bookings
  final int totalBookings;

  /// Date range for this spending data
  final DateTime startDate;
  final DateTime endDate;

  /// Last updated timestamp
  final DateTime lastUpdated;

  /// Creates a new travel spending instance
  const TravelSpending({
    required this.id,
    required this.userId,
    required this.totalAmount,
    required this.currency,
    required this.categoryBreakdown,
    required this.monthlySpending,
    required this.totalSavings,
    required this.totalBookings,
    required this.startDate,
    required this.endDate,
    required this.lastUpdated,
  });

  /// Creates a travel spending from JSON
  factory TravelSpending.fromJson(Map<String, dynamic> json) {
    return TravelSpending(
      id: json['id'] as String,
      userId: json['userId'] as String,
      totalAmount: (json['totalAmount'] as num).toDouble(),
      currency: json['currency'] as String,
      categoryBreakdown: Map<TravelServiceCategory, double>.from(
        (json['categoryBreakdown'] as Map<String, dynamic>).map(
          (key, value) => MapEntry(
            TravelServiceCategory.values.firstWhere(
              (e) => e.name == key,
            ),
            (value as num).toDouble(),
          ),
        ),
      ),
      monthlySpending: Map<String, double>.from(
        (json['monthlySpending'] as Map<String, dynamic>).map(
          (key, value) => MapEntry(key, (value as num).toDouble()),
        ),
      ),
      totalSavings: (json['totalSavings'] as num).toDouble(),
      totalBookings: json['totalBookings'] as int,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  /// Converts travel spending to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'totalAmount': totalAmount,
      'currency': currency,
      'categoryBreakdown': categoryBreakdown.map(
        (key, value) => MapEntry(key.name, value),
      ),
      'monthlySpending': monthlySpending,
      'totalSavings': totalSavings,
      'totalBookings': totalBookings,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// Get formatted total amount
  String get formattedTotalAmount {
    return '$currency ${totalAmount.toStringAsFixed(2)}';
  }

  /// Get formatted total savings
  String get formattedTotalSavings {
    return '$currency ${totalSavings.toStringAsFixed(2)}';
  }

  /// Get savings percentage
  double get savingsPercentage {
    if (totalAmount == 0) return 0.0;
    return (totalSavings / (totalAmount + totalSavings)) * 100;
  }

  /// Get average spending per booking
  double get averageSpendingPerBooking {
    if (totalBookings == 0) return 0.0;
    return totalAmount / totalBookings;
  }

  /// Get category percentage
  double getCategoryPercentage(TravelServiceCategory category) {
    if (totalAmount == 0) return 0.0;
    final categoryAmount = categoryBreakdown[category] ?? 0.0;
    return (categoryAmount / totalAmount) * 100;
  }

  /// Get top spending category
  TravelServiceCategory? get topSpendingCategory {
    if (categoryBreakdown.isEmpty) return null;
    return categoryBreakdown.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  /// Copy with new values
  TravelSpending copyWith({
    String? id,
    String? userId,
    double? totalAmount,
    String? currency,
    Map<TravelServiceCategory, double>? categoryBreakdown,
    Map<String, double>? monthlySpending,
    double? totalSavings,
    int? totalBookings,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? lastUpdated,
  }) {
    return TravelSpending(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      totalAmount: totalAmount ?? this.totalAmount,
      currency: currency ?? this.currency,
      categoryBreakdown: categoryBreakdown ?? this.categoryBreakdown,
      monthlySpending: monthlySpending ?? this.monthlySpending,
      totalSavings: totalSavings ?? this.totalSavings,
      totalBookings: totalBookings ?? this.totalBookings,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Create empty spending data
  factory TravelSpending.empty(String userId) {
    final now = DateTime.now();
    return TravelSpending(
      id: const Uuid().v4(),
      userId: userId,
      totalAmount: 0.0,
      currency: 'USD',
      categoryBreakdown: {},
      monthlySpending: {},
      totalSavings: 0.0,
      totalBookings: 0,
      startDate: DateTime(now.year, 1, 1),
      endDate: DateTime(now.year, 12, 31),
      lastUpdated: now,
    );
  }
}

/// Model representing travel patterns and insights
class TravelPattern {
  /// Unique identifier
  final String id;

  /// User ID
  final String userId;

  /// Top destinations with visit counts
  final Map<String, int> topDestinations;

  /// Average advance booking days
  final double averageAdvanceBookingDays;

  /// Preferred booking day of week
  final String preferredBookingDay;

  /// Most active travel month
  final String mostActiveMonth;

  /// Travel frequency (trips per year)
  final double travelFrequency;

  /// Preferred travel class/type patterns
  final Map<String, int> travelPreferences;

  /// Seasonal travel patterns
  final Map<String, int> seasonalPatterns;

  /// Last updated timestamp
  final DateTime lastUpdated;

  /// Creates a new travel pattern instance
  const TravelPattern({
    required this.id,
    required this.userId,
    required this.topDestinations,
    required this.averageAdvanceBookingDays,
    required this.preferredBookingDay,
    required this.mostActiveMonth,
    required this.travelFrequency,
    required this.travelPreferences,
    required this.seasonalPatterns,
    required this.lastUpdated,
  });

  /// Creates a travel pattern from JSON
  factory TravelPattern.fromJson(Map<String, dynamic> json) {
    return TravelPattern(
      id: json['id'] as String,
      userId: json['userId'] as String,
      topDestinations: Map<String, int>.from(json['topDestinations'] as Map),
      averageAdvanceBookingDays:
          (json['averageAdvanceBookingDays'] as num).toDouble(),
      preferredBookingDay: json['preferredBookingDay'] as String,
      mostActiveMonth: json['mostActiveMonth'] as String,
      travelFrequency: (json['travelFrequency'] as num).toDouble(),
      travelPreferences:
          Map<String, int>.from(json['travelPreferences'] as Map),
      seasonalPatterns: Map<String, int>.from(json['seasonalPatterns'] as Map),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  /// Converts travel pattern to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'topDestinations': topDestinations,
      'averageAdvanceBookingDays': averageAdvanceBookingDays,
      'preferredBookingDay': preferredBookingDay,
      'mostActiveMonth': mostActiveMonth,
      'travelFrequency': travelFrequency,
      'travelPreferences': travelPreferences,
      'seasonalPatterns': seasonalPatterns,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// Get top destination
  String? get topDestination {
    if (topDestinations.isEmpty) return null;
    return topDestinations.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  /// Get travel style description
  String get travelStyle {
    if (averageAdvanceBookingDays > 30) {
      return 'Early Planner';
    } else if (averageAdvanceBookingDays > 14) {
      return 'Advance Booker';
    } else if (averageAdvanceBookingDays > 7) {
      return 'Flexible Traveler';
    } else {
      return 'Last-Minute Explorer';
    }
  }

  /// Create empty pattern data
  factory TravelPattern.empty(String userId) {
    return TravelPattern(
      id: const Uuid().v4(),
      userId: userId,
      topDestinations: {},
      averageAdvanceBookingDays: 0.0,
      preferredBookingDay: 'Unknown',
      mostActiveMonth: 'Unknown',
      travelFrequency: 0.0,
      travelPreferences: {},
      seasonalPatterns: {},
      lastUpdated: DateTime.now(),
    );
  }
}

/// Model representing destination insights
class DestinationInsight {
  /// Destination name
  final String destination;

  /// Number of visits
  final int visitCount;

  /// Total spending at this destination
  final double totalSpending;

  /// Average spending per visit
  final double averageSpending;

  /// Currency used
  final String currency;

  /// Last visit date
  final DateTime? lastVisit;

  /// Favorite travel services used at this destination
  final Map<TravelServiceCategory, int> serviceUsage;

  /// Creates a new destination insight
  const DestinationInsight({
    required this.destination,
    required this.visitCount,
    required this.totalSpending,
    required this.averageSpending,
    required this.currency,
    this.lastVisit,
    required this.serviceUsage,
  });

  /// Creates from JSON
  factory DestinationInsight.fromJson(Map<String, dynamic> json) {
    return DestinationInsight(
      destination: json['destination'] as String,
      visitCount: json['visitCount'] as int,
      totalSpending: (json['totalSpending'] as num).toDouble(),
      averageSpending: (json['averageSpending'] as num).toDouble(),
      currency: json['currency'] as String,
      lastVisit: json['lastVisit'] != null
          ? DateTime.parse(json['lastVisit'] as String)
          : null,
      serviceUsage: Map<TravelServiceCategory, int>.from(
        (json['serviceUsage'] as Map<String, dynamic>).map(
          (key, value) => MapEntry(
            TravelServiceCategory.values.firstWhere((e) => e.name == key),
            value as int,
          ),
        ),
      ),
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'destination': destination,
      'visitCount': visitCount,
      'totalSpending': totalSpending,
      'averageSpending': averageSpending,
      'currency': currency,
      'lastVisit': lastVisit?.toIso8601String(),
      'serviceUsage': serviceUsage.map(
        (key, value) => MapEntry(key.name, value),
      ),
    };
  }

  /// Get formatted total spending
  String get formattedTotalSpending {
    return '$currency ${totalSpending.toStringAsFixed(2)}';
  }

  /// Get formatted average spending
  String get formattedAverageSpending {
    return '$currency ${averageSpending.toStringAsFixed(2)}';
  }
}

/// Model representing analytics preferences and privacy settings
class AnalyticsPreferences {
  /// Whether analytics tracking is enabled
  final bool analyticsEnabled;

  /// Whether spending tracking is enabled
  final bool spendingTrackingEnabled;

  /// Whether pattern analysis is enabled
  final bool patternAnalysisEnabled;

  /// Whether to share data for insights
  final bool shareForInsights;

  /// Whether to include in benchmarks
  final bool includeInBenchmarks;

  /// Data retention period in days
  final int dataRetentionDays;

  /// Whether to export data automatically
  final bool autoExportEnabled;

  /// Export frequency in days
  final int exportFrequencyDays;

  /// Last updated timestamp
  final DateTime lastUpdated;

  /// Creates analytics preferences
  const AnalyticsPreferences({
    required this.analyticsEnabled,
    required this.spendingTrackingEnabled,
    required this.patternAnalysisEnabled,
    required this.shareForInsights,
    required this.includeInBenchmarks,
    required this.dataRetentionDays,
    required this.autoExportEnabled,
    required this.exportFrequencyDays,
    required this.lastUpdated,
  });

  /// Creates from JSON
  factory AnalyticsPreferences.fromJson(Map<String, dynamic> json) {
    return AnalyticsPreferences(
      analyticsEnabled: json['analyticsEnabled'] as bool,
      spendingTrackingEnabled: json['spendingTrackingEnabled'] as bool,
      patternAnalysisEnabled: json['patternAnalysisEnabled'] as bool,
      shareForInsights: json['shareForInsights'] as bool,
      includeInBenchmarks: json['includeInBenchmarks'] as bool,
      dataRetentionDays: json['dataRetentionDays'] as int,
      autoExportEnabled: json['autoExportEnabled'] as bool,
      exportFrequencyDays: json['exportFrequencyDays'] as int,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'analyticsEnabled': analyticsEnabled,
      'spendingTrackingEnabled': spendingTrackingEnabled,
      'patternAnalysisEnabled': patternAnalysisEnabled,
      'shareForInsights': shareForInsights,
      'includeInBenchmarks': includeInBenchmarks,
      'dataRetentionDays': dataRetentionDays,
      'autoExportEnabled': autoExportEnabled,
      'exportFrequencyDays': exportFrequencyDays,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// Create default preferences
  factory AnalyticsPreferences.defaultPreferences() {
    return AnalyticsPreferences(
      analyticsEnabled: true,
      spendingTrackingEnabled: true,
      patternAnalysisEnabled: true,
      shareForInsights: false,
      includeInBenchmarks: false,
      dataRetentionDays: 365,
      autoExportEnabled: false,
      exportFrequencyDays: 30,
      lastUpdated: DateTime.now(),
    );
  }

  /// Copy with new values
  AnalyticsPreferences copyWith({
    bool? analyticsEnabled,
    bool? spendingTrackingEnabled,
    bool? patternAnalysisEnabled,
    bool? shareForInsights,
    bool? includeInBenchmarks,
    int? dataRetentionDays,
    bool? autoExportEnabled,
    int? exportFrequencyDays,
    DateTime? lastUpdated,
  }) {
    return AnalyticsPreferences(
      analyticsEnabled: analyticsEnabled ?? this.analyticsEnabled,
      spendingTrackingEnabled:
          spendingTrackingEnabled ?? this.spendingTrackingEnabled,
      patternAnalysisEnabled:
          patternAnalysisEnabled ?? this.patternAnalysisEnabled,
      shareForInsights: shareForInsights ?? this.shareForInsights,
      includeInBenchmarks: includeInBenchmarks ?? this.includeInBenchmarks,
      dataRetentionDays: dataRetentionDays ?? this.dataRetentionDays,
      autoExportEnabled: autoExportEnabled ?? this.autoExportEnabled,
      exportFrequencyDays: exportFrequencyDays ?? this.exportFrequencyDays,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

// TODO: Backend Integration - Analytics Data Models API
// POST /api/v1/analytics/sync
// Headers: Authorization: Bearer {token}, Content-Type: application/json
// Body: {
//   "userId": "string",
//   "travelSpending": TravelSpending.toJson(),
//   "travelPattern": TravelPattern.toJson(),
//   "destinationInsights": List<DestinationInsight>.map(toJson),
//   "preferences": AnalyticsPreferences.toJson(),
//   "syncTimestamp": "ISO8601"
// }
// Response: {
//   "success": boolean,
//   "syncId": "string",
//   "lastSyncTime": "ISO8601",
//   "conflicts": ConflictResolution[]
// }

// TODO: Analytics Data Export API
// GET /api/v1/analytics/export?userId={id}&format=json&dateRange=2024
// Headers: Authorization: Bearer {token}
// Response: Complete user analytics data in requested format
// Formats: json, csv, pdf
// Include: spending breakdown, travel patterns, destination insights

// TODO: Analytics Benchmarking API
// GET /api/v1/analytics/benchmarks?userSegment=frequent_traveler&anonymous=true
// Response: {
//   "averageSpending": number,
//   "topDestinations": string[],
//   "averageAdvanceBooking": number,
//   "savingsOpportunities": BenchmarkInsight[]
// }
