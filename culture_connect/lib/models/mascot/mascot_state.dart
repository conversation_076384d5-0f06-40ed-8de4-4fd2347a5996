// Flutter imports
import 'package:flutter/material.dart';

/// Enum representing different mascot expressions
enum MascotExpression {
  /// Happy expression for successful bookings and positive outcomes
  happy,

  /// Excited expression for new discoveries and AR experiences
  excited,

  /// Helpful expression for onboarding and tutorials
  helpful,

  /// Sympathetic expression for errors and failures
  sympathetic,

  /// Celebrating expression for milestones and achievements
  celebrating,
}

/// Extension on MascotExpression to provide additional functionality
extension MascotExpressionExtension on MascotExpression {
  /// Get the display name for the expression
  String get displayName {
    switch (this) {
      case MascotExpression.happy:
        return 'Happy';
      case MascotExpression.excited:
        return 'Excited';
      case MascotExpression.helpful:
        return 'Helpful';
      case MascotExpression.sympathetic:
        return 'Sympathetic';
      case MascotExpression.celebrating:
        return 'Celebrating';
    }
  }

  /// Get the icon for the expression
  IconData get icon {
    switch (this) {
      case MascotExpression.happy:
        return Icons.sentiment_satisfied;
      case MascotExpression.excited:
        return Icons.sentiment_very_satisfied;
      case MascotExpression.helpful:
        return Icons.help_outline;
      case MascotExpression.sympathetic:
        return Icons.sentiment_dissatisfied;
      case MascotExpression.celebrating:
        return Icons.celebration;
    }
  }

  /// Get the primary color for the expression
  Color get primaryColor {
    switch (this) {
      case MascotExpression.happy:
        return Colors.green;
      case MascotExpression.excited:
        return Colors.orange;
      case MascotExpression.helpful:
        return Colors.blue;
      case MascotExpression.sympathetic:
        return Colors.grey;
      case MascotExpression.celebrating:
        return Colors.purple;
    }
  }

  /// Get the animation asset path for the expression
  String get animationAssetPath {
    switch (this) {
      case MascotExpression.happy:
        return 'assets/animations/mascot/happy.json';
      case MascotExpression.excited:
        return 'assets/animations/mascot/excited.json';
      case MascotExpression.helpful:
        return 'assets/animations/mascot/helpful.json';
      case MascotExpression.sympathetic:
        return 'assets/animations/mascot/sympathetic.json';
      case MascotExpression.celebrating:
        return 'assets/animations/mascot/celebrating.json';
    }
  }

  /// Get the fallback animation URL for the expression
  String get fallbackAnimationUrl {
    switch (this) {
      case MascotExpression.happy:
        return 'https://assets10.lottiefiles.com/packages/lf20_s2lryxtd.json';
      case MascotExpression.excited:
        return 'https://assets4.lottiefiles.com/packages/lf20_jbrw3hcz.json';
      case MascotExpression.helpful:
        return 'https://assets9.lottiefiles.com/packages/lf20_dmw9zyps.json';
      case MascotExpression.sympathetic:
        return 'https://assets8.lottiefiles.com/packages/lf20_qp1spzqv.json';
      case MascotExpression.celebrating:
        return 'https://assets1.lottiefiles.com/packages/lf20_rovf92iq.json';
    }
  }

  /// Check if this expression should repeat the animation
  bool get shouldRepeat {
    switch (this) {
      case MascotExpression.happy:
        return false;
      case MascotExpression.excited:
        return true;
      case MascotExpression.helpful:
        return true;
      case MascotExpression.sympathetic:
        return false;
      case MascotExpression.celebrating:
        return false;
    }
  }

  /// Get the animation duration for the expression
  Duration get animationDuration {
    switch (this) {
      case MascotExpression.happy:
        return const Duration(milliseconds: 1500);
      case MascotExpression.excited:
        return const Duration(milliseconds: 2000);
      case MascotExpression.helpful:
        return const Duration(milliseconds: 1800);
      case MascotExpression.sympathetic:
        return const Duration(milliseconds: 1200);
      case MascotExpression.celebrating:
        return const Duration(milliseconds: 3000);
    }
  }
}

/// Enum representing different mascot contexts
enum MascotContext {
  /// Achievement unlocked context
  achievementUnlocked,

  /// Booking successful context
  bookingSuccessful,

  /// Booking failed context
  bookingFailed,

  /// Loading context
  loading,

  /// Error context
  error,

  /// Onboarding context
  onboarding,

  /// Tutorial context
  tutorial,

  /// Discovery context
  discovery,

  /// Default idle context
  idle,
}

/// Model representing the current state of the mascot
class MascotState {
  /// The current expression of the mascot
  final MascotExpression expression;

  /// The context that triggered this state
  final MascotContext context;

  /// Additional metadata for the state
  final Map<String, dynamic> metadata;

  /// Whether the mascot is currently animating
  final bool isAnimating;

  /// The timestamp when this state was created
  final DateTime timestamp;

  /// Whether this state should trigger haptic feedback
  final bool shouldTriggerHaptic;

  /// The duration this state should be displayed
  final Duration? displayDuration;

  /// Creates a new mascot state
  MascotState({
    required this.expression,
    required this.context,
    this.metadata = const {},
    this.isAnimating = false,
    DateTime? timestamp,
    this.shouldTriggerHaptic = false,
    this.displayDuration,
  }) : timestamp = timestamp ?? DateTime.now();

  /// Creates a copy of this state with updated values
  MascotState copyWith({
    MascotExpression? expression,
    MascotContext? context,
    Map<String, dynamic>? metadata,
    bool? isAnimating,
    DateTime? timestamp,
    bool? shouldTriggerHaptic,
    Duration? displayDuration,
  }) {
    return MascotState(
      expression: expression ?? this.expression,
      context: context ?? this.context,
      metadata: metadata ?? this.metadata,
      isAnimating: isAnimating ?? this.isAnimating,
      timestamp: timestamp ?? this.timestamp,
      shouldTriggerHaptic: shouldTriggerHaptic ?? this.shouldTriggerHaptic,
      displayDuration: displayDuration ?? this.displayDuration,
    );
  }

  /// Creates a mascot state for achievement celebration
  factory MascotState.achievementCelebration({
    String? achievementId,
    String? achievementTitle,
  }) {
    return MascotState(
      expression: MascotExpression.celebrating,
      context: MascotContext.achievementUnlocked,
      metadata: {
        'achievementId': achievementId,
        'achievementTitle': achievementTitle,
        'celebrationType': 'achievement',
      },
      shouldTriggerHaptic: true,
      displayDuration: const Duration(seconds: 3),
    );
  }

  /// Creates a mascot state for successful booking
  factory MascotState.bookingSuccess({
    String? bookingType,
    String? bookingId,
  }) {
    return MascotState(
      expression: MascotExpression.happy,
      context: MascotContext.bookingSuccessful,
      metadata: {
        'bookingType': bookingType,
        'bookingId': bookingId,
      },
      shouldTriggerHaptic: true,
      displayDuration: const Duration(seconds: 2),
    );
  }

  /// Creates a mascot state for errors
  factory MascotState.error({
    String? errorMessage,
    String? errorType,
  }) {
    return MascotState(
      expression: MascotExpression.sympathetic,
      context: MascotContext.error,
      metadata: {
        'errorMessage': errorMessage,
        'errorType': errorType,
      },
      displayDuration: const Duration(seconds: 2),
    );
  }

  /// Creates a mascot state for loading
  factory MascotState.loading({
    String? loadingMessage,
  }) {
    return MascotState(
      expression: MascotExpression.helpful,
      context: MascotContext.loading,
      metadata: {
        'loadingMessage': loadingMessage,
      },
      isAnimating: true,
    );
  }

  /// Creates a mascot state for onboarding
  factory MascotState.onboarding({
    String? step,
    String? message,
  }) {
    return MascotState(
      expression: MascotExpression.helpful,
      context: MascotContext.onboarding,
      metadata: {
        'step': step,
        'message': message,
      },
    );
  }

  /// Creates a mascot state for discovery
  factory MascotState.discovery({
    String? discoveryType,
    String? discoveryMessage,
  }) {
    return MascotState(
      expression: MascotExpression.excited,
      context: MascotContext.discovery,
      metadata: {
        'discoveryType': discoveryType,
        'discoveryMessage': discoveryMessage,
      },
      isAnimating: true,
    );
  }

  /// Creates a default idle mascot state
  factory MascotState.idle() {
    return MascotState(
      expression: MascotExpression.helpful,
      context: MascotContext.idle,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MascotState &&
        other.expression == expression &&
        other.context == context &&
        other.isAnimating == isAnimating &&
        other.shouldTriggerHaptic == shouldTriggerHaptic;
  }

  @override
  int get hashCode {
    return Object.hash(
      expression,
      context,
      isAnimating,
      shouldTriggerHaptic,
    );
  }

  @override
  String toString() {
    return 'MascotState(expression: $expression, context: $context, '
        'isAnimating: $isAnimating, timestamp: $timestamp)';
  }
}
