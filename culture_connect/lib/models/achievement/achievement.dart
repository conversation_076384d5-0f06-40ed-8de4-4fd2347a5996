// Flutter imports
import 'package:flutter/material.dart';

/// Enum for achievement types based on travel booking activities
enum AchievementType {
  // Travel Booking Achievements
  firstJourney,
  insuranceProtector,
  worldExplorer,
  skyNavigator,
  journeyPlanner,
  travelMaster,

  // Service Explorer Achievements
  serviceSampler,
  comparisonExpert,
  earlyBird,
  lastMinuteHero,

  // Loyalty & Engagement Achievements
  loyalTraveler,
  reviewChampion,
  referralMaster,
  appExplorer,

  // Safety & Security Achievements
  safetyFirst,
  documentOrganizer,
  visaExpert,

  // Special Achievements
  globalNomad,
  culturalAmbassador,
  travelInfluencer,
}

/// Enum for achievement categories
enum AchievementCategory {
  travelBooking,
  serviceUsage,
  loyaltyProgram,
  safetyFeatures,
  exploration,
  social,
}

/// Enum for achievement difficulty levels
enum AchievementDifficulty {
  bronze,
  silver,
  gold,
  platinum,
  diamond,
}

/// Enum for achievement reward types
enum AchievementRewardType {
  points,
  badge,
  discount,
  exclusiveAccess,
  feature,
}

/// Model representing an achievement in the CultureConnect app
class Achievement {
  /// Unique identifier for the achievement
  final String id;

  /// Achievement type
  final AchievementType type;

  /// Achievement category
  final AchievementCategory category;

  /// Achievement difficulty level
  final AchievementDifficulty difficulty;

  /// Achievement title
  final String title;

  /// Achievement description
  final String description;

  /// Achievement icon
  final IconData icon;

  /// Achievement color theme
  final Color color;

  /// Points required to unlock this achievement
  final int pointsRequired;

  /// Reward type for this achievement
  final AchievementRewardType rewardType;

  /// Reward value (points, discount percentage, etc.)
  final int rewardValue;

  /// Whether this achievement is hidden until unlocked
  final bool isHidden;

  /// Whether this achievement can be earned multiple times
  final bool isRepeatable;

  /// Prerequisites (other achievement IDs that must be unlocked first)
  final List<String> prerequisites;

  /// Tags for filtering and searching
  final List<String> tags;

  /// Achievement creation timestamp
  final DateTime createdAt;

  /// Achievement last updated timestamp
  final DateTime updatedAt;

  const Achievement({
    required this.id,
    required this.type,
    required this.category,
    required this.difficulty,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.pointsRequired,
    required this.rewardType,
    required this.rewardValue,
    this.isHidden = false,
    this.isRepeatable = false,
    this.prerequisites = const [],
    this.tags = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  /// Creates an Achievement from JSON
  factory Achievement.fromJson(Map<String, dynamic> json) {
    return Achievement(
      id: json['id'] as String,
      type: AchievementType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => AchievementType.firstJourney,
      ),
      category: AchievementCategory.values.firstWhere(
        (e) => e.toString().split('.').last == json['category'],
        orElse: () => AchievementCategory.travelBooking,
      ),
      difficulty: AchievementDifficulty.values.firstWhere(
        (e) => e.toString().split('.').last == json['difficulty'],
        orElse: () => AchievementDifficulty.bronze,
      ),
      title: json['title'] as String,
      description: json['description'] as String,
      icon: IconData(
        json['iconCodePoint'] as int,
        fontFamily: 'MaterialIcons',
      ),
      color: Color(json['colorValue'] as int),
      pointsRequired: json['pointsRequired'] as int,
      rewardType: AchievementRewardType.values.firstWhere(
        (e) => e.toString().split('.').last == json['rewardType'],
        orElse: () => AchievementRewardType.points,
      ),
      rewardValue: json['rewardValue'] as int,
      isHidden: json['isHidden'] as bool? ?? false,
      isRepeatable: json['isRepeatable'] as bool? ?? false,
      prerequisites: (json['prerequisites'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              [],
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Converts the Achievement to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'category': category.toString().split('.').last,
      'difficulty': difficulty.toString().split('.').last,
      'title': title,
      'description': description,
      'iconCodePoint': icon.codePoint,
      'colorValue': color.value,
      'pointsRequired': pointsRequired,
      'rewardType': rewardType.toString().split('.').last,
      'rewardValue': rewardValue,
      'isHidden': isHidden,
      'isRepeatable': isRepeatable,
      'prerequisites': prerequisites,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Creates a copy of this Achievement with updated fields
  Achievement copyWith({
    String? id,
    AchievementType? type,
    AchievementCategory? category,
    AchievementDifficulty? difficulty,
    String? title,
    String? description,
    IconData? icon,
    Color? color,
    int? pointsRequired,
    AchievementRewardType? rewardType,
    int? rewardValue,
    bool? isHidden,
    bool? isRepeatable,
    List<String>? prerequisites,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Achievement(
      id: id ?? this.id,
      type: type ?? this.type,
      category: category ?? this.category,
      difficulty: difficulty ?? this.difficulty,
      title: title ?? this.title,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      pointsRequired: pointsRequired ?? this.pointsRequired,
      rewardType: rewardType ?? this.rewardType,
      rewardValue: rewardValue ?? this.rewardValue,
      isHidden: isHidden ?? this.isHidden,
      isRepeatable: isRepeatable ?? this.isRepeatable,
      prerequisites: prerequisites ?? this.prerequisites,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Gets the display name for the achievement difficulty
  String get difficultyDisplayName {
    switch (difficulty) {
      case AchievementDifficulty.bronze:
        return 'Bronze';
      case AchievementDifficulty.silver:
        return 'Silver';
      case AchievementDifficulty.gold:
        return 'Gold';
      case AchievementDifficulty.platinum:
        return 'Platinum';
      case AchievementDifficulty.diamond:
        return 'Diamond';
    }
  }

  /// Gets the display name for the achievement category
  String get categoryDisplayName {
    switch (category) {
      case AchievementCategory.travelBooking:
        return 'Travel Booking';
      case AchievementCategory.serviceUsage:
        return 'Service Usage';
      case AchievementCategory.loyaltyProgram:
        return 'Loyalty Program';
      case AchievementCategory.safetyFeatures:
        return 'Safety Features';
      case AchievementCategory.exploration:
        return 'Exploration';
      case AchievementCategory.social:
        return 'Social';
    }
  }

  /// Gets the display name for the reward type
  String get rewardDisplayName {
    switch (rewardType) {
      case AchievementRewardType.points:
        return '$rewardValue Points';
      case AchievementRewardType.badge:
        return 'Badge';
      case AchievementRewardType.discount:
        return '$rewardValue% Discount';
      case AchievementRewardType.exclusiveAccess:
        return 'Exclusive Access';
      case AchievementRewardType.feature:
        return 'Feature Unlock';
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Achievement &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Achievement{id: $id, title: $title, type: $type, difficulty: $difficulty}';
  }
}
