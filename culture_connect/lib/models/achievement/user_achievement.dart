// Project imports
import 'package:culture_connect/models/achievement/achievement.dart';

/// Enum for user achievement status
enum UserAchievementStatus {
  locked,
  inProgress,
  unlocked,
  celebrated,
}

/// Model representing a user's progress and status for a specific achievement
class UserAchievement {
  /// Unique identifier for this user achievement record
  final String id;

  /// User ID who owns this achievement
  final String userId;

  /// Achievement ID this record refers to
  final String achievementId;

  /// The actual achievement data
  final Achievement achievement;

  /// Current status of this achievement for the user
  final UserAchievementStatus status;

  /// Current progress towards completing this achievement (0.0 to 1.0)
  final double progress;

  /// Current points earned towards this achievement
  final int currentPoints;

  /// Timestamp when this achievement was unlocked (null if not unlocked)
  final DateTime? unlockedAt;

  /// Timestamp when the celebration was shown (null if not celebrated)
  final DateTime? celebratedAt;

  /// Number of times this achievement has been earned (for repeatable achievements)
  final int timesEarned;

  /// Additional metadata for tracking progress
  final Map<String, dynamic> progressMetadata;

  /// Timestamp when this record was created
  final DateTime createdAt;

  /// Timestamp when this record was last updated
  final DateTime updatedAt;

  const UserAchievement({
    required this.id,
    required this.userId,
    required this.achievementId,
    required this.achievement,
    required this.status,
    required this.progress,
    required this.currentPoints,
    this.unlockedAt,
    this.celebratedAt,
    this.timesEarned = 0,
    this.progressMetadata = const {},
    required this.createdAt,
    required this.updatedAt,
  });

  /// Creates a UserAchievement from JSON
  factory UserAchievement.fromJson(Map<String, dynamic> json) {
    return UserAchievement(
      id: json['id'] as String,
      userId: json['userId'] as String,
      achievementId: json['achievementId'] as String,
      achievement:
          Achievement.fromJson(json['achievement'] as Map<String, dynamic>),
      status: UserAchievementStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => UserAchievementStatus.locked,
      ),
      progress: (json['progress'] as num).toDouble(),
      currentPoints: json['currentPoints'] as int,
      unlockedAt: json['unlockedAt'] != null
          ? DateTime.parse(json['unlockedAt'] as String)
          : null,
      celebratedAt: json['celebratedAt'] != null
          ? DateTime.parse(json['celebratedAt'] as String)
          : null,
      timesEarned: json['timesEarned'] as int? ?? 0,
      progressMetadata: Map<String, dynamic>.from(
        json['progressMetadata'] as Map<String, dynamic>? ?? {},
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Converts the UserAchievement to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'achievementId': achievementId,
      'achievement': achievement.toJson(),
      'status': status.toString().split('.').last,
      'progress': progress,
      'currentPoints': currentPoints,
      'unlockedAt': unlockedAt?.toIso8601String(),
      'celebratedAt': celebratedAt?.toIso8601String(),
      'timesEarned': timesEarned,
      'progressMetadata': progressMetadata,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Creates a copy of this UserAchievement with updated fields
  UserAchievement copyWith({
    String? id,
    String? userId,
    String? achievementId,
    Achievement? achievement,
    UserAchievementStatus? status,
    double? progress,
    int? currentPoints,
    DateTime? unlockedAt,
    DateTime? celebratedAt,
    int? timesEarned,
    Map<String, dynamic>? progressMetadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserAchievement(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      achievementId: achievementId ?? this.achievementId,
      achievement: achievement ?? this.achievement,
      status: status ?? this.status,
      progress: progress ?? this.progress,
      currentPoints: currentPoints ?? this.currentPoints,
      unlockedAt: unlockedAt ?? this.unlockedAt,
      celebratedAt: celebratedAt ?? this.celebratedAt,
      timesEarned: timesEarned ?? this.timesEarned,
      progressMetadata: progressMetadata ?? this.progressMetadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Whether this achievement is unlocked
  bool get isUnlocked =>
      status == UserAchievementStatus.unlocked ||
      status == UserAchievementStatus.celebrated;

  /// Whether this achievement is in progress
  bool get isInProgress => status == UserAchievementStatus.inProgress;

  /// Whether this achievement is locked
  bool get isLocked => status == UserAchievementStatus.locked;

  /// Whether the celebration has been shown
  bool get isCelebrated => status == UserAchievementStatus.celebrated;

  /// Whether this achievement needs celebration
  bool get needsCelebration => status == UserAchievementStatus.unlocked;

  /// Progress percentage (0 to 100)
  int get progressPercentage => (progress * 100).round();

  /// Points remaining to unlock this achievement
  int get pointsRemaining => (achievement.pointsRequired - currentPoints)
      .clamp(0, achievement.pointsRequired);

  /// Whether the achievement is complete (progress = 1.0)
  bool get isComplete => progress >= 1.0;

  /// Whether the achievement can be unlocked (meets all requirements)
  bool get canUnlock => isComplete && !isUnlocked;

  /// Gets a user-friendly status description
  String get statusDescription {
    switch (status) {
      case UserAchievementStatus.locked:
        return 'Locked';
      case UserAchievementStatus.inProgress:
        return 'In Progress ($progressPercentage%)';
      case UserAchievementStatus.unlocked:
        return 'Unlocked';
      case UserAchievementStatus.celebrated:
        return 'Earned';
    }
  }

  /// Gets the next milestone for progress tracking
  String get nextMilestone {
    if (isUnlocked) {
      return achievement.isRepeatable ? 'Earn again' : 'Completed';
    }

    final remaining = pointsRemaining;
    if (remaining <= 0) {
      return 'Ready to unlock!';
    }

    return '$remaining more points needed';
  }

  /// Gets progress metadata value by key
  T? getProgressMetadata<T>(String key) {
    final value = progressMetadata[key];
    return value is T ? value : null;
  }

  /// Updates progress metadata with a new key-value pair
  UserAchievement updateProgressMetadata(String key, dynamic value) {
    final newMetadata = Map<String, dynamic>.from(progressMetadata);
    newMetadata[key] = value;

    return copyWith(
      progressMetadata: newMetadata,
      updatedAt: DateTime.now(),
    );
  }

  /// Marks this achievement as unlocked
  UserAchievement unlock() {
    return copyWith(
      status: UserAchievementStatus.unlocked,
      progress: 1.0,
      currentPoints: achievement.pointsRequired,
      unlockedAt: DateTime.now(),
      timesEarned: timesEarned + 1,
      updatedAt: DateTime.now(),
    );
  }

  /// Marks this achievement as celebrated
  UserAchievement markCelebrated() {
    return copyWith(
      status: UserAchievementStatus.celebrated,
      celebratedAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Updates the progress for this achievement
  UserAchievement updateProgress(int points, {Map<String, dynamic>? metadata}) {
    final newPoints =
        (currentPoints + points).clamp(0, achievement.pointsRequired);
    final newProgress =
        (newPoints / achievement.pointsRequired).clamp(0.0, 1.0);

    UserAchievementStatus newStatus = status;
    if (newProgress >= 1.0 && status == UserAchievementStatus.inProgress) {
      newStatus = UserAchievementStatus.unlocked;
    } else if (newProgress > 0.0 && status == UserAchievementStatus.locked) {
      newStatus = UserAchievementStatus.inProgress;
    }

    final newMetadata = metadata != null
        ? {...progressMetadata, ...metadata}
        : progressMetadata;

    return copyWith(
      status: newStatus,
      progress: newProgress,
      currentPoints: newPoints,
      progressMetadata: newMetadata,
      unlockedAt: newStatus == UserAchievementStatus.unlocked
          ? DateTime.now()
          : unlockedAt,
      updatedAt: DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserAchievement &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserAchievement{id: $id, achievementId: $achievementId, status: $status, progress: $progress}';
  }
}
