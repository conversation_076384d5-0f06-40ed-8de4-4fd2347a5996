// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:uuid/uuid.dart';

// Project imports
import 'package:culture_connect/models/travel/flight/booking_info.dart';
import 'package:culture_connect/models/travel/flight/passenger_info.dart';

/// Enum representing different booking statuses
enum BookingStatus {
  /// Booking is confirmed and active
  confirmed,

  /// Booking is pending confirmation
  pending,

  /// Booking has been cancelled
  cancelled,

  /// Booking is completed (flight has departed)
  completed,

  /// Booking requires attention (e.g., flight delayed/cancelled)
  requiresAttention,

  /// Booking is being modified
  modifying,
}

/// Extension for booking status
extension BookingStatusExtension on BookingStatus {
  /// Get the display name for the booking status
  String get displayName {
    switch (this) {
      case BookingStatus.confirmed:
        return 'Confirmed';
      case BookingStatus.pending:
        return 'Pending';
      case BookingStatus.cancelled:
        return 'Cancelled';
      case BookingStatus.completed:
        return 'Completed';
      case BookingStatus.requiresAttention:
        return 'Requires Attention';
      case BookingStatus.modifying:
        return 'Modifying';
    }
  }

  /// Get the color associated with the booking status
  Color get color {
    switch (this) {
      case BookingStatus.confirmed:
        return const Color(0xFF4CAF50); // Green
      case BookingStatus.pending:
        return const Color(0xFFFF9800); // Orange
      case BookingStatus.cancelled:
        return const Color(0xFFF44336); // Red
      case BookingStatus.completed:
        return const Color(0xFF9E9E9E); // Grey
      case BookingStatus.requiresAttention:
        return const Color(0xFFE91E63); // Pink
      case BookingStatus.modifying:
        return const Color(0xFF2196F3); // Blue
    }
  }

  /// Get the icon associated with the booking status
  IconData get icon {
    switch (this) {
      case BookingStatus.confirmed:
        return Icons.check_circle;
      case BookingStatus.pending:
        return Icons.schedule;
      case BookingStatus.cancelled:
        return Icons.cancel;
      case BookingStatus.completed:
        return Icons.flight_land;
      case BookingStatus.requiresAttention:
        return Icons.warning;
      case BookingStatus.modifying:
        return Icons.edit;
    }
  }
}

/// Enum representing different types of booking modifications
enum ModificationType {
  /// Seat change
  seatChange,

  /// Meal preference change
  mealChange,

  /// Special assistance request
  specialAssistance,

  /// Passenger information update
  passengerInfo,

  /// Flight change (date/time)
  flightChange,

  /// Cancellation
  cancellation,
}

/// Extension for modification type
extension ModificationTypeExtension on ModificationType {
  /// Get the display name for the modification type
  String get displayName {
    switch (this) {
      case ModificationType.seatChange:
        return 'Seat Change';
      case ModificationType.mealChange:
        return 'Meal Preference';
      case ModificationType.specialAssistance:
        return 'Special Assistance';
      case ModificationType.passengerInfo:
        return 'Passenger Information';
      case ModificationType.flightChange:
        return 'Flight Change';
      case ModificationType.cancellation:
        return 'Cancellation';
    }
  }

  /// Get the icon for the modification type
  IconData get icon {
    switch (this) {
      case ModificationType.seatChange:
        return Icons.airline_seat_recline_normal;
      case ModificationType.mealChange:
        return Icons.restaurant;
      case ModificationType.specialAssistance:
        return Icons.accessible;
      case ModificationType.passengerInfo:
        return Icons.person;
      case ModificationType.flightChange:
        return Icons.flight;
      case ModificationType.cancellation:
        return Icons.cancel;
    }
  }
}

/// Model representing a booking modification
class BookingModification {
  /// Unique identifier for the modification
  final String id;

  /// Type of modification
  final ModificationType type;

  /// Description of the modification
  final String description;

  /// Previous value (if applicable)
  final String? previousValue;

  /// New value
  final String newValue;

  /// Fee for the modification
  final double? fee;

  /// Currency for the fee
  final String? currency;

  /// Timestamp when modification was requested
  final DateTime requestedAt;

  /// Timestamp when modification was processed
  final DateTime? processedAt;

  /// Whether the modification is approved
  final bool isApproved;

  /// Reason for rejection (if applicable)
  final String? rejectionReason;

  /// Creates a new booking modification
  const BookingModification({
    required this.id,
    required this.type,
    required this.description,
    this.previousValue,
    required this.newValue,
    this.fee,
    this.currency,
    required this.requestedAt,
    this.processedAt,
    this.isApproved = false,
    this.rejectionReason,
  });

  /// Creates a new booking modification with generated ID
  factory BookingModification.create({
    required ModificationType type,
    required String description,
    String? previousValue,
    required String newValue,
    double? fee,
    String? currency,
  }) {
    return BookingModification(
      id: const Uuid().v4(),
      type: type,
      description: description,
      previousValue: previousValue,
      newValue: newValue,
      fee: fee,
      currency: currency,
      requestedAt: DateTime.now(),
    );
  }

  /// Creates a copy with updated values
  BookingModification copyWith({
    String? id,
    ModificationType? type,
    String? description,
    String? previousValue,
    String? newValue,
    double? fee,
    String? currency,
    DateTime? requestedAt,
    DateTime? processedAt,
    bool? isApproved,
    String? rejectionReason,
  }) {
    return BookingModification(
      id: id ?? this.id,
      type: type ?? this.type,
      description: description ?? this.description,
      previousValue: previousValue ?? this.previousValue,
      newValue: newValue ?? this.newValue,
      fee: fee ?? this.fee,
      currency: currency ?? this.currency,
      requestedAt: requestedAt ?? this.requestedAt,
      processedAt: processedAt ?? this.processedAt,
      isApproved: isApproved ?? this.isApproved,
      rejectionReason: rejectionReason ?? this.rejectionReason,
    );
  }

  /// Creates a booking modification from JSON
  factory BookingModification.fromJson(Map<String, dynamic> json) {
    return BookingModification(
      id: json['id'] as String,
      type: ModificationType.values[json['type'] as int],
      description: json['description'] as String,
      previousValue: json['previousValue'] as String?,
      newValue: json['newValue'] as String,
      fee: json['fee'] as double?,
      currency: json['currency'] as String?,
      requestedAt: DateTime.parse(json['requestedAt'] as String),
      processedAt: json['processedAt'] != null
          ? DateTime.parse(json['processedAt'] as String)
          : null,
      isApproved: json['isApproved'] as bool? ?? false,
      rejectionReason: json['rejectionReason'] as String?,
    );
  }

  /// Converts booking modification to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.index,
      'description': description,
      'previousValue': previousValue,
      'newValue': newValue,
      'fee': fee,
      'currency': currency,
      'requestedAt': requestedAt.toIso8601String(),
      'processedAt': processedAt?.toIso8601String(),
      'isApproved': isApproved,
      'rejectionReason': rejectionReason,
    };
  }

  /// Get formatted fee
  String get formattedFee {
    if (fee == null || currency == null) return 'Free';
    return '$currency ${fee!.toStringAsFixed(2)}';
  }

  /// Check if modification is pending
  bool get isPending => processedAt == null;

  /// Check if modification is processed
  bool get isProcessed => processedAt != null;

  /// Check if modification is rejected
  bool get isRejected => isProcessed && !isApproved;
}

/// Model representing a digital boarding pass
class BoardingPass {
  /// Unique identifier for the boarding pass
  final String id;

  /// Booking reference
  final String bookingReference;

  /// Passenger information
  final PassengerInfo passenger;

  /// Flight number
  final String flightNumber;

  /// Departure airport code
  final String departureAirport;

  /// Arrival airport code
  final String arrivalAirport;

  /// Departure date and time
  final DateTime departureTime;

  /// Seat number
  final String? seatNumber;

  /// Gate number
  final String? gate;

  /// Terminal
  final String? terminal;

  /// Boarding group
  final String? boardingGroup;

  /// QR code data for digital scanning
  final String qrCodeData;

  /// Whether the boarding pass is active
  final bool isActive;

  /// Timestamp when boarding pass was generated
  final DateTime generatedAt;

  /// Creates a new boarding pass
  const BoardingPass({
    required this.id,
    required this.bookingReference,
    required this.passenger,
    required this.flightNumber,
    required this.departureAirport,
    required this.arrivalAirport,
    required this.departureTime,
    this.seatNumber,
    this.gate,
    this.terminal,
    this.boardingGroup,
    required this.qrCodeData,
    this.isActive = true,
    required this.generatedAt,
  });

  /// Creates a boarding pass from booking info
  factory BoardingPass.fromBookingInfo({
    required BookingInfo bookingInfo,
    required PassengerInfo passenger,
    String? gate,
    String? terminal,
    String? boardingGroup,
  }) {
    final id = const Uuid().v4();
    final qrCodeData =
        'BP:$id:${bookingInfo.bookingReference}:${passenger.passportNumber}';

    return BoardingPass(
      id: id,
      bookingReference: bookingInfo.bookingReference ?? 'UNKNOWN',
      passenger: passenger,
      flightNumber: bookingInfo.flight.flightNumber,
      departureAirport: bookingInfo.flight.departureAirport,
      arrivalAirport: bookingInfo.flight.arrivalAirport,
      departureTime: bookingInfo.flight.departureDateTime,
      seatNumber: bookingInfo.selectedSeats[passenger.passportNumber],
      gate: gate,
      terminal: terminal,
      boardingGroup: boardingGroup,
      qrCodeData: qrCodeData,
      generatedAt: DateTime.now(),
    );
  }

  /// Get formatted departure time
  String get formattedDepartureTime {
    return '${departureTime.hour.toString().padLeft(2, '0')}:${departureTime.minute.toString().padLeft(2, '0')}';
  }

  /// Get formatted departure date
  String get formattedDepartureDate {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return '${departureTime.day} ${months[departureTime.month - 1]} ${departureTime.year}';
  }

  /// Check if boarding pass is available (24 hours before departure)
  bool get isAvailable {
    final now = DateTime.now();
    final availableTime = departureTime.subtract(const Duration(hours: 24));
    return now.isAfter(availableTime) && isActive;
  }

  /// Get time until boarding pass is available
  Duration? get timeUntilAvailable {
    if (isAvailable) return null;
    final now = DateTime.now();
    final availableTime = departureTime.subtract(const Duration(hours: 24));
    return availableTime.difference(now);
  }

  /// Creates a boarding pass from JSON
  factory BoardingPass.fromJson(Map<String, dynamic> json) {
    return BoardingPass(
      id: json['id'] as String,
      bookingReference: json['bookingReference'] as String,
      passenger:
          PassengerInfo.fromJson(json['passenger'] as Map<String, dynamic>),
      flightNumber: json['flightNumber'] as String,
      departureAirport: json['departureAirport'] as String,
      arrivalAirport: json['arrivalAirport'] as String,
      departureTime: DateTime.parse(json['departureTime'] as String),
      seatNumber: json['seatNumber'] as String?,
      gate: json['gate'] as String?,
      terminal: json['terminal'] as String?,
      boardingGroup: json['boardingGroup'] as String?,
      qrCodeData: json['qrCodeData'] as String,
      isActive: json['isActive'] as bool? ?? true,
      generatedAt: DateTime.parse(json['generatedAt'] as String),
    );
  }

  /// Converts boarding pass to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'bookingReference': bookingReference,
      'passenger': passenger.toJson(),
      'flightNumber': flightNumber,
      'departureAirport': departureAirport,
      'arrivalAirport': arrivalAirport,
      'departureTime': departureTime.toIso8601String(),
      'seatNumber': seatNumber,
      'gate': gate,
      'terminal': terminal,
      'boardingGroup': boardingGroup,
      'qrCodeData': qrCodeData,
      'isActive': isActive,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }
}
