import 'package:flutter/material.dart';
import 'package:culture_connect/models/offline/content_conflict_resolution.dart';

/// Status of a content conflict
enum ContentConflictStatus {
  /// Conflict is pending resolution
  pending,

  /// Conflict has been resolved
  resolved,

  /// Conflict resolution has been deferred
  deferred,
}

/// Extension methods for ContentConflictStatus
extension ContentConflictStatusExtension on ContentConflictStatus {
  /// Get the display name for the conflict status
  String get displayName {
    switch (this) {
      case ContentConflictStatus.pending:
        return 'Pending';
      case ContentConflictStatus.resolved:
        return 'Resolved';
      case ContentConflictStatus.deferred:
        return 'Deferred';
    }
  }

  /// Get the color for the conflict status
  Color get color {
    switch (this) {
      case ContentConflictStatus.pending:
        return Colors.orange;
      case ContentConflictStatus.resolved:
        return Colors.green;
      case ContentConflictStatus.deferred:
        return Colors.blue;
    }
  }

  /// Get the icon for the conflict status
  IconData get icon {
    switch (this) {
      case ContentConflictStatus.pending:
        return Icons.warning;
      case ContentConflictStatus.resolved:
        return Icons.check_circle;
      case ContentConflictStatus.deferred:
        return Icons.schedule;
    }
  }
}

/// A class representing a content conflict
class ContentConflict {
  /// The unique identifier
  final String id;

  /// The content ID
  final String contentId;

  /// The content type
  final String contentType;

  /// The content title
  final String contentTitle;

  /// The server version
  final int serverVersion;

  /// The client version
  final int clientVersion;

  /// The server modified time
  final DateTime serverModifiedTime;

  /// The client modified time
  final DateTime clientModifiedTime;

  /// The conflict status
  final ContentConflictStatus status;

  /// The resolution strategy
  final ContentConflictResolution? resolution;

  /// The resolution time
  final DateTime? resolutionTime;

  /// The server data
  final Map<String, dynamic> serverData;

  /// The client data
  final Map<String, dynamic> clientData;

  /// The merged data (if resolved with merge)
  final Map<String, dynamic>? mergedData;

  /// Creates a new content conflict
  const ContentConflict({
    required this.id,
    required this.contentId,
    required this.contentType,
    required this.contentTitle,
    required this.serverVersion,
    required this.clientVersion,
    required this.serverModifiedTime,
    required this.clientModifiedTime,
    required this.status,
    this.resolution,
    this.resolutionTime,
    required this.serverData,
    required this.clientData,
    this.mergedData,
  });

  /// Check if the conflict is resolved
  bool get isResolved => status == ContentConflictStatus.resolved;

  /// Check if the conflict is pending
  bool get isPending => status == ContentConflictStatus.pending;

  /// Check if the conflict is deferred
  bool get isDeferred => status == ContentConflictStatus.deferred;

  /// Get the formatted server modified time
  String get formattedServerModifiedTime => _formatDateTime(serverModifiedTime);

  /// Get the formatted client modified time
  String get formattedClientModifiedTime => _formatDateTime(clientModifiedTime);

  /// Get the formatted resolution time
  String? get formattedResolutionTime =>
      resolutionTime != null ? _formatDateTime(resolutionTime!) : null;

  /// Format a date time to a human-readable string
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// Create a copy of this content conflict with the given fields replaced
  ContentConflict copyWith({
    String? id,
    String? contentId,
    String? contentType,
    String? contentTitle,
    int? serverVersion,
    int? clientVersion,
    DateTime? serverModifiedTime,
    DateTime? clientModifiedTime,
    ContentConflictStatus? status,
    ContentConflictResolution? resolution,
    DateTime? resolutionTime,
    Map<String, dynamic>? serverData,
    Map<String, dynamic>? clientData,
    Map<String, dynamic>? mergedData,
  }) {
    return ContentConflict(
      id: id ?? this.id,
      contentId: contentId ?? this.contentId,
      contentType: contentType ?? this.contentType,
      contentTitle: contentTitle ?? this.contentTitle,
      serverVersion: serverVersion ?? this.serverVersion,
      clientVersion: clientVersion ?? this.clientVersion,
      serverModifiedTime: serverModifiedTime ?? this.serverModifiedTime,
      clientModifiedTime: clientModifiedTime ?? this.clientModifiedTime,
      status: status ?? this.status,
      resolution: resolution ?? this.resolution,
      resolutionTime: resolutionTime ?? this.resolutionTime,
      serverData: serverData ?? this.serverData,
      clientData: clientData ?? this.clientData,
      mergedData: mergedData ?? this.mergedData,
    );
  }

  /// Create a content conflict from JSON
  factory ContentConflict.fromJson(Map<String, dynamic> json) {
    return ContentConflict(
      id: json['id'],
      contentId: json['contentId'],
      contentType: json['contentType'],
      contentTitle: json['contentTitle'],
      serverVersion: json['serverVersion'],
      clientVersion: json['clientVersion'],
      serverModifiedTime: DateTime.parse(json['serverModifiedTime']),
      clientModifiedTime: DateTime.parse(json['clientModifiedTime']),
      status: ContentConflictStatus.values.firstWhere(
        (status) => status.name == json['status'],
        orElse: () => ContentConflictStatus.pending,
      ),
      resolution: json['resolution'] != null
          ? ContentConflictResolution.values.firstWhere(
              (resolution) => resolution.name == json['resolution'],
              orElse: () => ContentConflictResolution.askUser,
            )
          : null,
      resolutionTime: json['resolutionTime'] != null
          ? DateTime.parse(json['resolutionTime'])
          : null,
      serverData: Map<String, dynamic>.from(json['serverData']),
      clientData: Map<String, dynamic>.from(json['clientData']),
      mergedData: json['mergedData'] != null
          ? Map<String, dynamic>.from(json['mergedData'])
          : null,
    );
  }

  /// Convert this content conflict to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'contentId': contentId,
      'contentType': contentType,
      'contentTitle': contentTitle,
      'serverVersion': serverVersion,
      'clientVersion': clientVersion,
      'serverModifiedTime': serverModifiedTime.toIso8601String(),
      'clientModifiedTime': clientModifiedTime.toIso8601String(),
      'status': status.name,
      'resolution': resolution?.name,
      'resolutionTime': resolutionTime?.toIso8601String(),
      'serverData': serverData,
      'clientData': clientData,
      'mergedData': mergedData,
    };
  }

  /// Resolve the conflict with the server version
  ContentConflict resolveWithServerVersion() {
    return copyWith(
      status: ContentConflictStatus.resolved,
      resolution: ContentConflictResolution.serverWins,
      resolutionTime: DateTime.now(),
    );
  }

  /// Resolve the conflict with the client version
  ContentConflict resolveWithClientVersion() {
    return copyWith(
      status: ContentConflictStatus.resolved,
      resolution: ContentConflictResolution.clientWins,
      resolutionTime: DateTime.now(),
    );
  }

  /// Resolve the conflict with a merged version
  ContentConflict resolveWithMergedVersion(Map<String, dynamic> mergedData) {
    return copyWith(
      status: ContentConflictStatus.resolved,
      resolution: ContentConflictResolution.merge,
      resolutionTime: DateTime.now(),
      mergedData: mergedData,
    );
  }

  /// Defer the conflict resolution
  ContentConflict defer() {
    return copyWith(
      status: ContentConflictStatus.deferred,
      resolutionTime: DateTime.now(),
    );
  }
}
