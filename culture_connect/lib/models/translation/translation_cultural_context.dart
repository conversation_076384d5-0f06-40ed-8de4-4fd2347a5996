import 'package:culture_connect/models/translation/cultural_context_model.dart';

/// A model representing cultural context information for a translation
class TranslationCulturalContext {
  /// The list of cultural context notes
  final List<CulturalContextNote> notes;

  /// Whether cultural context information is available
  final bool hasContextInformation;

  /// The source language code
  final String sourceLanguage;

  /// The target language code
  final String targetLanguage;

  /// The source region or dialect (if applicable)
  final String? sourceRegion;

  /// The target region or dialect (if applicable)
  final String? targetRegion;

  /// General cultural notes about the language pair
  final String? generalCulturalNote;

  /// Creates a new translation cultural context
  const TranslationCulturalContext({
    required this.notes,
    required this.hasContextInformation,
    required this.sourceLanguage,
    required this.targetLanguage,
    this.sourceRegion,
    this.targetRegion,
    this.generalCulturalNote,
  });

  /// Creates an empty cultural context
  factory TranslationCulturalContext.empty({
    required String sourceLanguage,
    required String targetLanguage,
  }) {
    return TranslationCulturalContext(
      notes: const [],
      hasContextInformation: false,
      sourceLanguage: sourceLanguage,
      targetLanguage: targetLanguage,
    );
  }

  /// Creates a copy with some fields replaced
  TranslationCulturalContext copyWith({
    List<CulturalContextNote>? notes,
    bool? hasContextInformation,
    String? sourceLanguage,
    String? targetLanguage,
    String? sourceRegion,
    String? targetRegion,
    String? generalCulturalNote,
  }) {
    return TranslationCulturalContext(
      notes: notes ?? this.notes,
      hasContextInformation:
          hasContextInformation ?? this.hasContextInformation,
      sourceLanguage: sourceLanguage ?? this.sourceLanguage,
      targetLanguage: targetLanguage ?? this.targetLanguage,
      sourceRegion: sourceRegion ?? this.sourceRegion,
      targetRegion: targetRegion ?? this.targetRegion,
      generalCulturalNote: generalCulturalNote ?? this.generalCulturalNote,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'notes': notes.map((note) => note.toJson()).toList(),
      'hasContextInformation': hasContextInformation,
      'sourceLanguage': sourceLanguage,
      'targetLanguage': targetLanguage,
      'sourceRegion': sourceRegion,
      'targetRegion': targetRegion,
      'generalCulturalNote': generalCulturalNote,
    };
  }

  /// Creates from JSON
  factory TranslationCulturalContext.fromJson(Map<String, dynamic> json) {
    return TranslationCulturalContext(
      notes: (json['notes'] as List)
          .map((noteJson) =>
              CulturalContextNote.fromJson(noteJson as Map<String, dynamic>))
          .toList(),
      hasContextInformation: json['hasContextInformation'] as bool,
      sourceLanguage: json['sourceLanguage'] as String,
      targetLanguage: json['targetLanguage'] as String,
      sourceRegion: json['sourceRegion'] as String?,
      targetRegion: json['targetRegion'] as String?,
      generalCulturalNote: json['generalCulturalNote'] as String?,
    );
  }

  /// Get notes of a specific type
  List<CulturalContextNote> getNotesOfType(CulturalContextType type) {
    return notes.where((note) => note.type == type).toList();
  }

  /// Get notes for a specific text segment
  List<CulturalContextNote> getNotesForSegment(String segment) {
    return notes
        .where((note) =>
            note.textSegment.contains(segment) ||
            segment.contains(note.textSegment))
        .toList();
  }

  /// Get notes for a specific position in the text
  List<CulturalContextNote> getNotesForPosition(int position) {
    return notes
        .where(
            (note) => position >= note.startIndex && position <= note.endIndex)
        .toList();
  }

  /// Get sensitive notes
  List<CulturalContextNote> getSensitiveNotes() {
    return notes.where((note) => note.isSensitive).toList();
  }

  /// Check if there are any sensitive notes
  bool get hasSensitiveContent => notes.any((note) => note.isSensitive);

  /// Get the number of notes
  int get noteCount => notes.length;

  /// Check if there are any notes
  bool get hasNotes => notes.isNotEmpty;
}
