import 'package:culture_connect/models/translation/language_model.dart';

/// A model representing a participant's language preferences in a group conversation
class ParticipantLanguagePreference {
  /// The user ID of the participant
  final String userId;

  /// The display name of the participant
  final String displayName;

  /// The preferred language for the participant
  final LanguageModel preferredLanguage;

  /// Whether to auto-translate messages for this participant
  final bool autoTranslate;

  /// Whether to show original text alongside translations
  final bool showOriginalText;

  /// Whether to use dialect recognition for this participant
  final bool useDialectRecognition;

  /// The dialect or region for this participant (if applicable)
  final String? dialect;

  /// Creates a new participant language preference
  const ParticipantLanguagePreference({
    required this.userId,
    required this.displayName,
    required this.preferredLanguage,
    this.autoTranslate = true,
    this.showOriginalText = false,
    this.useDialectRecognition = false,
    this.dialect,
  });

  /// Creates a copy with some fields replaced
  ParticipantLanguagePreference copyWith({
    String? userId,
    String? displayName,
    LanguageModel? preferredLanguage,
    bool? autoTranslate,
    bool? showOriginalText,
    bool? useDialectRecognition,
    String? dialect,
  }) {
    return ParticipantLanguagePreference(
      userId: userId ?? this.userId,
      displayName: displayName ?? this.displayName,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      autoTranslate: autoTranslate ?? this.autoTranslate,
      showOriginalText: showOriginalText ?? this.showOriginalText,
      useDialectRecognition:
          useDialectRecognition ?? this.useDialectRecognition,
      dialect: dialect ?? this.dialect,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'displayName': displayName,
      'preferredLanguage': preferredLanguage.toJson(),
      'autoTranslate': autoTranslate,
      'showOriginalText': showOriginalText,
      'useDialectRecognition': useDialectRecognition,
      'dialect': dialect,
    };
  }

  /// Creates from JSON
  factory ParticipantLanguagePreference.fromJson(Map<String, dynamic> json) {
    return ParticipantLanguagePreference(
      userId: json['userId'] as String,
      displayName: json['displayName'] as String,
      preferredLanguage: LanguageModel.fromJson(
          json['preferredLanguage'] as Map<String, dynamic>),
      autoTranslate: json['autoTranslate'] as bool? ?? true,
      showOriginalText: json['showOriginalText'] as bool? ?? false,
      useDialectRecognition: json['useDialectRecognition'] as bool? ?? false,
      dialect: json['dialect'] as String?,
    );
  }
}

/// A model representing group translation settings
class GroupTranslationSettings {
  /// The group chat ID
  final String groupId;

  /// The language preferences for each participant
  final Map<String, ParticipantLanguagePreference> participantPreferences;

  /// Whether to auto-detect languages for messages
  final bool autoDetectLanguages;

  /// Whether to show translation indicators in the chat
  final bool showTranslationIndicators;

  /// Whether to enable real-time translation for the group
  final bool enableRealTimeTranslation;

  /// Whether to translate media captions
  final bool translateMediaCaptions;

  /// Whether to enable cultural context for translations
  final bool enableCulturalContext;

  /// Whether to enable slang and idiom detection
  final bool enableSlangIdiomDetection;

  /// Whether to enable pronunciation guidance
  final bool enablePronunciationGuidance;

  /// Creates a new group translation settings
  const GroupTranslationSettings({
    required this.groupId,
    required this.participantPreferences,
    this.autoDetectLanguages = true,
    this.showTranslationIndicators = true,
    this.enableRealTimeTranslation = true,
    this.translateMediaCaptions = true,
    this.enableCulturalContext = true,
    this.enableSlangIdiomDetection = true,
    this.enablePronunciationGuidance = true,
  });

  /// Creates a copy with some fields replaced
  GroupTranslationSettings copyWith({
    String? groupId,
    Map<String, ParticipantLanguagePreference>? participantPreferences,
    bool? autoDetectLanguages,
    bool? showTranslationIndicators,
    bool? enableRealTimeTranslation,
    bool? translateMediaCaptions,
    bool? enableCulturalContext,
    bool? enableSlangIdiomDetection,
    bool? enablePronunciationGuidance,
  }) {
    return GroupTranslationSettings(
      groupId: groupId ?? this.groupId,
      participantPreferences:
          participantPreferences ?? this.participantPreferences,
      autoDetectLanguages: autoDetectLanguages ?? this.autoDetectLanguages,
      showTranslationIndicators:
          showTranslationIndicators ?? this.showTranslationIndicators,
      enableRealTimeTranslation:
          enableRealTimeTranslation ?? this.enableRealTimeTranslation,
      translateMediaCaptions:
          translateMediaCaptions ?? this.translateMediaCaptions,
      enableCulturalContext:
          enableCulturalContext ?? this.enableCulturalContext,
      enableSlangIdiomDetection:
          enableSlangIdiomDetection ?? this.enableSlangIdiomDetection,
      enablePronunciationGuidance:
          enablePronunciationGuidance ?? this.enablePronunciationGuidance,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    final participantPreferencesJson = <String, dynamic>{};
    participantPreferences.forEach((key, value) {
      participantPreferencesJson[key] = value.toJson();
    });

    return {
      'groupId': groupId,
      'participantPreferences': participantPreferencesJson,
      'autoDetectLanguages': autoDetectLanguages,
      'showTranslationIndicators': showTranslationIndicators,
      'enableRealTimeTranslation': enableRealTimeTranslation,
      'translateMediaCaptions': translateMediaCaptions,
      'enableCulturalContext': enableCulturalContext,
      'enableSlangIdiomDetection': enableSlangIdiomDetection,
      'enablePronunciationGuidance': enablePronunciationGuidance,
    };
  }

  /// Creates from JSON
  factory GroupTranslationSettings.fromJson(Map<String, dynamic> json) {
    final participantPreferencesJson =
        json['participantPreferences'] as Map<String, dynamic>;
    final participantPreferences = <String, ParticipantLanguagePreference>{};

    participantPreferencesJson.forEach((key, value) {
      participantPreferences[key] =
          ParticipantLanguagePreference.fromJson(value as Map<String, dynamic>);
    });

    return GroupTranslationSettings(
      groupId: json['groupId'] as String,
      participantPreferences: participantPreferences,
      autoDetectLanguages: json['autoDetectLanguages'] as bool? ?? true,
      showTranslationIndicators:
          json['showTranslationIndicators'] as bool? ?? true,
      enableRealTimeTranslation:
          json['enableRealTimeTranslation'] as bool? ?? true,
      translateMediaCaptions: json['translateMediaCaptions'] as bool? ?? true,
      enableCulturalContext: json['enableCulturalContext'] as bool? ?? true,
      enableSlangIdiomDetection:
          json['enableSlangIdiomDetection'] as bool? ?? true,
      enablePronunciationGuidance:
          json['enablePronunciationGuidance'] as bool? ?? true,
    );
  }

  /// Get the preferred language for a participant
  LanguageModel? getPreferredLanguageForParticipant(String userId) {
    return participantPreferences[userId]?.preferredLanguage;
  }

  /// Add or update a participant's language preference
  GroupTranslationSettings updateParticipantPreference(
    ParticipantLanguagePreference preference,
  ) {
    final updatedPreferences =
        Map<String, ParticipantLanguagePreference>.from(participantPreferences);
    updatedPreferences[preference.userId] = preference;

    return copyWith(
      participantPreferences: updatedPreferences,
    );
  }

  /// Remove a participant's language preference
  GroupTranslationSettings removeParticipantPreference(String userId) {
    final updatedPreferences =
        Map<String, ParticipantLanguagePreference>.from(participantPreferences);
    updatedPreferences.remove(userId);

    return copyWith(
      participantPreferences: updatedPreferences,
    );
  }

  /// Get all unique languages used in the group
  List<LanguageModel> get uniqueLanguages {
    final languages = <String, LanguageModel>{};

    for (final preference in participantPreferences.values) {
      languages[preference.preferredLanguage.code] =
          preference.preferredLanguage;
    }

    return languages.values.toList();
  }

  /// Check if a language is used in the group
  bool isLanguageUsedInGroup(String languageCode) {
    return participantPreferences.values.any(
      (preference) => preference.preferredLanguage.code == languageCode,
    );
  }

  /// Get the number of participants using a specific language
  int getParticipantCountForLanguage(String languageCode) {
    return participantPreferences.values
        .where(
          (preference) => preference.preferredLanguage.code == languageCode,
        )
        .length;
  }

  /// Get participants using a specific language
  List<ParticipantLanguagePreference> getParticipantsForLanguage(
      String languageCode) {
    return participantPreferences.values
        .where(
          (preference) => preference.preferredLanguage.code == languageCode,
        )
        .toList();
  }
}

/// A model representing a group message translation
class GroupMessageTranslation {
  /// The message ID
  final String messageId;

  /// The original text of the message
  final String originalText;

  /// The language code of the original message
  final String originalLanguageCode;

  /// The translations for each target language
  final Map<String, String> translations;

  /// When the translations were created
  final DateTime translatedAt;

  /// Creates a new group message translation
  const GroupMessageTranslation({
    required this.messageId,
    required this.originalText,
    required this.originalLanguageCode,
    required this.translations,
    required this.translatedAt,
  });

  /// Creates a copy with some fields replaced
  GroupMessageTranslation copyWith({
    String? messageId,
    String? originalText,
    String? originalLanguageCode,
    Map<String, String>? translations,
    DateTime? translatedAt,
  }) {
    return GroupMessageTranslation(
      messageId: messageId ?? this.messageId,
      originalText: originalText ?? this.originalText,
      originalLanguageCode: originalLanguageCode ?? this.originalLanguageCode,
      translations: translations ?? this.translations,
      translatedAt: translatedAt ?? this.translatedAt,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'messageId': messageId,
      'originalText': originalText,
      'originalLanguageCode': originalLanguageCode,
      'translations': translations,
      'translatedAt': translatedAt.toIso8601String(),
    };
  }

  /// Creates from JSON
  factory GroupMessageTranslation.fromJson(Map<String, dynamic> json) {
    final translationsJson = json['translations'] as Map<String, dynamic>;
    final translations = <String, String>{};

    translationsJson.forEach((key, value) {
      translations[key] = value as String;
    });

    return GroupMessageTranslation(
      messageId: json['messageId'] as String,
      originalText: json['originalText'] as String,
      originalLanguageCode: json['originalLanguageCode'] as String,
      translations: translations,
      translatedAt: DateTime.parse(json['translatedAt'] as String),
    );
  }

  /// Add a translation for a target language
  GroupMessageTranslation addTranslation(
      String languageCode, String translatedText) {
    final updatedTranslations = Map<String, String>.from(translations);
    updatedTranslations[languageCode] = translatedText;

    return copyWith(
      translations: updatedTranslations,
      translatedAt: DateTime.now(),
    );
  }

  /// Get the translation for a target language
  String? getTranslationForLanguage(String languageCode) {
    return translations[languageCode];
  }

  /// Check if a translation exists for a target language
  bool hasTranslationForLanguage(String languageCode) {
    return translations.containsKey(languageCode);
  }

  /// Get all target languages with translations
  List<String> get targetLanguages {
    return translations.keys.toList();
  }
}
