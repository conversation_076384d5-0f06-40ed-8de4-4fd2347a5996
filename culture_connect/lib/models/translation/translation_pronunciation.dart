import 'package:culture_connect/models/translation/pronunciation_model.dart';

/// A model representing pronunciation information for a translation
class TranslationPronunciation {
  /// The list of pronunciation guides
  final List<PronunciationGuide> guides;

  /// Whether pronunciation information is available
  final bool hasPronunciationGuides;

  /// The source language code
  final String sourceLanguage;

  /// The target language code
  final String targetLanguage;

  /// The source region or dialect (if applicable)
  final String? sourceRegion;

  /// The target region or dialect (if applicable)
  final String? targetRegion;

  /// General pronunciation notes about the language
  final String? generalPronunciationNotes;

  /// Path to audio for the full text pronunciation (if available)
  final String? fullTextAudioPath;

  /// Creates a new translation pronunciation
  const TranslationPronunciation({
    required this.guides,
    required this.hasPronunciationGuides,
    required this.sourceLanguage,
    required this.targetLanguage,
    this.sourceRegion,
    this.targetRegion,
    this.generalPronunciationNotes,
    this.fullTextAudioPath,
  });

  /// Creates an empty pronunciation guide
  factory TranslationPronunciation.empty({
    required String sourceLanguage,
    required String targetLanguage,
  }) {
    return TranslationPronunciation(
      guides: const [],
      hasPronunciationGuides: false,
      sourceLanguage: sourceLanguage,
      targetLanguage: targetLanguage,
    );
  }

  /// Creates a copy with some fields replaced
  TranslationPronunciation copyWith({
    List<PronunciationGuide>? guides,
    bool? hasPronunciationGuides,
    String? sourceLanguage,
    String? targetLanguage,
    String? sourceRegion,
    String? targetRegion,
    String? generalPronunciationNotes,
    String? fullTextAudioPath,
  }) {
    return TranslationPronunciation(
      guides: guides ?? this.guides,
      hasPronunciationGuides:
          hasPronunciationGuides ?? this.hasPronunciationGuides,
      sourceLanguage: sourceLanguage ?? this.sourceLanguage,
      targetLanguage: targetLanguage ?? this.targetLanguage,
      sourceRegion: sourceRegion ?? this.sourceRegion,
      targetRegion: targetRegion ?? this.targetRegion,
      generalPronunciationNotes:
          generalPronunciationNotes ?? this.generalPronunciationNotes,
      fullTextAudioPath: fullTextAudioPath ?? this.fullTextAudioPath,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'guides': guides.map((guide) => guide.toJson()).toList(),
      'hasPronunciationGuides': hasPronunciationGuides,
      'sourceLanguage': sourceLanguage,
      'targetLanguage': targetLanguage,
      'sourceRegion': sourceRegion,
      'targetRegion': targetRegion,
      'generalPronunciationNotes': generalPronunciationNotes,
      'fullTextAudioPath': fullTextAudioPath,
    };
  }

  /// Creates from JSON
  factory TranslationPronunciation.fromJson(Map<String, dynamic> json) {
    return TranslationPronunciation(
      guides: (json['guides'] as List)
          .map((guideJson) =>
              PronunciationGuide.fromJson(guideJson as Map<String, dynamic>))
          .toList(),
      hasPronunciationGuides: json['hasPronunciationGuides'] as bool,
      sourceLanguage: json['sourceLanguage'] as String,
      targetLanguage: json['targetLanguage'] as String,
      sourceRegion: json['sourceRegion'] as String?,
      targetRegion: json['targetRegion'] as String?,
      generalPronunciationNotes: json['generalPronunciationNotes'] as String?,
      fullTextAudioPath: json['fullTextAudioPath'] as String?,
    );
  }

  /// Get guides of a specific type
  List<PronunciationGuide> getGuidesOfType(PronunciationGuideType type) {
    return guides.where((guide) => guide.type == type).toList();
  }

  /// Get guides for a specific text segment
  List<PronunciationGuide> getGuidesForSegment(String segment) {
    return guides
        .where((guide) =>
            guide.text.contains(segment) || segment.contains(guide.text))
        .toList();
  }

  /// Get guides for a specific position in the text
  List<PronunciationGuide> getGuidesForPosition(int position) {
    return guides
        .where((guide) =>
            position >= guide.startIndex && position <= guide.endIndex)
        .toList();
  }

  /// Get guides by difficulty level
  List<PronunciationGuide> getGuidesByDifficulty(
      PronunciationDifficulty difficulty) {
    return guides.where((guide) => guide.difficulty == difficulty).toList();
  }

  /// Get difficult guides (moderate or higher difficulty)
  List<PronunciationGuide> getDifficultGuides() {
    return guides
        .where((guide) =>
            guide.difficulty == PronunciationDifficulty.moderate ||
            guide.difficulty == PronunciationDifficulty.difficult ||
            guide.difficulty == PronunciationDifficulty.veryDifficult)
        .toList();
  }

  /// Get guides with audio
  List<PronunciationGuide> getGuidesWithAudio() {
    return guides.where((guide) => guide.audioPath != null).toList();
  }

  /// Get the number of guides
  int get guideCount => guides.length;

  /// Get the number of difficult guides
  int get difficultGuideCount => getDifficultGuides().length;

  /// Get the number of guides with audio
  int get guidesWithAudioCount => getGuidesWithAudio().length;

  /// Check if there are any guides
  bool get hasGuides => guides.isNotEmpty;

  /// Check if there are any difficult guides
  bool get hasDifficultGuides => getDifficultGuides().isNotEmpty;

  /// Check if there are any guides with audio
  bool get hasGuidesWithAudio => getGuidesWithAudio().isNotEmpty;

  /// Check if full text audio is available
  bool get hasFullTextAudio => fullTextAudioPath != null;
}
