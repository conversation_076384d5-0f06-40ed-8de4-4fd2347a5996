import 'package:flutter/material.dart';

/// Confidence level for a translation
enum ConfidenceLevel {
  /// Very high confidence (90-100%)
  veryHigh,

  /// High confidence (75-90%)
  high,

  /// Medium confidence (50-75%)
  medium,

  /// Low confidence (25-50%)
  low,

  /// Very low confidence (0-25%)
  veryLow,
}

/// Extension on ConfidenceLevel to provide additional functionality
extension ConfidenceLevelExtension on ConfidenceLevel {
  /// Get the display name of the confidence level
  String get displayName {
    switch (this) {
      case ConfidenceLevel.veryHigh:
        return 'Very High';
      case ConfidenceLevel.high:
        return 'High';
      case ConfidenceLevel.medium:
        return 'Medium';
      case ConfidenceLevel.low:
        return 'Low';
      case ConfidenceLevel.veryLow:
        return 'Very Low';
    }
  }

  /// Get the color associated with the confidence level
  Color get color {
    switch (this) {
      case ConfidenceLevel.veryHigh:
        return Colors.green;
      case ConfidenceLevel.high:
        return Colors.lightGreen;
      case ConfidenceLevel.medium:
        return Colors.amber;
      case ConfidenceLevel.low:
        return Colors.orange;
      case ConfidenceLevel.veryLow:
        return Colors.red;
    }
  }

  /// Get the icon associated with the confidence level
  IconData get icon {
    switch (this) {
      case ConfidenceLevel.veryHigh:
        return Icons.verified;
      case ConfidenceLevel.high:
        return Icons.thumb_up;
      case ConfidenceLevel.medium:
        return Icons.thumbs_up_down;
      case ConfidenceLevel.low:
        return Icons.thumb_down;
      case ConfidenceLevel.veryLow:
        return Icons.dangerous;
    }
  }

  /// Get the confidence level from a confidence score (0.0 to 1.0)
  static ConfidenceLevel fromScore(double score) {
    if (score >= 0.9) {
      return ConfidenceLevel.veryHigh;
    } else if (score >= 0.75) {
      return ConfidenceLevel.high;
    } else if (score >= 0.5) {
      return ConfidenceLevel.medium;
    } else if (score >= 0.25) {
      return ConfidenceLevel.low;
    } else {
      return ConfidenceLevel.veryLow;
    }
  }
}

/// A model representing confidence information for a translation
class TranslationConfidenceModel {
  /// Overall confidence score (0.0 to 1.0)
  final double overallScore;

  /// Confidence level based on the overall score
  final ConfidenceLevel confidenceLevel;

  /// Confidence scores for individual segments of the translation
  final List<SegmentConfidence>? segmentScores;

  /// Factors that influenced the confidence score
  final List<ConfidenceFactor>? factors;

  /// Creates a new translation confidence model
  const TranslationConfidenceModel({
    required this.overallScore,
    required this.confidenceLevel,
    this.segmentScores,
    this.factors,
  });

  /// Creates a translation confidence model from an overall score
  factory TranslationConfidenceModel.fromScore(double score) {
    return TranslationConfidenceModel(
      overallScore: score,
      confidenceLevel: ConfidenceLevelExtension.fromScore(score),
    );
  }

  /// Creates a copy with some fields replaced
  TranslationConfidenceModel copyWith({
    double? overallScore,
    ConfidenceLevel? confidenceLevel,
    List<SegmentConfidence>? segmentScores,
    List<ConfidenceFactor>? factors,
  }) {
    return TranslationConfidenceModel(
      overallScore: overallScore ?? this.overallScore,
      confidenceLevel: confidenceLevel ?? this.confidenceLevel,
      segmentScores: segmentScores ?? this.segmentScores,
      factors: factors ?? this.factors,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'overallScore': overallScore,
      'confidenceLevel': confidenceLevel.index,
      'segmentScores':
          segmentScores?.map((segment) => segment.toJson()).toList(),
      'factors': factors?.map((factor) => factor.toJson()).toList(),
    };
  }

  /// Creates from JSON
  factory TranslationConfidenceModel.fromJson(Map<String, dynamic> json) {
    return TranslationConfidenceModel(
      overallScore: (json['overallScore'] as num).toDouble(),
      confidenceLevel: ConfidenceLevel.values[json['confidenceLevel'] as int],
      segmentScores: json['segmentScores'] != null
          ? (json['segmentScores'] as List)
              .map((e) => SegmentConfidence.fromJson(e as Map<String, dynamic>))
              .toList()
          : null,
      factors: json['factors'] != null
          ? (json['factors'] as List)
              .map((e) => ConfidenceFactor.fromJson(e as Map<String, dynamic>))
              .toList()
          : null,
    );
  }
}

/// A model representing confidence information for a segment of a translation
class SegmentConfidence {
  /// The text segment
  final String text;

  /// Start index in the translated text
  final int startIndex;

  /// End index in the translated text
  final int endIndex;

  /// Confidence score for this segment (0.0 to 1.0)
  final double score;

  /// Creates a new segment confidence
  const SegmentConfidence({
    required this.text,
    required this.startIndex,
    required this.endIndex,
    required this.score,
  });

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'startIndex': startIndex,
      'endIndex': endIndex,
      'score': score,
    };
  }

  /// Creates from JSON
  factory SegmentConfidence.fromJson(Map<String, dynamic> json) {
    return SegmentConfidence(
      text: json['text'] as String,
      startIndex: json['startIndex'] as int,
      endIndex: json['endIndex'] as int,
      score: (json['score'] as num).toDouble(),
    );
  }
}

/// A model representing a factor that influenced the confidence score
class ConfidenceFactor {
  /// The type of factor
  final String type;

  /// Description of the factor
  final String description;

  /// Impact on the confidence score (-1.0 to 1.0)
  final double impact;

  /// Creates a new confidence factor
  const ConfidenceFactor({
    required this.type,
    required this.description,
    required this.impact,
  });

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'description': description,
      'impact': impact,
    };
  }

  /// Creates from JSON
  factory ConfidenceFactor.fromJson(Map<String, dynamic> json) {
    return ConfidenceFactor(
      type: json['type'] as String,
      description: json['description'] as String,
      impact: (json['impact'] as num).toDouble(),
    );
  }
}
