import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/core/theme/app_theme.dart';

/// Custom Material Design 3 UI components for payment providers
class ProviderUIComponents {
  /// Create a payment provider card with Material Design 3 styling
  static Widget buildProviderCard({
    required BuildContext context,
    required PaymentProvider provider,
    required bool isSelected,
    required bool isRecommended,
    required bool isEnabled,
    required VoidCallback onTap,
    String? subtitle,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      elevation: isSelected ? 8 : 2,
      shadowColor: colorScheme.shadowithAlpha(51),
      surfaceTintColor: isSelected ? colorScheme.primary : null,
      child: InkWell(
        onTap: isEnabled
            ? () {
                HapticFeedback.selectionClick();
                onTap();
              }
            : null,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: isSelected
                ? Border.all(color: colorScheme.primary, width: 2)
                : Border.all(
                    color: colorScheme.outlineithAlpha(77), width: 1),
          ),
          child: Row(
            children: [
              // Provider icon
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: _getProviderColor(provider, colorScheme),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getProviderIcon(provider),
                  color: Colorshite,
                  size: 24,
                ),
              ),

              const SizedBox(width: 16),

              // Provider details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          _getProviderDisplayName(provider),
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight600,
                            color: isEnabled
                                ? colorScheme.onSurface
                                : colorScheme.onSurfaceithAlpha(153),
                          ),
                        ),
                        if (isRecommended) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: colorScheme.primaryContainer,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'Recommended',
                              style: theme.textTheme.labelSmall?.copyWith(
                                color: colorScheme.onPrimaryContainer,
                                fontWeight: FontWeight500,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    if (subtitle != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              // Selection indicator
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: colorScheme.primary,
                  size: 24,
                )
              else if (!isEnabled)
                Icon(
                  Icons.block,
                  color: colorScheme.onSurfaceithAlpha(153),
                  size: 24,
                ),
            ],
          ),
        ),
      ),
    )
        .animate(target: isSelected ? 1 : 0)
        .scale(duration: AppTheme.animationDuration)
        .shimmer(
          duration: const Duration(milliseconds: 1500),
          color: colorScheme.primaryithAlpha(51),
        );
  }

  /// Create a payment amount display with currency formatting
  static Widget buildAmountDisplay({
    required BuildContext context,
    required double amount,
    required String currency,
    String? originalAmount,
    String? originalCurrency,
    String? exchangeRate,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainerithAlpha(77),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colorScheme.primaryithAlpha(77),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment Amount',
            style: theme.textTheme.labelLarge?.copyWith(
              color: colorScheme.onSurfaceVariant,
              fontWeight: FontWeight500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _formatCurrency(amount, currency),
            style: theme.textThemeeadlineMedium?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (originalAmount != null && originalCurrency != null) ...[
            const SizedBox(height: 4),
            Text(
              'Original: ${_formatCurrency(double.parse(originalAmount), originalCurrency)}',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
          ],
          if (exchangeRate != null) ...[
            const SizedBox(height: 4),
            Text(
              'Exchange Rate: $exchangeRate',
              style: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Create a payment status indicator with animations
  static Widget buildStatusIndicator({
    required BuildContext context,
    required PaymentStatus status,
    String? message,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    final statusConfig = _getStatusConfig(status, colorScheme);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: statusConfig.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: statusConfig.borderColor,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (status == PaymentStatus.processing)
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor:
                    AlwaysStoppedAnimation<Color>(statusConfig.iconColor),
              ),
            )
          else
            Icon(
              statusConfig.icon,
              color: statusConfig.iconColor,
              size: 16,
            ),
          const SizedBox(width: 8),
          Text(
            message ?? _getStatusDisplayName(status),
            style: theme.textTheme.labelMedium?.copyWith(
              color: statusConfig.textColor,
              fontWeight: FontWeight500,
            ),
          ),
        ],
      ),
    ).animate(target: status == PaymentStatus.processing ? 1 : 0).shimmer(
          duration: const Duration(milliseconds: 2000),
          color: statusConfig.iconColorithAlpha(77),
        );
  }

  /// Create a security badge for payment providers
  static Widget buildSecurityBadge({
    required BuildContext context,
    required List<String> securityFeatures,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighestithAlpha(128),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colorScheme.outlineithAlpha(77),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.security,
            color: colorScheme.primary,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            'Secured by ${securityFeatures.join(', ')}',
            style: theme.textTheme.labelSmall?.copyWith(
              color: colorScheme.onSurfaceVariant,
              fontWeight: FontWeight500,
            ),
          ),
        ],
      ),
    );
  }

  /// Get provider-specific color
  static Color _getProviderColor(
      PaymentProvider provider, ColorScheme colorScheme) {
    switch (provider) {
      case PaymentProvider.stripe:
        return const Color(0xFF635BFF);
      case PaymentProvider.paystack:
        return const Color(0xFF0FA958);
      case PaymentProvider.busha:
        return const Color(0xFFFF6B35);
    }
  }

  /// Get provider-specific icon
  static IconData _getProviderIcon(PaymentProvider provider) {
    switch (provider) {
      case PaymentProvider.stripe:
        return Icons.credit_card;
      case PaymentProvider.paystack:
        return Icons.account_balance;
      case PaymentProvider.busha:
        return Icons.currency_bitcoin;
    }
  }

  /// Get provider display name
  static String _getProviderDisplayName(PaymentProvider provider) {
    switch (provider) {
      case PaymentProvider.stripe:
        return 'Stripe';
      case PaymentProvider.paystack:
        return 'Paystack';
      case PaymentProvider.busha:
        return 'Busha.co';
    }
  }

  /// Get payment status display name
  static String _getStatusDisplayName(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.processing:
        return 'Processing';
      case PaymentStatus.successful:
        return 'Completed';
      case PaymentStatus.failed:
        return 'Failed';
      case PaymentStatus.cancelled:
        return 'Cancelled';
      case PaymentStatus.expired:
        return 'Expired';
    }
  }

  /// Get status configuration for styling
  static _StatusConfig _getStatusConfig(
      PaymentStatus status, ColorScheme colorScheme) {
    switch (status) {
      case PaymentStatus.pending:
        return _StatusConfig(
          icon: Icons.schedule,
          iconColor: colorScheme.primary,
          textColor: colorScheme.onSurfaceVariant,
          backgroundColor: colorScheme.primaryContainerithAlpha(77),
          borderColor: colorScheme.primaryithAlpha(77),
        );
      case PaymentStatus.processing:
        return _StatusConfig(
          icon: Icons.sync,
          iconColor: colorScheme.primary,
          textColor: colorScheme.onSurfaceVariant,
          backgroundColor: colorScheme.primaryContainerithAlpha(77),
          borderColor: colorScheme.primaryithAlpha(77),
        );
      case PaymentStatus.successful:
        return _StatusConfig(
          icon: Icons.check_circle,
          iconColor: const Color(0xFF4CAF50),
          textColor: const Color(0xFF2E7D32),
          backgroundColor: const Color(0xFF4CAF50)ithAlpha(26),
          borderColor: const Color(0xFF4CAF50)ithAlpha(77),
        );
      case PaymentStatus.failed:
        return _StatusConfig(
          icon: Icons.error,
          iconColor: colorScheme.error,
          textColor: colorScheme.onErrorContainer,
          backgroundColor: colorScheme.errorContainerithAlpha(77),
          borderColor: colorScheme.errorithAlpha(77),
        );
      case PaymentStatus.cancelled:
        return _StatusConfig(
          icon: Icons.cancel,
          iconColor: colorScheme.onSurfaceVariant,
          textColor: colorScheme.onSurfaceVariant,
          backgroundColor: colorScheme.surfaceContainerHighestithAlpha(77),
          borderColor: colorScheme.outlineithAlpha(77),
        );
      case PaymentStatus.expired:
        return _StatusConfig(
          icon: Icons.access_time,
          iconColor: colorScheme.error,
          textColor: colorScheme.onErrorContainer,
          backgroundColor: colorScheme.errorContainerithAlpha(77),
          borderColor: colorScheme.errorithAlpha(77),
        );
    }
  }

  /// Format currency with proper locale formatting
  static String _formatCurrency(double amount, String currency) {
    // TODO: Implement proper locale-based currency formatting
    // This is a simplified implementation
    switch (currency.toUpperCase()) {
      case 'USD':
        return '\$${amount.toStringAsFixed(2)}';
      case 'NGN':
        return '₦${amount.toStringAsFixed(2)}';
      case 'EUR':
        return '€${amount.toStringAsFixed(2)}';
      case 'GBP':
        return '£${amount.toStringAsFixed(2)}';
      case 'BTC':
        return '₿${amount.toStringAsFixed(8)}';
      case 'ETH':
        return 'Ξ${amount.toStringAsFixed(6)}';
      default:
        return '$currency ${amount.toStringAsFixed(2)}';
    }
  }
}

/// Configuration for payment status styling
class _StatusConfig {
  final IconData icon;
  final Color iconColor;
  final Color textColor;
  final Color backgroundColor;
  final Color borderColor;

  const _StatusConfig({
    required this.icon,
    required this.iconColor,
    required this.textColor,
    required this.backgroundColor,
    required this.borderColor,
  });
}
