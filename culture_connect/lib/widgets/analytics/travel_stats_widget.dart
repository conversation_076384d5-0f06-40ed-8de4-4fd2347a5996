// Flutter imports
import 'package:flutter/material.dart';

// Package imports

// Project imports
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/models/analytics/travel_analytics_model.dart';
import 'package:culture_connect/models/achievement/user_achievement.dart';

/// Reusable widget for displaying travel statistics
class TravelStatsCard extends StatelessWidget {
  /// The title of the stat
  final String title;

  /// The value to display
  final String value;

  /// The icon to display
  final IconData icon;

  /// The color theme for the card
  final Color color;

  /// Optional subtitle
  final String? subtitle;

  /// Whether to show a trend indicator
  final bool showTrend;

  /// The trend direction (positive/negative)
  final bool isPositiveTrend;

  /// The trend percentage
  final double? trendPercentage;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Creates a travel stats card
  const TravelStatsCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.subtitle,
    this.showTrend = false,
    this.isPositiveTrend = true,
    this.trendPercentage,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        side: BorderSide(
          color: theme.colorScheme.outline.withAlpha(51),
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withAlpha(26),
                      borderRadius:
                          BorderRadius.circular(AppTheme.borderRadiusSmall),
                    ),
                    child: Icon(
                      icon,
                      color: color,
                      size: 20,
                    ),
                  ),
                  const Spacer(),
                  if (showTrend && trendPercentage != null)
                    _buildTrendIndicator(theme),
                ],
              ),
              const SizedBox(height: AppTheme.spacingMedium),
              Text(
                value,
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: AppTheme.spacingSmall),
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              if (subtitle != null) ...[
                const SizedBox(height: AppTheme.spacingSmall),
                Text(
                  subtitle!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTrendIndicator(ThemeData theme) {
    final trendColor =
        isPositiveTrend ? AppTheme.successColor : AppTheme.errorColor;
    final trendIcon = isPositiveTrend ? Icons.trending_up : Icons.trending_down;

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 6,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: trendColor.withAlpha(26),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            trendIcon,
            color: trendColor,
            size: 12,
          ),
          const SizedBox(width: 2),
          Text(
            '${trendPercentage!.toStringAsFixed(1)}%',
            style: theme.textTheme.labelSmall?.copyWith(
              color: trendColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

/// Widget for displaying spending charts
class SpendingChart extends StatelessWidget {
  /// The spending data to display
  final TravelSpending spendingData;

  /// The chart type
  final SpendingChartType chartType;

  /// The height of the chart
  final double height;

  /// Creates a spending chart
  const SpendingChart({
    super.key,
    required this.spendingData,
    this.chartType = SpendingChartType.pie,
    this.height = 200,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      height: height,
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.outline.withAlpha(51),
        ),
      ),
      child: Column(
        children: [
          Text(
            'Spending Breakdown',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Expanded(
            child: _buildChartContent(theme),
          ),
        ],
      ),
    );
  }

  Widget _buildChartContent(ThemeData theme) {
    switch (chartType) {
      case SpendingChartType.pie:
        return _buildPieChart(theme);
      case SpendingChartType.bar:
        return _buildBarChart(theme);
      case SpendingChartType.line:
        return _buildLineChart(theme);
    }
  }

  Widget _buildPieChart(ThemeData theme) {
    // TODO: Implement actual pie chart with charts library
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.pie_chart,
            size: 48,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            'Pie chart visualization',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBarChart(ThemeData theme) {
    // TODO: Implement actual bar chart with charts library
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bar_chart,
            size: 48,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            'Bar chart visualization',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLineChart(ThemeData theme) {
    // TODO: Implement actual line chart with charts library
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.show_chart,
            size: 48,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            'Line chart visualization',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
}

/// Widget for displaying achievement progress
class AchievementProgressWidget extends StatelessWidget {
  /// The achievement to display
  final UserAchievement achievement;

  /// Whether to show the progress bar
  final bool showProgress;

  /// Whether to use compact layout
  final bool isCompact;

  /// Creates an achievement progress widget
  const AchievementProgressWidget({
    super.key,
    required this.achievement,
    this.showProgress = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(
          isCompact ? AppTheme.spacingSmall : AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: achievement.isUnlocked
            ? achievement.achievement.color.withAlpha(13)
            : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: achievement.isUnlocked
              ? achievement.achievement.color.withAlpha(51)
              : theme.colorScheme.outline.withAlpha(51),
        ),
      ),
      child: isCompact ? _buildCompactContent(theme) : _buildFullContent(theme),
    );
  }

  Widget _buildCompactContent(ThemeData theme) {
    return Row(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: achievement.isUnlocked
                ? achievement.achievement.color
                : theme.colorScheme.surfaceContainerHighest,
            shape: BoxShape.circle,
          ),
          child: Icon(
            achievement.achievement.icon,
            color: achievement.isUnlocked
                ? Colors.white
                : theme.colorScheme.onSurfaceVariant,
            size: 16,
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                achievement.achievement.title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              if (showProgress && !achievement.isUnlocked)
                LinearProgressIndicator(
                  value: achievement.progress,
                  backgroundColor: theme.colorScheme.surfaceContainerHighest,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    achievement.achievement.color,
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFullContent(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: achievement.isUnlocked
                    ? achievement.achievement.color
                    : theme.colorScheme.surfaceContainerHighest,
                shape: BoxShape.circle,
              ),
              child: Icon(
                achievement.achievement.icon,
                color: achievement.isUnlocked
                    ? Colors.white
                    : theme.colorScheme.onSurfaceVariant,
                size: 24,
              ),
            ),
            const SizedBox(width: AppTheme.spacingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    achievement.achievement.title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  Text(
                    achievement.achievement.description,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
        if (showProgress && !achievement.isUnlocked) ...[
          const SizedBox(height: AppTheme.spacingMedium),
          Row(
            children: [
              Expanded(
                child: LinearProgressIndicator(
                  value: achievement.progress,
                  backgroundColor: theme.colorScheme.surfaceContainerHighest,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    achievement.achievement.color,
                  ),
                ),
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              Text(
                '${(achievement.progress * 100).toStringAsFixed(0)}%',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }
}

/// Enum for spending chart types
enum SpendingChartType {
  /// Pie chart
  pie,

  /// Bar chart
  bar,

  /// Line chart
  line,
}

// TODO: Backend Integration - Travel Stats Widget API
// GET /api/v1/analytics/charts?type=spending&period=yearly&userId={id}
// Headers: Authorization: Bearer {token}
// Response: {
//   "chartData": ChartDataPoint[],
//   "chartType": "pie" | "bar" | "line",
//   "metadata": {
//     "totalValue": number,
//     "dataPoints": number,
//     "lastUpdated": "ISO8601"
//   }
// }

// TODO: Achievement Progress Visualization API
// GET /api/v1/achievements/progress?userId={id}&includeProjections=true
// Headers: Authorization: Bearer {token}
// Response: {
//   "achievements": UserAchievement[],
//   "progressSummary": {
//     "totalUnlocked": number,
//     "totalAvailable": number,
//     "completionPercentage": number
//   },
//   "projections": AchievementProjection[]
// }

// TODO: Real-time Stats Updates WebSocket
// WS /ws/analytics/stats-updates?userId={id}
// Receives: {
//   "type": "achievement_progress" | "spending_update" | "stats_refresh",
//   "data": any,
//   "timestamp": "ISO8601"
// }
