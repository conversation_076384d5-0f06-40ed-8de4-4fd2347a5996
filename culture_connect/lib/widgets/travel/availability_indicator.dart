/// Widget for displaying real-time availability information for travel services
///
/// This widget shows the current availability status of a travel service,
/// including whether it's available, how many units are available, and when
/// it will next be available if it's currently unavailable.
library availability_indicator;

// Package imports
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports - Services
import 'package:culture_connect/services/travel_availability_service.dart';

// Project imports - Theme
import 'package:culture_connect/theme/app_theme.dart';

/// Widget to display real-time availability information
class AvailabilityIndicator extends ConsumerWidget {
  /// The type of service (hotel, flight, car, restaurant, security, cruise)
  final String serviceType;

  /// The ID of the service
  final String serviceId;

  /// Whether to show detailed information
  final bool showDetails;

  /// Whether to show a refresh button
  final bool showRefreshButton;

  /// Creates a new availability indicator
  const AvailabilityIndicator({
    super.key,
    required this.serviceType,
    required this.serviceId,
    this.showDetails = true,
    this.showRefreshButton = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final availabilityAsync = ref.watch(serviceAvailabilityProvider(
      MapEntry(serviceType, serviceId),
    ));

    return availabilityAsync.when(
      data: (availability) =>
          _buildAvailabilityInfo(context, ref, availability),
      loading: () => _buildLoadingIndicator(),
      error: (error, stackTrace) => _buildErrorIndicator(context, ref, error),
    );
  }

  /// Builds the loading indicator widget
  ///
  /// This widget is shown while the availability is being checked.
  /// It includes a spinner and a loading message.
  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            'Checking availability...',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the error indicator widget
  ///
  /// This widget is shown when there's an error checking availability.
  /// It includes an error icon, a message, and optionally a refresh button.
  Widget _buildErrorIndicator(
      BuildContext context, WidgetRef ref, Object error) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: AppTheme.errorColor.withAlpha(25),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppTheme.errorColor.withAlpha(75), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.error_outline,
            size: 16,
            color: AppTheme.errorColor,
          ),
          const SizedBox(width: 8),
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Unable to check availability',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.errorColor,
                  ),
                ),
                if (kDebugMode) // Only show error details in debug mode
                  Text(
                    error.toString(),
                    style: TextStyle(
                      fontSize: 10,
                      color: AppTheme.errorColor.withAlpha(180),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
          if (showRefreshButton) ...[
            const SizedBox(width: 8),
            _buildRefreshButton(context, ref),
          ],
        ],
      ),
    );
  }

  /// Builds the availability information widget
  ///
  /// This widget shows the current availability status, including whether
  /// the service is available, how many units are available, and when it
  /// will next be available if it's currently unavailable.
  Widget _buildAvailabilityInfo(
      BuildContext context, WidgetRef ref, AvailabilityInfo availability) {
    final isAvailable = availability.isAvailable;
    final backgroundColor =
        isAvailable ? Colors.green[100] : Colors.orange[100];
    final textColor = isAvailable ? Colors.green[700] : Colors.orange[700];
    final iconData =
        isAvailable ? Icons.check_circle_outline : Icons.access_time;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
        border: availability.isFromCache
            ? Border.all(color: Colors.grey[400]!, width: 1)
            : null,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            iconData,
            size: 16,
            color: textColor,
          ),
          const SizedBox(width: 8),
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  isAvailable
                      ? 'Available${availability.availableCount > 0 ? ' (${availability.availableCount})' : ''}'
                      : 'Not available',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: textColor,
                  ),
                ),
                if (showDetails) ...[
                  if (!isAvailable && availability.nextAvailableDate != null)
                    Text(
                      'Next available: ${_formatDate(availability.nextAvailableDate!)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: textColor,
                      ),
                    ),
                  if (availability.isFromCache)
                    Text(
                      'Last updated: ${_formatDate(availability.timestamp)}',
                      style: TextStyle(
                        fontSize: 10,
                        fontStyle: FontStyle.italic,
                        color: Colors.grey[600],
                      ),
                    ),
                ],
              ],
            ),
          ),
          if (showRefreshButton) ...[
            const SizedBox(width: 8),
            _buildRefreshButton(context, ref),
          ],
        ],
      ),
    );
  }

  /// Builds a refresh button that invalidates the availability provider
  ///
  /// This forces a refresh of the availability data.
  Widget _buildRefreshButton(BuildContext context, WidgetRef ref) {
    return InkWell(
      onTap: () {
        // Invalidate the provider to force a refresh
        ref.invalidate(
            serviceAvailabilityProvider(MapEntry(serviceType, serviceId)));
      },
      borderRadius: BorderRadius.circular(12),
      child: const Padding(
        padding: EdgeInsets.all(4),
        child: Icon(
          Icons.refresh,
          size: 16,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  /// Formats a date in a user-friendly way
  ///
  /// Returns "Today" or "Tomorrow" for those dates, otherwise returns
  /// a formatted date string in the format "MM/DD/YYYY".
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final dateToCheck = DateTime(date.year, date.month, date.day);

    if (dateToCheck == today) {
      return 'Today';
    } else if (dateToCheck == tomorrow) {
      return 'Tomorrow';
    } else {
      return '${date.month}/${date.day}/${date.year}';
    }
  }
}
