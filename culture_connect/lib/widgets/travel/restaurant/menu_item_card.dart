// Dart imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/restaurant.dart';
import 'package:culture_connect/providers/services_providers.dart';
import 'package:culture_connect/widgets/common/rating_stars.dart';

/// A widget that displays a menu item as a card with add to order functionality
class MenuItemCard extends ConsumerStatefulWidget {
  /// The menu item to display
  final MenuItem menuItem;

  /// Callback when the item is added to the order
  final Function(MenuItem, int)? onAddToOrder;

  /// Whether to show the add to order button
  final bool showAddToOrderButton;

  /// Whether to show the rating
  final bool showRating;

  /// The rating of the menu item (0-5)
  final double? rating;

  /// The number of ratings
  final int? ratingCount;

  /// Creates a new menu item card
  const MenuItemCard({
    super.key,
    required this.menuItem,
    this.onAddToOrder,
    this.showAddToOrderButton = true,
    this.showRating = false,
    this.rating,
    this.ratingCount,
  });

  @override
  ConsumerState<MenuItemCard> createState() => _MenuItemCardState();
}

class _MenuItemCardState extends ConsumerState<MenuItemCard>
    with SingleTickerProviderStateMixin {
  // Logger tag for this widget
  static const String _logTag = 'MenuItemCard';

  // Quantity of the item to add to order
  int _quantity = 1;

  // Animation controller for the add to order button
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    final logger = ref.read(loggingServiceProvider);

    try {
      logger.debug(
          _logTag, 'Initializing menu item card for ${widget.menuItem.name}');

      // Initialize animation controller
      _animationController = AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 200),
      );

      // Create scale animation
      _scaleAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: Curves.easeInOut,
        ),
      );
    } catch (e, stackTrace) {
      logger.error(_logTag, 'Error initializing menu item card', e, stackTrace);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final logger = ref.read(loggingServiceProvider);

    try {
      logger.debug(
          _logTag, 'Building menu item card for ${widget.menuItem.name}');

      return Card(
        margin: const EdgeInsets.only(bottom: 12),
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: theme.colorScheme.outlineVariant,
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Menu item content
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Menu item image
                  if (widget.menuItem.imageUrl != null &&
                      widget.menuItem.imageUrl!.isNotEmpty)
                    _buildMenuItemImage(context),

                  SizedBox(
                      width: widget.menuItem.imageUrl != null &&
                              widget.menuItem.imageUrl!.isNotEmpty
                          ? 16
                          : 0),

                  // Menu item details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Name and price
                        _buildNameAndPrice(context),

                        const SizedBox(height: 4),

                        // Rating if available
                        if (widget.showRating && widget.rating != null)
                          _buildRating(context),

                        const SizedBox(height: 4),

                        // Description
                        Text(
                          widget.menuItem.description,
                          style: theme.textTheme.bodyMedium,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),

                        const SizedBox(height: 8),

                        // Dietary tags
                        _buildDietaryTags(context),
                      ],
                    ),
                  ),
                ],
              ),

              // Add to order section
              if (widget.showAddToOrderButton) _buildAddToOrderSection(context),
            ],
          ),
        ),
      );
    } catch (e, stackTrace) {
      logger.error(_logTag, 'Error building menu item card', e, stackTrace);

      // Fallback UI in case of error
      return Card(
        margin: const EdgeInsets.only(bottom: 12),
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: theme.colorScheme.outlineVariant,
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(
                Icons.error_outline,
                color: theme.colorScheme.error,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Error displaying menu item',
                  style: theme.textTheme.bodyMedium,
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  /// Builds the menu item image
  Widget _buildMenuItemImage(BuildContext context) {
    final theme = Theme.of(context);
    final logger = ref.read(loggingServiceProvider);

    try {
      return Hero(
        tag: 'menu_item_${widget.menuItem.id}',
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: SizedBox(
            width: 80,
            height: 80,
            child: Image.network(
              widget.menuItem.imageUrl!,
              fit: BoxFit.cover,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  color: theme.colorScheme.surfaceContainerHighest,
                  child: Center(
                    child: CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                      strokeWidth: 2,
                    ),
                  ),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                logger.warning(
                    _logTag,
                    'Error loading image for ${widget.menuItem.name}',
                    error,
                    stackTrace);
                return Container(
                  color: theme.colorScheme.surfaceContainerHighest,
                  child: Center(
                    child: Icon(
                      Icons.image_not_supported,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      );
    } catch (e, stackTrace) {
      logger.error(_logTag, 'Error building menu item image', e, stackTrace);

      // Fallback UI in case of error
      return Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Icon(
            Icons.image_not_supported,
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      );
    }
  }

  /// Builds the name and price section
  Widget _buildNameAndPrice(BuildContext context) {
    final theme = Theme.of(context);
    final logger = ref.read(loggingServiceProvider);

    try {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Text(
              widget.menuItem.name,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            widget.menuItem.formattedPrice,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
        ],
      );
    } catch (e, stackTrace) {
      logger.error(
          _logTag, 'Error building name and price section', e, stackTrace);

      // Fallback UI in case of error
      return Text(
        widget.menuItem.name,
        style: theme.textTheme.titleMedium,
      );
    }
  }

  /// Builds the rating section
  Widget _buildRating(BuildContext context) {
    final theme = Theme.of(context);
    final logger = ref.read(loggingServiceProvider);

    try {
      return Row(
        children: [
          RatingStars(
            rating: widget.rating ?? 0,
            size: 16,
          ),
          const SizedBox(width: 4),
          if (widget.ratingCount != null)
            Text(
              '(${widget.ratingCount})',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
        ],
      );
    } catch (e, stackTrace) {
      logger.error(_logTag, 'Error building rating section', e, stackTrace);

      // Fallback UI in case of error
      return const SizedBox.shrink();
    }
  }

  /// Builds the dietary tags section
  Widget _buildDietaryTags(BuildContext context) {
    final logger = ref.read(loggingServiceProvider);

    try {
      return Wrap(
        spacing: 8,
        runSpacing: 8,
        children: [
          if (widget.menuItem.isVegetarian)
            _buildDietaryTag(context, 'Vegetarian', Icons.eco),
          if (widget.menuItem.isVegan)
            _buildDietaryTag(context, 'Vegan', Icons.spa),
          if (widget.menuItem.isGlutenFree)
            _buildDietaryTag(context, 'Gluten-Free', Icons.no_food),
          if (widget.menuItem.containsNuts)
            _buildDietaryTag(context, 'Contains Nuts', Icons.warning_amber),
          if (widget.menuItem.isSpicy)
            _buildDietaryTag(context, 'Spicy', Icons.whatshot),
          if (widget.menuItem.isPopular)
            _buildDietaryTag(context, 'Popular', Icons.star),
        ],
      );
    } catch (e, stackTrace) {
      logger.error(
          _logTag, 'Error building dietary tags section', e, stackTrace);

      // Fallback UI in case of error
      return const SizedBox.shrink();
    }
  }

  /// Builds a dietary tag
  Widget _buildDietaryTag(BuildContext context, String label, IconData icon) {
    final theme = Theme.of(context);
    final logger = ref.read(loggingServiceProvider);

    try {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 14,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    } catch (e, stackTrace) {
      logger.error(_logTag, 'Error building dietary tag', e, stackTrace);

      // Fallback UI in case of error
      return const SizedBox.shrink();
    }
  }

  /// Builds the add to order section
  Widget _buildAddToOrderSection(BuildContext context) {
    final theme = Theme.of(context);
    final logger = ref.read(loggingServiceProvider);

    try {
      return Padding(
        padding: const EdgeInsets.only(top: 12),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Quantity selector
            Row(
              children: [
                _buildQuantityButton(
                  context,
                  icon: Icons.remove,
                  onPressed: _quantity > 1 ? _decrementQuantity : null,
                ),
                const SizedBox(width: 8),
                Text(
                  _quantity.toString(),
                  style: theme.textTheme.titleMedium,
                ),
                const SizedBox(width: 8),
                _buildQuantityButton(
                  context,
                  icon: Icons.add,
                  onPressed: _quantity < 10 ? _incrementQuantity : null,
                ),
              ],
            ),

            // Add to order button
            AnimatedBuilder(
              animation: _scaleAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  child: child,
                );
              },
              child: ElevatedButton.icon(
                onPressed: _addToOrder,
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                icon: const Icon(Icons.add_shopping_cart),
                label: const Text('Add to Order'),
              ),
            ),
          ],
        ),
      );
    } catch (e, stackTrace) {
      logger.error(
          _logTag, 'Error building add to order section', e, stackTrace);

      // Fallback UI in case of error
      return const SizedBox.shrink();
    }
  }

  /// Builds a quantity button
  Widget _buildQuantityButton(
    BuildContext context, {
    required IconData icon,
    VoidCallback? onPressed,
  }) {
    final theme = Theme.of(context);

    return Material(
      color: theme.colorScheme.surfaceContainerHighest,
      borderRadius: BorderRadius.circular(4),
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(4),
        child: SizedBox(
          width: 32,
          height: 32,
          child: Center(
            child: Icon(
              icon,
              size: 18,
              color: onPressed != null
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onSurfaceVariant.withAlpha(128),
            ),
          ),
        ),
      ),
    );
  }

  /// Increments the quantity
  void _incrementQuantity() {
    final logger = ref.read(loggingServiceProvider);

    try {
      setState(() {
        _quantity = (_quantity + 1).clamp(1, 10);
      });
      logger.debug(_logTag,
          'Incremented quantity to $_quantity for ${widget.menuItem.name}');
    } catch (e, stackTrace) {
      logger.error(_logTag, 'Error incrementing quantity', e, stackTrace);
    }
  }

  /// Decrements the quantity
  void _decrementQuantity() {
    final logger = ref.read(loggingServiceProvider);

    try {
      setState(() {
        _quantity = (_quantity - 1).clamp(1, 10);
      });
      logger.debug(_logTag,
          'Decremented quantity to $_quantity for ${widget.menuItem.name}');
    } catch (e, stackTrace) {
      logger.error(_logTag, 'Error decrementing quantity', e, stackTrace);
    }
  }

  /// Adds the item to the order
  void _addToOrder() {
    final logger = ref.read(loggingServiceProvider);

    try {
      logger.debug(_logTag,
          'Adding ${widget.menuItem.name} to order (quantity: $_quantity)');

      // Play animation
      _animationController.forward().then((_) {
        _animationController.reverse();
      });

      // Call the callback
      widget.onAddToOrder?.call(widget.menuItem, _quantity);

      // Show snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Added ${widget.menuItem.name} to order'),
            duration: const Duration(seconds: 2),
            action: SnackBarAction(
              label: 'View Order',
              onPressed: () {
                // Navigate to order screen (would be implemented in a real app)
              },
            ),
          ),
        );
      }
    } catch (e, stackTrace) {
      logger.error(_logTag, 'Error adding item to order', e, stackTrace);

      // Show error snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding item to order: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}
