// Dart imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/providers/services_providers.dart';

/// A widget that displays a restaurant amenity as a chip with an icon and label
class RestaurantAmenityChip extends ConsumerWidget {
  /// The icon to display
  final IconData icon;

  /// The label to display
  final String label;

  /// The color of the icon (defaults to the primary color)
  final Color? iconColor;

  /// The color of the chip background (defaults to surfaceContainerHighest)
  final Color? backgroundColor;

  /// Whether to show a shadow
  final bool showShadow;

  /// Whether the amenity is available (if false, will show as disabled)
  final bool isAvailable;

  /// Callback when the chip is tapped
  final VoidCallback? onTap;

  /// Creates a new restaurant amenity chip
  const RestaurantAmenityChip({
    super.key,
    required this.icon,
    required this.label,
    this.iconColor,
    this.backgroundColor,
    this.showShadow = true,
    this.isAvailable = true,
    this.onTap,
  });

  /// Creates a restaurant amenity chip for WiFi
  factory RestaurantAmenityChip.wifi({
    Key? key,
    bool isAvailable = true,
    VoidCallback? onTap,
  }) {
    return RestaurantAmenityChip(
      key: key,
      icon: Icons.wifi,
      label: 'WiFi',
      isAvailable: isAvailable,
      onTap: onTap,
    );
  }

  /// Creates a restaurant amenity chip for parking
  factory RestaurantAmenityChip.parking({
    Key? key,
    bool isAvailable = true,
    VoidCallback? onTap,
  }) {
    return RestaurantAmenityChip(
      key: key,
      icon: Icons.local_parking,
      label: 'Parking',
      isAvailable: isAvailable,
      onTap: onTap,
    );
  }

  /// Creates a restaurant amenity chip for valet parking
  factory RestaurantAmenityChip.valetParking({
    Key? key,
    bool isAvailable = true,
    VoidCallback? onTap,
  }) {
    return RestaurantAmenityChip(
      key: key,
      icon: Icons.directions_car,
      label: 'Valet Parking',
      isAvailable: isAvailable,
      onTap: onTap,
    );
  }

  /// Creates a restaurant amenity chip for outdoor seating
  factory RestaurantAmenityChip.outdoorSeating({
    Key? key,
    bool isAvailable = true,
    VoidCallback? onTap,
  }) {
    return RestaurantAmenityChip(
      key: key,
      icon: Icons.deck,
      label: 'Outdoor Seating',
      isAvailable: isAvailable,
      onTap: onTap,
    );
  }

  /// Creates a restaurant amenity chip for bar
  factory RestaurantAmenityChip.bar({
    Key? key,
    bool isAvailable = true,
    VoidCallback? onTap,
  }) {
    return RestaurantAmenityChip(
      key: key,
      icon: Icons.local_bar,
      label: 'Bar',
      isAvailable: isAvailable,
      onTap: onTap,
    );
  }

  /// Creates a restaurant amenity chip for live music
  factory RestaurantAmenityChip.liveMusic({
    Key? key,
    bool isAvailable = true,
    VoidCallback? onTap,
  }) {
    return RestaurantAmenityChip(
      key: key,
      icon: Icons.music_note,
      label: 'Live Music',
      isAvailable: isAvailable,
      onTap: onTap,
    );
  }

  /// Creates a restaurant amenity chip for kids menu
  factory RestaurantAmenityChip.kidsMenu({
    Key? key,
    bool isAvailable = true,
    VoidCallback? onTap,
  }) {
    return RestaurantAmenityChip(
      key: key,
      icon: Icons.child_care,
      label: 'Kids Menu',
      isAvailable: isAvailable,
      onTap: onTap,
    );
  }

  /// Creates a restaurant amenity chip for vegetarian options
  factory RestaurantAmenityChip.vegetarian({
    Key? key,
    bool isAvailable = true,
    VoidCallback? onTap,
  }) {
    return RestaurantAmenityChip(
      key: key,
      icon: Icons.eco,
      label: 'Vegetarian',
      isAvailable: isAvailable,
      onTap: onTap,
    );
  }

  /// Creates a restaurant amenity chip for vegan options
  factory RestaurantAmenityChip.vegan({
    Key? key,
    bool isAvailable = true,
    VoidCallback? onTap,
  }) {
    return RestaurantAmenityChip(
      key: key,
      icon: Icons.spa,
      label: 'Vegan',
      isAvailable: isAvailable,
      onTap: onTap,
    );
  }

  /// Creates a restaurant amenity chip for gluten-free options
  factory RestaurantAmenityChip.glutenFree({
    Key? key,
    bool isAvailable = true,
    VoidCallback? onTap,
  }) {
    return RestaurantAmenityChip(
      key: key,
      icon: Icons.no_food,
      label: 'Gluten-Free',
      isAvailable: isAvailable,
      onTap: onTap,
    );
  }

  /// Creates a restaurant amenity chip for halal options
  factory RestaurantAmenityChip.halal({
    Key? key,
    bool isAvailable = true,
    VoidCallback? onTap,
  }) {
    return RestaurantAmenityChip(
      key: key,
      icon: Icons.restaurant_menu,
      label: 'Halal',
      isAvailable: isAvailable,
      onTap: onTap,
    );
  }

  /// Creates a restaurant amenity chip for kosher options
  factory RestaurantAmenityChip.kosher({
    Key? key,
    bool isAvailable = true,
    VoidCallback? onTap,
  }) {
    return RestaurantAmenityChip(
      key: key,
      icon: Icons.restaurant_menu,
      label: 'Kosher',
      isAvailable: isAvailable,
      onTap: onTap,
    );
  }

  /// Creates a restaurant amenity chip for wheelchair accessibility
  factory RestaurantAmenityChip.wheelchairAccessible({
    Key? key,
    bool isAvailable = true,
    VoidCallback? onTap,
  }) {
    return RestaurantAmenityChip(
      key: key,
      icon: Icons.accessible,
      label: 'Accessible',
      isAvailable: isAvailable,
      onTap: onTap,
    );
  }

  /// Creates a restaurant amenity chip for credit cards
  factory RestaurantAmenityChip.creditCards({
    Key? key,
    bool isAvailable = true,
    VoidCallback? onTap,
  }) {
    return RestaurantAmenityChip(
      key: key,
      icon: Icons.credit_card,
      label: 'Credit Cards',
      isAvailable: isAvailable,
      onTap: onTap,
    );
  }

  /// Creates a restaurant amenity chip for reservations
  factory RestaurantAmenityChip.reservations({
    Key? key,
    bool isAvailable = true,
    VoidCallback? onTap,
  }) {
    return RestaurantAmenityChip(
      key: key,
      icon: Icons.book_online,
      label: 'Reservations',
      isAvailable: isAvailable,
      onTap: onTap,
    );
  }

  /// Creates a restaurant amenity chip for dress code
  factory RestaurantAmenityChip.dressCode({
    Key? key,
    bool isAvailable = true,
    VoidCallback? onTap,
  }) {
    return RestaurantAmenityChip(
      key: key,
      icon: Icons.checkroom,
      label: 'Dress Code',
      isAvailable: isAvailable,
      onTap: onTap,
    );
  }

  /// Creates a restaurant amenity chip for view
  factory RestaurantAmenityChip.view({
    Key? key,
    bool isAvailable = true,
    VoidCallback? onTap,
  }) {
    return RestaurantAmenityChip(
      key: key,
      icon: Icons.landscape,
      label: 'View',
      isAvailable: isAvailable,
      onTap: onTap,
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final logger = ref.read(loggingServiceProvider);

    try {
      logger.debug(
          'RestaurantAmenityChip', 'Building chip for amenity: $label');

      // Determine colors based on availability
      final effectiveIconColor = isAvailable
          ? (iconColor ?? theme.colorScheme.primary)
          : theme.colorScheme.onSurfaceVariant
              .withAlpha(128); // 128 is equivalent to 0.5 opacity

      final effectiveBackgroundColor = isAvailable
          ? (backgroundColor ?? theme.colorScheme.surfaceContainerHighest)
          : theme.colorScheme.surfaceContainerLow;

      // Build the chip
      return Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isAvailable ? onTap : null,
          borderRadius: BorderRadius.circular(16),
          child: Semantics(
            button: onTap != null,
            enabled: isAvailable,
            label: '$label amenity',
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: effectiveBackgroundColor,
                borderRadius: BorderRadius.circular(16),
                boxShadow: showShadow
                    ? [
                        BoxShadow(
                          color: Colors.black.withAlpha(
                              13), // 13 is equivalent to 0.05 opacity
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : null,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    icon,
                    size: 18,
                    color: effectiveIconColor,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    label,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: isAvailable
                          ? theme.colorScheme.onSurface
                          : theme.colorScheme.onSurface.withAlpha(128),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    } catch (e, stackTrace) {
      logger.error('RestaurantAmenityChip', 'Error building amenity chip', e,
          stackTrace);

      // Fallback UI in case of error
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: theme.colorScheme.errorContainer,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: 18,
              color: theme.colorScheme.error,
            ),
            const SizedBox(width: 8),
            Text(
              'Error',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.error,
              ),
            ),
          ],
        ),
      );
    }
  }
}
