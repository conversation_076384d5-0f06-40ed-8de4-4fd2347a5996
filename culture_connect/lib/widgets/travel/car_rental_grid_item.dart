import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/widgets/common/rating_display.dart';

/// A widget for displaying a car rental in a grid
class CarRentalGridItem extends StatelessWidget {
  /// The car rental to display
  final CarRental carRental;

  /// Callback when the item is tapped
  final VoidCallback onTap;

  /// Creates a new car rental grid item
  const CarRentalGridItem({
    super.key,
    required this.carRental,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Car image
            Stack(
              children: [
                AspectRatio(
                  aspectRatio: 16 / 9,
                  child: <PERSON>(
                    tag: 'car_rental_image_${carRental.id}',
                    child: Image.network(
                      carRental.imageUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: theme.colorScheme.surfaceContainerHighest,
                          child: Center(
                            child: Icon(
                              Icons.image_not_supported,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                if (carRental.isOnSale)
                  Positioned(
                    top: 8,
                    left: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.error,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '${carRental.discountPercentage?.round() ?? 0}% OFF',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onError,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            ),

            // Car details
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Car name
                  Text(
                    carRental.name,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),

                  // Car full name
                  Text(
                    carRental.fullName,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),

                  // Car features
                  Row(
                    children: [
                      _buildFeatureIcon(
                        Icons.settings,
                        carRental.transmission.displayName,
                      ),
                      const SizedBox(width: 8),
                      _buildFeatureIcon(
                        Icons.local_gas_station,
                        carRental.fuelType.displayName,
                      ),
                      const SizedBox(width: 8),
                      _buildFeatureIcon(
                        Icons.airline_seat_recline_normal,
                        '${carRental.seats} seats',
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Rating and price
                  Row(
                    children: [
                      RatingDisplay(
                        rating: carRental.rating,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '(${carRental.reviewCount})',
                        style: theme.textTheme.bodySmall,
                      ),
                      const Spacer(),
                      if (carRental.isOnSale && carRental.originalPrice != null)
                        Text(
                          carRental.formattedOriginalPrice!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            decoration: TextDecoration.lineThrough,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      const SizedBox(width: 4),
                      Text(
                        carRental.formattedPrice,
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),

                  // Per day label
                  Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      'per day',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureIcon(IconData icon, String label) {
    return Expanded(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: Colors.grey,
          ),
          const SizedBox(width: 4),
          Flexible(
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
