import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/travel/travel_services_provider.dart';

/// A dialog for comparing car rentals
class CarComparisonDialog extends ConsumerStatefulWidget {
  /// The car rental to compare
  final CarRental carRental;

  /// Callback when a car is selected
  final Function(CarRental) onCarSelected;

  /// Creates a new car comparison dialog
  const CarComparisonDialog({
    super.key,
    required this.carRental,
    required this.onCarSelected,
  });

  @override
  ConsumerState<CarComparisonDialog> createState() =>
      _CarComparisonDialogState();
}

class _CarComparisonDialogState extends ConsumerState<CarComparisonDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  CarRental? _selectedCar;
  List<CarRental> _similarCars = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadSimilarCars();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSimilarCars() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get all car rentals
      final carRentalsAsyncValue = await ref.read(carRentalsProvider.future);

      // Filter similar cars (same car type, similar price range)
      final similarCars = carRentalsAsyncValue.where((car) {
        return car.id != widget.carRental.id &&
            car.carType == widget.carRental.carType &&
            (car.price >= widget.carRental.price * 0.8 &&
                car.price <= widget.carRental.price * 1.2);
      }).toList();

      // Sort by rating
      similarCars.sort((a, b) => b.rating.compareTo(a.rating));

      // Take top 5
      final topSimilarCars = similarCars.take(5).toList();

      setState(() {
        _similarCars = topSimilarCars;
        _selectedCar = topSimilarCars.isNotEmpty ? topSimilarCars.first : null;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      debugPrint('Error loading similar cars: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: double.infinity,
        constraints: const BoxConstraints(
          maxWidth: 800,
          maxHeight: 600,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'Compare Cars',
                      style: theme.textTheme.titleLarge,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              ),
            ),

            // Tab bar
            TabBar(
              controller: _tabController,
              tabs: const [
                Tab(text: 'Side by Side'),
                Tab(text: 'Feature Comparison'),
              ],
              labelColor: theme.colorScheme.primary,
              unselectedLabelColor: theme.colorScheme.onSurface,
              indicatorColor: theme.colorScheme.primary,
            ),

            // Tab content
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _similarCars.isEmpty
                      ? Center(
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Text(
                              'No similar cars found to compare',
                              style: theme.textTheme.titleMedium,
                            ),
                          ),
                        )
                      : TabBarView(
                          controller: _tabController,
                          children: [
                            _buildSideBySideComparison(),
                            _buildFeatureComparison(),
                          ],
                        ),
            ),

            // Footer
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  OutlinedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: const Text('Cancel'),
                  ),
                  if (_selectedCar != null)
                    ElevatedButton(
                      onPressed: () {
                        widget.onCarSelected(_selectedCar!);
                        Navigator.of(context).pop();
                      },
                      child: const Text('Select Car'),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSideBySideComparison() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Car selection dropdown
          DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: 'Compare with',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            value: _selectedCar?.id,
            items: _similarCars.map((car) {
              return DropdownMenuItem<String>(
                value: car.id,
                child: Text(
                    '${car.year} ${car.make} ${car.model} - ${car.formattedPrice}/day'),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedCar =
                      _similarCars.firstWhere((car) => car.id == value);
                });
              }
            },
          ),
          const SizedBox(height: 16),

          // Car comparison
          Expanded(
            child: _selectedCar == null
                ? Center(
                    child: Text(
                      'Select a car to compare',
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  )
                : Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child:
                            _buildCarCard(widget.carRental, isSelected: true),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildCarCard(_selectedCar!, isSelected: false),
                      ),
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureComparison() {
    final theme = Theme.of(context);

    // Define the features to compare
    final features = [
      'Make & Model',
      'Year',
      'Type',
      'Price per Day',
      'Rating',
      'Transmission',
      'Fuel Type',
      'Seats',
      'Doors',
      'Luggage Capacity',
      'Mileage Limit',
      'Air Conditioning',
      'GPS',
      'Bluetooth',
      'USB',
      'Sunroof',
    ];

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Car selection dropdown
          DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: 'Compare with',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            value: _selectedCar?.id,
            items: _similarCars.map((car) {
              return DropdownMenuItem<String>(
                value: car.id,
                child: Text(
                    '${car.year} ${car.make} ${car.model} - ${car.formattedPrice}/day'),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedCar =
                      _similarCars.firstWhere((car) => car.id == value);
                });
              }
            },
          ),
          const SizedBox(height: 16),

          // Feature comparison table
          Expanded(
            child: _selectedCar == null
                ? Center(
                    child: Text(
                      'Select a car to compare',
                      style: theme.textTheme.bodyLarge,
                    ),
                  )
                : SingleChildScrollView(
                    child: Table(
                      border: TableBorder.all(
                        color: theme.colorScheme.outlineVariant,
                        width: 1,
                      ),
                      columnWidths: const {
                        0: FlexColumnWidth(2),
                        1: FlexColumnWidth(3),
                        2: FlexColumnWidth(3),
                      },
                      children: [
                        // Header row
                        TableRow(
                          decoration: BoxDecoration(
                            color: theme.colorScheme.surfaceContainerHighest,
                          ),
                          children: [
                            _buildTableCell('Feature', isHeader: true),
                            _buildTableCell(widget.carRental.name,
                                isHeader: true),
                            _buildTableCell(_selectedCar!.name, isHeader: true),
                          ],
                        ),

                        // Feature rows
                        for (var feature in features)
                          TableRow(
                            children: [
                              _buildTableCell(feature),
                              _buildTableCell(_getCarFeatureValue(
                                  widget.carRental, feature)),
                              _buildTableCell(
                                  _getCarFeatureValue(_selectedCar!, feature)),
                            ],
                          ),
                      ],
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildCarCard(CarRental car, {required bool isSelected}) {
    final theme = Theme.of(context);

    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: isSelected ? theme.colorScheme.primary : Colors.transparent,
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Car image
          ClipRRect(
            borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
            child: Image.network(
              car.imageUrl,
              height: 150,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: 150,
                  color: theme.colorScheme.surfaceContainerHighest,
                  child: Center(
                    child: Icon(
                      Icons.image_not_supported,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                );
              },
            ),
          ),

          // Car details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  car.name,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  car.fullName,
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(
                      Icons.star,
                      size: 16,
                      color: Colors.amber,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${car.rating.toStringAsFixed(1)} (${car.reviewCount})',
                      style: theme.textTheme.bodyMedium,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '${car.formattedPrice} / day',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                _buildFeatureRow(Icons.settings, 'Transmission',
                    car.transmission.displayName),
                _buildFeatureRow(
                    Icons.local_gas_station, 'Fuel', car.fuelType.displayName),
                _buildFeatureRow(Icons.airline_seat_recline_normal, 'Seats',
                    car.seats.toString()),
                _buildFeatureRow(
                    Icons.door_front_door, 'Doors', car.doors.toString()),
                _buildFeatureRow(
                    Icons.luggage, 'Luggage', '${car.luggageCapacity} bags'),
                const SizedBox(height: 16),
                if (!isSelected)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        widget.onCarSelected(car);
                        Navigator.of(context).pop();
                      },
                      child: const Text('Select'),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureRow(IconData icon, String label, String value) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableCell(String text, {bool isHeader = false}) {
    final theme = Theme.of(context);

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Text(
          text,
          style: isHeader
              ? theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                )
              : theme.textTheme.bodyMedium,
          textAlign: isHeader ? TextAlign.center : TextAlign.left,
        ),
      ),
    );
  }

  String _getCarFeatureValue(CarRental car, String feature) {
    switch (feature) {
      case 'Make & Model':
        return '${car.make} ${car.model}';
      case 'Year':
        return car.year.toString();
      case 'Type':
        return car.carType.displayName;
      case 'Price per Day':
        return car.formattedPrice;
      case 'Rating':
        return '${car.rating.toStringAsFixed(1)} (${car.reviewCount})';
      case 'Transmission':
        return car.transmission.displayName;
      case 'Fuel Type':
        return car.fuelType.displayName;
      case 'Seats':
        return car.seats.toString();
      case 'Doors':
        return car.doors.toString();
      case 'Luggage Capacity':
        return '${car.luggageCapacity} bags';
      case 'Mileage Limit':
        return car.formattedMileageLimit;
      case 'Air Conditioning':
        return car.hasAirConditioning ? 'Yes' : 'No';
      case 'GPS':
        return car.hasGPS ? 'Yes' : 'No';
      case 'Bluetooth':
        return car.hasBluetooth ? 'Yes' : 'No';
      case 'USB':
        return car.hasUSB ? 'Yes' : 'No';
      case 'Sunroof':
        return car.hasSunroof ? 'Yes' : 'No';
      default:
        return '-';
    }
  }
}
