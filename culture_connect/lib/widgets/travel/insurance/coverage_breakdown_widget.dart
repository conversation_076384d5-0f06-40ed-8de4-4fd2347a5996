// Flutter imports
import 'package:flutter/material.dart';

// Project imports
import 'package:culture_connect/models/travel/insurance/insurance.dart'
    hide InsuranceCoverageType, InsuranceClaimStatus;

/// A widget for displaying insurance coverage breakdown with visual indicators
class CoverageBreakdownWidget extends StatelessWidget {
  /// The list of insurance coverages to display
  final List<InsuranceCoverage> coverages;

  /// Whether to show coverage amounts
  final bool showAmounts;

  /// Whether to show coverage descriptions
  final bool showDescriptions;

  /// Whether to show deductible information
  final bool showDeductibles;

  /// Whether to use compact layout
  final bool isCompact;

  /// Whether to show only included coverages
  final bool showOnlyIncluded;

  /// Whether to allow interactive expansion
  final bool isExpandable;

  /// Callback when a coverage item is tapped
  final Function(InsuranceCoverage)? onCoverageTap;

  /// Creates a new coverage breakdown widget
  const CoverageBreakdownWidget({
    super.key,
    required this.coverages,
    this.showAmounts = true,
    this.showDescriptions = false,
    this.showDeductibles = false,
    this.isCompact = false,
    this.showOnlyIncluded = false,
    this.isExpandable = false,
    this.onCoverageTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final filteredCoverages = showOnlyIncluded
        ? coverages.where((c) => c.isIncluded).toList()
        : coverages;

    if (filteredCoverages.isEmpty) {
      return _buildEmptyState(theme);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        _buildHeader(theme),

        const SizedBox(height: 12),

        // Coverage items
        ...filteredCoverages
            .map((coverage) => _buildCoverageItem(theme, coverage)),

        // Summary section
        if (!isCompact && !showOnlyIncluded) ...[
          const SizedBox(height: 16),
          _buildSummary(theme),
        ],
      ],
    );
  }

  Widget _buildHeader(ThemeData theme) {
    final includedCount = coverages.where((c) => c.isIncluded).length;
    final totalCount = coverages.length;

    return Row(
      children: [
        Icon(
          Icons.security,
          size: 20,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(width: 8),
        Text(
          'Coverage Details',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        if (!showOnlyIncluded)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '$includedCount/$totalCount',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onPrimaryContainer,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildCoverageItem(ThemeData theme, InsuranceCoverage coverage) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onCoverageTap != null ? () => onCoverageTap!(coverage) : null,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: coverage.isIncluded
                ? theme.colorScheme.surfaceContainerLow
                : theme.colorScheme.surfaceContainerLowest,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: coverage.isIncluded
                  ? theme.colorScheme.primary.withAlpha(77) // 0.3 opacity
                  : theme.colorScheme.outlineVariant,
              width: coverage.isIncluded ? 1.5 : 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Main coverage info
              Row(
                children: [
                  // Coverage icon and status
                  _buildCoverageIcon(theme, coverage),

                  const SizedBox(width: 12),

                  // Coverage name and amount
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          coverage.type.displayName,
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: coverage.isIncluded
                                ? theme.colorScheme.onSurface
                                : theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                        if (showAmounts && coverage.isIncluded) ...[
                          const SizedBox(height: 2),
                          Text(
                            coverage.formattedAmount,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  // Status indicator
                  _buildStatusIndicator(theme, coverage),
                ],
              ),

              // Additional details
              if (!isCompact && coverage.isIncluded) ...[
                const SizedBox(height: 8),
                _buildAdditionalDetails(theme, coverage),
              ],

              // Description
              if (showDescriptions && coverage.type.description.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  coverage.type.description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCoverageIcon(ThemeData theme, InsuranceCoverage coverage) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: coverage.isIncluded
            ? theme.colorScheme.primaryContainer
            : theme.colorScheme.surfaceContainerHigh,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Icon(
        coverage.type.icon,
        size: 16,
        color: coverage.isIncluded
            ? theme.colorScheme.onPrimaryContainer
            : theme.colorScheme.onSurfaceVariant,
      ),
    );
  }

  Widget _buildStatusIndicator(ThemeData theme, InsuranceCoverage coverage) {
    return Icon(
      coverage.isIncluded ? Icons.check_circle : Icons.cancel,
      size: 20,
      color: coverage.isIncluded
          ? Colors.green
          : theme.colorScheme.onSurfaceVariant,
    );
  }

  Widget _buildAdditionalDetails(ThemeData theme, InsuranceCoverage coverage) {
    final details = <Widget>[];

    // Deductible information
    if (showDeductibles &&
        coverage.deductible != null &&
        coverage.deductible! > 0) {
      details.add(
        _buildDetailChip(
          theme,
          'Deductible: ${coverage.formattedDeductible}',
          Icons.remove_circle_outline,
        ),
      );
    }

    // Maximum benefit
    if (coverage.maximumBenefit != null &&
        coverage.maximumBenefit! > 0 &&
        coverage.maximumBenefit != coverage.amount) {
      details.add(
        _buildDetailChip(
          theme,
          'Max: ${coverage.formattedMaximumBenefit}',
          Icons.trending_up,
        ),
      );
    }

    if (details.isEmpty) return const SizedBox.shrink();

    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: details,
    );
  }

  Widget _buildDetailChip(ThemeData theme, String text, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: theme.colorScheme.secondaryContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: theme.colorScheme.onSecondaryContainer,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSecondaryContainer,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummary(ThemeData theme) {
    final includedCoverages = coverages.where((c) => c.isIncluded).toList();
    final totalCoverage = includedCoverages.fold<double>(
      0,
      (sum, coverage) => sum + coverage.amount,
    );

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withAlpha(77), // 0.3 opacity
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.primary.withAlpha(128), // 0.5 opacity
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.summarize,
            color: theme.colorScheme.onPrimaryContainer,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Total Coverage',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onPrimaryContainer,
                  ),
                ),
                Text(
                  '\$${totalCoverage.toStringAsFixed(0)}',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${includedCoverages.length} items',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onPrimaryContainer,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerLowest,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outlineVariant,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.security_outlined,
            size: 48,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 12),
          Text(
            'No Coverage Information',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Coverage details are not available for this policy.',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
