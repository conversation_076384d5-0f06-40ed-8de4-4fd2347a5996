// Flutter imports
import 'package:flutter/material.dart';

// Project imports
import 'package:culture_connect/models/travel/insurance/insurance.dart'
    hide InsuranceCoverageType, InsuranceClaimStatus;

/// A comprehensive insurance card widget for displaying insurance providers and policies
class InsuranceCard extends StatelessWidget {
  /// The insurance provider to display (optional)
  final InsuranceProvider? provider;

  /// The insurance policy to display (optional)
  final InsurancePolicy? policy;

  /// Whether to show provider details
  final bool showProviderDetails;

  /// Whether to show policy details
  final bool showPolicyDetails;

  /// Whether to show rating information
  final bool showRating;

  /// Whether to show price information
  final bool showPrice;

  /// Whether to show coverage summary
  final bool showCoverage;

  /// Whether to show action buttons
  final bool showActions;

  /// Whether to use horizontal layout
  final bool isHorizontal;

  /// Whether to use compact layout
  final bool isCompact;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Callback when the view details button is tapped
  final VoidCallback? onViewDetails;

  /// Callback when the purchase/select button is tapped
  final VoidCallback? onPurchase;

  /// Callback when the contact button is tapped
  final VoidCallback? onContact;

  /// Creates a new insurance card
  const InsuranceCard({
    super.key,
    this.provider,
    this.policy,
    this.showProviderDetails = true,
    this.showPolicyDetails = true,
    this.showRating = true,
    this.showPrice = true,
    this.showCoverage = false,
    this.showActions = true,
    this.isHorizontal = false,
    this.isCompact = false,
    this.onTap,
    this.onViewDetails,
    this.onPurchase,
    this.onContact,
  }) : assert(provider != null || policy != null,
            'Either provider or policy must be provided');

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (isHorizontal) {
      return _buildHorizontalCard(theme);
    }

    return _buildVerticalCard(theme);
  }

  Widget _buildVerticalCard(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(isCompact ? 12 : 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header section
              _buildHeader(theme),

              if (!isCompact) ...[
                const SizedBox(height: 12),

                // Content section
                if (showProviderDetails && provider != null)
                  _buildProviderDetails(theme),

                if (showPolicyDetails && policy != null)
                  _buildPolicyDetails(theme),

                if (showCoverage && (provider != null || policy != null))
                  _buildCoverageSection(theme),

                const SizedBox(height: 12),

                // Actions section
                if (showActions) _buildActions(theme),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHorizontalCard(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Left section - Icon/Logo
              _buildIcon(theme),

              const SizedBox(width: 16),

              // Middle section - Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeader(theme),
                    if (showProviderDetails && provider != null)
                      _buildProviderDetails(theme),
                    if (showPolicyDetails && policy != null)
                      _buildPolicyDetails(theme),
                  ],
                ),
              ),

              // Right section - Price/Actions
              if (showPrice || showActions)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (showPrice) _buildPriceSection(theme),
                    if (showActions) _buildQuickActions(theme),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    final displayName = provider?.name ?? policy?.name ?? 'Insurance';
    final subtitle = provider?.description ?? policy?.description;

    return Row(
      children: [
        if (!isHorizontal) _buildIcon(theme),
        if (!isHorizontal) const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                displayName,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              if (subtitle != null && !isCompact) ...[
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              if (showRating && provider != null) ...[
                const SizedBox(height: 4),
                _buildRatingSection(theme),
              ],
            ],
          ),
        ),
        if (!isHorizontal && showPrice) _buildPriceSection(theme),
      ],
    );
  }

  Widget _buildIcon(ThemeData theme) {
    IconData iconData;
    Color iconColor;

    if (provider != null) {
      iconData = Icons.business;
      iconColor = theme.colorScheme.primary;
    } else if (policy != null) {
      iconData = policy!.type.icon;
      iconColor = theme.colorScheme.secondary;
    } else {
      iconData = Icons.security;
      iconColor = theme.colorScheme.tertiary;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: iconColor.withAlpha(26), // 0.1 opacity
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        iconData,
        size: isCompact ? 20 : 24,
        color: iconColor,
      ),
    );
  }

  Widget _buildRatingSection(ThemeData theme) {
    if (provider == null) return const SizedBox.shrink();

    return Row(
      children: [
        const Icon(
          Icons.star,
          size: 16,
          color: Colors.amber,
        ),
        const SizedBox(width: 4),
        Text(
          provider!.rating.toStringAsFixed(1),
          style: theme.textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          '(${provider!.reviewCount})',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildPriceSection(ThemeData theme) {
    final price = policy?.price ?? 0.0;
    final currency = policy?.currency ?? 'USD';

    if (!showPrice || price <= 0) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          '\$${price.toStringAsFixed(2)}',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
        Text(
          currency,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildProviderDetails(ThemeData theme) {
    if (provider == null || !showProviderDetails) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Provider: ${provider!.name}',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildPolicyDetails(ThemeData theme) {
    if (policy == null || !showPolicyDetails) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Type: ${policy!.type.displayName}',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildCoverageSection(ThemeData theme) {
    // This would show a brief coverage summary
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        'Coverage details available',
        style: theme.textTheme.bodySmall?.copyWith(
          color: theme.colorScheme.onSurfaceVariant,
        ),
      ),
    );
  }

  Widget _buildActions(ThemeData theme) {
    return Row(
      children: [
        if (onViewDetails != null)
          Expanded(
            child: OutlinedButton(
              onPressed: onViewDetails,
              child: const Text('Details'),
            ),
          ),
        if (onViewDetails != null && onPurchase != null)
          const SizedBox(width: 8),
        if (onPurchase != null)
          Expanded(
            child: ElevatedButton(
              onPressed: onPurchase,
              child: Text(policy != null ? 'Select' : 'View Policies'),
            ),
          ),
      ],
    );
  }

  Widget _buildQuickActions(ThemeData theme) {
    return Column(
      children: [
        if (onViewDetails != null)
          IconButton(
            onPressed: onViewDetails,
            icon: const Icon(Icons.info_outline),
            iconSize: 20,
          ),
        if (onContact != null)
          IconButton(
            onPressed: onContact,
            icon: const Icon(Icons.phone),
            iconSize: 20,
          ),
      ],
    );
  }
}
