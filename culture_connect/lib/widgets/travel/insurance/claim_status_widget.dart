// Flutter imports
import 'package:flutter/material.dart';

// Project imports
import 'package:culture_connect/models/travel/insurance/insurance_claim_status.dart';

/// A widget for displaying insurance claim status with visual indicators and timeline
class ClaimStatusWidget extends StatelessWidget {
  /// The current claim status
  final InsuranceClaimStatus status;

  /// Whether to show status description
  final bool showDescription;

  /// Whether to show status timeline
  final bool showTimeline;

  /// Whether to use compact layout
  final bool isCompact;

  /// Whether to show animated indicators
  final bool showAnimation;

  /// Additional status message
  final String? statusMessage;

  /// Progress percentage (0.0 to 1.0)
  final double? progress;

  /// Callback when status is tapped
  final VoidCallback? onTap;

  /// Creates a new claim status widget
  const ClaimStatusWidget({
    super.key,
    required this.status,
    this.showDescription = true,
    this.showTimeline = false,
    this.isCompact = false,
    this.showAnimation = true,
    this.statusMessage,
    this.progress,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (showTimeline) {
      return _buildTimelineView(theme);
    }

    return _buildStatusCard(theme);
  }

  Widget _buildStatusCard(ThemeData theme) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: EdgeInsets.all(isCompact ? 12 : 16),
        decoration: BoxDecoration(
          color: _getStatusBackgroundColor(theme),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: status.color.withAlpha(128), // 0.5 opacity
            width: 1.5,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Main status row
            Row(
              children: [
                _buildStatusIcon(theme),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        status.displayName,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: status.color,
                        ),
                      ),
                      if (showDescription && !isCompact) ...[
                        const SizedBox(height: 4),
                        Text(
                          status.description,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                _buildStatusBadge(theme),
              ],
            ),

            // Additional message
            if (statusMessage != null && statusMessage!.isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildStatusMessage(theme),
            ],

            // Progress indicator
            if (progress != null && !isCompact) ...[
              const SizedBox(height: 12),
              _buildProgressIndicator(theme),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineView(ThemeData theme) {
    const allStatuses = InsuranceClaimStatus.values;
    final currentIndex = allStatuses.indexOf(status);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Claim Progress',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...allStatuses.asMap().entries.map((entry) {
          final index = entry.key;
          final timelineStatus = entry.value;
          final isActive = index <= currentIndex;
          final isCurrent = index == currentIndex;

          return _buildTimelineItem(theme, timelineStatus, isActive, isCurrent,
              index == allStatuses.length - 1);
        }),
      ],
    );
  }

  Widget _buildTimelineItem(
      ThemeData theme,
      InsuranceClaimStatus timelineStatus,
      bool isActive,
      bool isCurrent,
      bool isLast) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: isActive
                    ? timelineStatus.color
                    : theme.colorScheme.surfaceContainerHigh,
                shape: BoxShape.circle,
                border: Border.all(
                  color: isCurrent
                      ? timelineStatus.color
                      : theme.colorScheme.outline,
                  width: isCurrent ? 3 : 1,
                ),
              ),
              child: Icon(
                timelineStatus.icon,
                size: 12,
                color: isActive
                    ? Colors.white
                    : theme.colorScheme.onSurfaceVariant,
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 40,
                color: isActive
                    ? timelineStatus.color.withAlpha(128)
                    : theme.colorScheme.outlineVariant,
              ),
          ],
        ),

        const SizedBox(width: 16),

        // Timeline content
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(bottom: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  timelineStatus.displayName,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: isCurrent ? FontWeight.bold : FontWeight.w500,
                    color: isActive
                        ? theme.colorScheme.onSurface
                        : theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                if (isCurrent && statusMessage != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    statusMessage!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatusIcon(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: status.color.withAlpha(26), // 0.1 opacity
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        status.icon,
        size: isCompact ? 16 : 20,
        color: status.color,
      ),
    );
  }

  Widget _buildStatusBadge(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: status.color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        _getStatusBadgeText(),
        style: theme.textTheme.bodySmall?.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildStatusMessage(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.outlineVariant,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            size: 16,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              statusMessage!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progress',
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              '${(progress! * 100).toInt()}%',
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: status.color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: theme.colorScheme.surfaceContainerHigh,
          valueColor: AlwaysStoppedAnimation<Color>(status.color),
          borderRadius: BorderRadius.circular(4),
        ),
      ],
    );
  }

  Color _getStatusBackgroundColor(ThemeData theme) {
    return status.color.withAlpha(26); // 0.1 opacity
  }

  String _getStatusBadgeText() {
    switch (status) {
      case InsuranceClaimStatus.submitted:
        return 'NEW';
      case InsuranceClaimStatus.inReview:
        return 'REVIEW';
      case InsuranceClaimStatus.infoRequested:
        return 'ACTION';
      case InsuranceClaimStatus.approved:
        return 'APPROVED';
      case InsuranceClaimStatus.denied:
        return 'DENIED';
      case InsuranceClaimStatus.paid:
        return 'PAID';
      case InsuranceClaimStatus.appealed:
        return 'APPEAL';
      case InsuranceClaimStatus.closed:
        return 'CLOSED';
      default:
        return 'STATUS';
    }
  }
}

/// A compact version of the claim status widget for use in lists
class CompactClaimStatusWidget extends StatelessWidget {
  /// The current claim status
  final InsuranceClaimStatus status;

  /// Creates a new compact claim status widget
  const CompactClaimStatusWidget({
    super.key,
    required this.status,
  });

  @override
  Widget build(BuildContext context) {
    return ClaimStatusWidget(
      status: status,
      isCompact: true,
      showDescription: false,
      showAnimation: false,
    );
  }
}

/// A timeline version of the claim status widget
class ClaimStatusTimelineWidget extends StatelessWidget {
  /// The current claim status
  final InsuranceClaimStatus status;

  /// Additional status message
  final String? statusMessage;

  /// Creates a new claim status timeline widget
  const ClaimStatusTimelineWidget({
    super.key,
    required this.status,
    this.statusMessage,
  });

  @override
  Widget build(BuildContext context) {
    return ClaimStatusWidget(
      status: status,
      showTimeline: true,
      statusMessage: statusMessage,
    );
  }
}
