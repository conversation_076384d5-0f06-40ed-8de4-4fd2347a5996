import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/car_rental_filter.dart';
import 'package:culture_connect/providers/travel/car_rental_filter_provider.dart';

/// A widget for selecting car rental sort options
class CarRentalSortSelector extends ConsumerWidget {
  /// Creates a new car rental sort selector
  const CarRentalSortSelector({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final filter = ref.watch(carRentalFilterProvider);
    final currentSortOption = filter.sortOption;

    return PopupMenuButton<CarRentalSortOption>(
      initialValue: currentSortOption,
      tooltip: 'Sort',
      icon: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            currentSortOption.icon,
            size: 20,
          ),
          const SizedBox(width: 4),
          Text(
            'Sort',
            style: theme.textTheme.bodyMedium,
          ),
        ],
      ),
      onSelected: (CarRentalSortOption sortOption) {
        ref.read(carRentalFilterProvider.notifier).state = filter.copyWith(
          sortOption: sortOption,
        );
      },
      itemBuilder: (BuildContext context) {
        return CarRentalSortOption.values.map((sortOption) {
          return PopupMenuItem<CarRentalSortOption>(
            value: sortOption,
            child: Row(
              children: [
                Icon(
                  sortOption.icon,
                  color: currentSortOption == sortOption
                      ? theme.colorScheme.primary
                      : null,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  sortOption.displayName,
                  style: TextStyle(
                    color: currentSortOption == sortOption
                        ? theme.colorScheme.primary
                        : null,
                    fontWeight: currentSortOption == sortOption
                        ? FontWeight.bold
                        : null,
                  ),
                ),
                if (currentSortOption == sortOption) ...[
                  const Spacer(),
                  Icon(
                    Icons.check,
                    color: theme.colorScheme.primary,
                    size: 20,
                  ),
                ],
              ],
            ),
          );
        }).toList();
      },
    );
  }
}
