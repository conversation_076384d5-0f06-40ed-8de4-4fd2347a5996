import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/providers/price_comparison_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/travel/price_comparison_card.dart';

/// A widget that displays a list of price comparisons
class PriceComparisonList extends ConsumerStatefulWidget {
  /// The travel service type
  final String serviceType;

  /// The travel service ID
  final String serviceId;

  /// The travel service name
  final String serviceName;

  /// Whether to show the price breakdown
  final bool showPriceBreakdown;

  /// Whether to show the booking button
  final bool showBookingButton;

  /// Whether to show the refresh button
  final bool showRefreshButton;

  /// Creates a new price comparison list
  const PriceComparisonList({
    super.key,
    required this.serviceType,
    required this.serviceId,
    required this.serviceName,
    this.showPriceBreakdown = false,
    this.showBookingButton = true,
    this.showRefreshButton = true,
  });

  @override
  ConsumerState<PriceComparisonList> createState() =>
      _PriceComparisonListState();
}

class _PriceComparisonListState extends ConsumerState<PriceComparisonList> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final serviceEntry = MapEntry(widget.serviceType, widget.serviceId);
    final priceComparisonAsync =
        ref.watch(priceComparisonProvider(serviceEntry));
    final bestPriceAsync = ref.watch(bestPriceProvider(serviceEntry));
    final priceRangeAsync = ref.watch(priceRangeProvider(serviceEntry));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Price Comparison',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.serviceName,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondaryColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              if (widget.showRefreshButton)
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () {
                    ref.invalidate(priceComparisonProvider(serviceEntry));
                  },
                  tooltip: 'Refresh prices',
                ),
            ],
          ),
        ),

        // Price range
        priceRangeAsync.when(
          data: (priceRange) {
            if (priceRange == null) return const SizedBox.shrink();

            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  const Text(
                    'Price range: ',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  Text(
                    '${widget.serviceType == 'flight' ? 'USD' : '\$'}${priceRange.key.toStringAsFixed(2)} - ${widget.serviceType == 'flight' ? 'USD' : '\$'}${priceRange.value.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),
            );
          },
          loading: () => const SizedBox.shrink(),
          error: (_, __) => const SizedBox.shrink(),
        ),

        const SizedBox(height: 8),

        // Price comparison list
        priceComparisonAsync.when(
          data: (pricePoints) {
            if (pricePoints.isEmpty) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Icon(
                        Icons.search_off,
                        size: 48,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'No price comparisons available',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (widget.showRefreshButton)
                        ElevatedButton.icon(
                          onPressed: () {
                            ref.invalidate(
                                priceComparisonProvider(serviceEntry));
                          },
                          icon: const Icon(Icons.refresh),
                          label: const Text('Refresh'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            foregroundColor: Colors.white,
                          ),
                        ),
                    ],
                  ),
                ),
              );
            }

            // Determine how many items to show
            final displayCount = _isExpanded ? pricePoints.length : 3;
            final displayedPricePoints =
                pricePoints.take(displayCount).toList();

            return Column(
              children: [
                // Price cards
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    children: [
                      ...displayedPricePoints.map((pricePoint) {
                        return bestPriceAsync.when(
                          data: (bestPrice) {
                            final isBestPrice = bestPrice != null &&
                                bestPrice.id == pricePoint.id;

                            return PriceComparisonCard(
                              pricePoint: pricePoint,
                              isBestPrice: isBestPrice,
                              showBookingButton: widget.showBookingButton,
                              showPriceBreakdown: widget.showPriceBreakdown,
                              onTap: () {
                                setState(() {
                                  _isExpanded = true;
                                });
                              },
                            );
                          },
                          loading: () => PriceComparisonCard(
                            pricePoint: pricePoint,
                            showBookingButton: widget.showBookingButton,
                            showPriceBreakdown: widget.showPriceBreakdown,
                          ),
                          error: (_, __) => PriceComparisonCard(
                            pricePoint: pricePoint,
                            showBookingButton: widget.showBookingButton,
                            showPriceBreakdown: widget.showPriceBreakdown,
                          ),
                        );
                      }),
                    ],
                  ),
                ),

                // Show more/less button
                if (pricePoints.length > 3)
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _isExpanded = !_isExpanded;
                      });
                    },
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _isExpanded
                              ? 'Show less'
                              : 'Show all ${pricePoints.length} options',
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                        Icon(
                          _isExpanded
                              ? Icons.keyboard_arrow_up
                              : Icons.keyboard_arrow_down,
                          size: 16,
                          color: AppTheme.primaryColor,
                        ),
                      ],
                    ),
                  ),
              ],
            );
          },
          loading: () => const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Column(
                children: [
                  CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Comparing prices...',
                    style: TextStyle(
                      fontSize: 16,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
          error: (error, stackTrace) => Center(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 48,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Error loading price comparisons',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    error.toString(),
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  if (widget.showRefreshButton)
                    ElevatedButton.icon(
                      onPressed: () {
                        ref.invalidate(priceComparisonProvider(serviceEntry));
                      },
                      icon: const Icon(Icons.refresh),
                      label: const Text('Try Again'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
