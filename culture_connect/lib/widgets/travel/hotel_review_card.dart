import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:culture_connect/models/travel/hotel_review.dart';
import 'package:culture_connect/widgets/common/rating_display.dart';
import 'package:culture_connect/widgets/photo_gallery.dart';

/// A card displaying a hotel review
class HotelReviewCard extends ConsumerStatefulWidget {
  /// The review to display
  final HotelReview review;

  /// Whether to show the full content
  final bool showFullContent;

  /// Whether to show the helpful button
  final bool showHelpfulButton;

  /// Whether to show the photos
  final bool showPhotos;

  /// Callback when the review is marked as helpful
  final Function(HotelReview)? onHelpfulToggled;

  /// Creates a new hotel review card
  const HotelReviewCard({
    super.key,
    required this.review,
    this.showFullContent = false,
    this.showHelpfulButton = true,
    this.showPhotos = true,
    this.onHelpfulToggled,
  });

  @override
  ConsumerState<HotelReviewCard> createState() => _HotelReviewCardState();
}

class _HotelReviewCardState extends ConsumerState<HotelReviewCard>
    with SingleTickerProviderStateMixin {
  bool _isExpanded = false;
  bool _isHelpful = false;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.showFullContent;
    // Check if the current user has marked this review as helpful
    // This would normally use the current user's ID from an auth service
    _isHelpful = widget.review.helpfulUserIds.contains('current-user-id');

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    if (_isExpanded) {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  void _toggleHelpful() {
    setState(() {
      _isHelpful = !_isHelpful;
    });

    if (widget.onHelpfulToggled != null) {
      widget.onHelpfulToggled!(widget.review);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final review = widget.review;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User info and overall rating
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User avatar
                Semantics(
                  label: 'Profile picture of ${review.userName}',
                  child: CircleAvatar(
                    radius: 24,
                    backgroundImage: review.userProfileImageUrl != null
                        ? NetworkImage(review.userProfileImageUrl!)
                        : null,
                    child: review.userProfileImageUrl == null
                        ? const Icon(
                            Icons.person,
                            size: 24,
                            color: Colors.white,
                            semanticLabel: 'Default profile icon',
                          )
                        : null,
                  ),
                ),

                const SizedBox(width: 12),

                // User info and rating
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              review.userName,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (review.isVerified)
                            const Tooltip(
                              message: 'Verified Stay',
                              child: Icon(
                                Icons.verified,
                                size: 16,
                                color: Colors.green,
                                semanticLabel: 'Verified Stay',
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          RatingDisplay(
                            rating: review.overallRating,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            timeago.format(review.datePosted),
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                      if (review.tripType != null ||
                          review.roomType != null) ...[
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            if (review.tripType != null) ...[
                              Icon(
                                Icons.people,
                                size: 14,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                review.tripType!,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                              const SizedBox(width: 8),
                            ],
                            if (review.roomType != null) ...[
                              const Icon(
                                Icons.hotel,
                                size: 14,
                                color: Colors.grey,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                review.roomType!,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Category ratings
            if (review.categoryRatings.isNotEmpty) ...[
              Wrap(
                spacing: 16,
                runSpacing: 8,
                children: review.categoryRatings.entries.map((entry) {
                  return _buildCategoryRating(
                    entry.key.displayName,
                    entry.value,
                  );
                }).toList(),
              ),
              const SizedBox(height: 12),
            ],

            // Tags
            if (review.tags.isNotEmpty) ...[
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: review.tags.map((tag) {
                  return Chip(
                    label: Text(
                      tag,
                      style: const TextStyle(
                        fontSize: 12,
                      ),
                    ),
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    visualDensity: VisualDensity.compact,
                    padding: EdgeInsets.zero,
                  );
                }).toList(),
              ),
              const SizedBox(height: 12),
            ],

            // Review content
            AnimatedSize(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: Container(
                constraints: BoxConstraints(
                  maxHeight: _isExpanded ? double.infinity : 72,
                ),
                child: Text(
                  review.content,
                  style: theme.textTheme.bodyMedium,
                  overflow:
                      _isExpanded ? TextOverflow.visible : TextOverflow.fade,
                ),
              ),
            ),

            // Show more/less button
            if (!widget.showFullContent && review.content.length > 150)
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: TextButton.icon(
                  key: ValueKey<bool>(_isExpanded),
                  onPressed: _toggleExpanded,
                  icon: Icon(
                    _isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    size: 16,
                  ),
                  label: Text(_isExpanded ? 'Show less' : 'Show more'),
                ),
              ),

            // Review photos
            if (widget.showPhotos && review.photoUrls.isNotEmpty) ...[
              const SizedBox(height: 12),
              SizedBox(
                height: 100,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: review.photoUrls.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: GestureDetector(
                        onTap: () {
                          showDialog(
                            context: context,
                            builder: (context) => Dialog(
                              child: PhotoGallery(
                                photos: review.photoUrls,
                                initialIndex: index,
                              ),
                            ),
                          );
                        },
                        child: Hero(
                          tag: 'review_photo_${review.id}_$index',
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: FadeInImage.assetNetwork(
                              placeholder:
                                  'assets/images/placeholder_image.png',
                              image: review.photoUrls[index],
                              width: 100,
                              height: 100,
                              fit: BoxFit.cover,
                              fadeInDuration: const Duration(milliseconds: 300),
                              fadeOutDuration:
                                  const Duration(milliseconds: 300),
                              imageErrorBuilder: (context, error, stackTrace) {
                                return Container(
                                  width: 100,
                                  height: 100,
                                  color:
                                      theme.colorScheme.surfaceContainerHighest,
                                  child: Center(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.image_not_supported,
                                          color: theme
                                              .colorScheme.onSurfaceVariant,
                                          size: 24,
                                          semanticLabel: 'Image failed to load',
                                        ),
                                        const SizedBox(height: 4),
                                        const Text(
                                          'Failed to load',
                                          style: TextStyle(
                                            fontSize: 10,
                                            color: Colors.grey,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],

            // Hotel response
            if (review.hotelResponse != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerLowest,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.business,
                          size: 16,
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Response from ${review.hotelResponse!.staffName}, ${review.hotelResponse!.staffTitle}',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      review.hotelResponse!.content,
                      style: theme.textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Responded ${timeago.format(review.hotelResponse!.datePosted)}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // Helpful button
            if (widget.showHelpfulButton) ...[
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: _isHelpful
                            ? theme.colorScheme.primary
                            : theme.colorScheme.outline,
                        width: 1,
                      ),
                    ),
                    child: OutlinedButton.icon(
                      onPressed: _toggleHelpful,
                      icon: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        transitionBuilder:
                            (Widget child, Animation<double> animation) {
                          return ScaleTransition(
                              scale: animation, child: child);
                        },
                        child: Icon(
                          _isHelpful ? Icons.thumb_up : Icons.thumb_up_outlined,
                          key: ValueKey<bool>(_isHelpful),
                          size: 16,
                          semanticLabel: _isHelpful
                              ? 'Marked as helpful'
                              : 'Mark as helpful',
                        ),
                      ),
                      label: Text(
                        _isHelpful
                            ? 'Helpful (${widget.review.helpfulCount + 1})'
                            : 'Helpful (${widget.review.helpfulCount})',
                      ),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: _isHelpful
                            ? theme.colorScheme.primary
                            : theme.colorScheme.onSurface,
                        side: BorderSide.none,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryRating(String category, double rating) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '$category: ',
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          rating.toStringAsFixed(1),
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
