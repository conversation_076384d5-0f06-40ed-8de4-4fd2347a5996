import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/services/ar_lazy_loading_service.dart';

/// Provider for the AR lazy loading service
final arLazyLoadingServiceProvider = Provider<ARLazyLoadingService>((ref) {
  return ARLazyLoadingService();
});

/// Provider for tracking AR features loading status
final arFeaturesLoadingProvider = FutureProvider<bool>((ref) async {
  final service = ref.watch(arLazyLoadingServiceProvider);
  return service.initializeARFeatures();
});

/// Provider for AR features loading state
final arFeaturesLoadingStateProvider =
    StateNotifierProvider<ARFeaturesLoadingNotifier, ARFeaturesLoadingState>(
        (ref) {
  final service = ref.watch(arLazyLoadingServiceProvider);
  return ARFeaturesLoadingNotifier(service);
});

/// AR features loading state
class ARFeaturesLoadingState {
  final bool isLoaded;
  final bool isLoading;
  final String? error;
  final double progress;

  ARFeaturesLoadingState({
    this.isLoaded = false,
    this.isLoading = false,
    this.error,
    this.progress = 0.0,
  });

  ARFeaturesLoadingState copyWith({
    bool? isLoaded,
    bool? isLoading,
    String? error,
    double? progress,
  }) {
    return ARFeaturesLoadingState(
      isLoaded: isLoaded ?? this.isLoaded,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      progress: progress ?? this.progress,
    );
  }
}

/// AR features loading notifier
class ARFeaturesLoadingNotifier extends StateNotifier<ARFeaturesLoadingState> {
  final ARLazyLoadingService _service;

  ARFeaturesLoadingNotifier(this._service) : super(ARFeaturesLoadingState()) {
    // Check if AR features are already loaded
    if (_service.arFeaturesLoaded) {
      state = state.copyWith(isLoaded: true, progress: 1.0);
    }
  }

  /// Load AR features
  Future<void> loadARFeatures() async {
    if (state.isLoaded) return;
    if (state.isLoading) return;

    try {
      // Update state to show loading started
      state = state.copyWith(isLoading: true, error: null, progress: 0.1);

      // Simulate progress updates
      _simulateProgressUpdates();

      // Load AR features
      final result = await _service.initializeARFeatures();

      // Update state based on loading result
      state = state.copyWith(
        isLoaded: result,
        isLoading: false,
        progress: 1.0,
      );
    } catch (e) {
      // Update state with error
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
        progress: 0.0,
      );
    }
  }

  /// Simulate progress updates
  void _simulateProgressUpdates() {
    // Simulate progress updates at 20%, 40%, 60%, and 80%
    Future.delayed(const Duration(milliseconds: 200), () {
      if (state.isLoading) {
        state = state.copyWith(progress: 0.2);
      }
    });

    Future.delayed(const Duration(milliseconds: 400), () {
      if (state.isLoading) {
        state = state.copyWith(progress: 0.4);
      }
    });

    Future.delayed(const Duration(milliseconds: 600), () {
      if (state.isLoading) {
        state = state.copyWith(progress: 0.6);
      }
    });

    Future.delayed(const Duration(milliseconds: 800), () {
      if (state.isLoading) {
        state = state.copyWith(progress: 0.8);
      }
    });
  }

  /// Retry loading AR features if it failed
  Future<void> retryLoadingARFeatures() async {
    state = state.copyWith(isLoading: false, error: null, progress: 0.0);
    await loadARFeatures();
  }
}
