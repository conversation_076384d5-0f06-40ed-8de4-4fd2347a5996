import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/instant_booking_model.dart';
import 'package:culture_connect/models/payment/payment_method_model.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/services/instant_booking_service.dart';

/// Provider for all instant bookings
final instantBookingsProvider =
    StreamProvider<List<InstantBookingModel>>((ref) {
  return ref.watch(instantBookingServiceProvider).instantBookingsStream;
});

/// Provider for a specific instant booking
final instantBookingProvider =
    Provider.family<InstantBookingModel?, String>((ref, id) {
  return ref.watch(instantBookingServiceProvider).getInstantBooking(id);
});

/// Provider for instant booking status updates
final instantBookingStatusProvider = StreamProvider<InstantBookingModel>((ref) {
  return ref.watch(instantBookingServiceProvider).instantBookingStatusStream;
});

/// Provider for instant bookings by status
final instantBookingsByStatusProvider =
    Provider.family<List<InstantBookingModel>, InstantBookingStatus>(
        (ref, status) {
  final bookings = ref.watch(instantBookingsProvider).value ?? [];
  return bookings.where((booking) => booking.status == status).toList();
});

/// Provider for successful instant bookings
final successfulInstantBookingsProvider =
    Provider<List<InstantBookingModel>>((ref) {
  final bookings = ref.watch(instantBookingsProvider).value ?? [];
  return bookings
      .where((booking) => booking.status == InstantBookingStatus.success)
      .toList();
});

/// Provider for failed instant bookings
final failedInstantBookingsProvider =
    Provider<List<InstantBookingModel>>((ref) {
  final bookings = ref.watch(instantBookingsProvider).value ?? [];
  return bookings
      .where((booking) => booking.status == InstantBookingStatus.failed)
      .toList();
});

/// Provider for processing instant bookings
final processingInstantBookingsProvider =
    Provider<List<InstantBookingModel>>((ref) {
  final bookings = ref.watch(instantBookingsProvider).value ?? [];
  return bookings
      .where((booking) => booking.status == InstantBookingStatus.processing)
      .toList();
});

/// Provider for cancelled instant bookings
final cancelledInstantBookingsProvider =
    Provider<List<InstantBookingModel>>((ref) {
  final bookings = ref.watch(instantBookingsProvider).value ?? [];
  return bookings
      .where((booking) => booking.status == InstantBookingStatus.cancelled)
      .toList();
});

/// Provider for instant bookings by type
final instantBookingsByTypeProvider =
    Provider.family<List<InstantBookingModel>, InstantBookingType>((ref, type) {
  final bookings = ref.watch(instantBookingsProvider).value ?? [];
  return bookings.where((booking) => booking.type == type).toList();
});

/// Provider for experience instant bookings
final experienceInstantBookingsProvider =
    Provider<List<InstantBookingModel>>((ref) {
  final bookings = ref.watch(instantBookingsProvider).value ?? [];
  return bookings
      .where((booking) => booking.type == InstantBookingType.experience)
      .toList();
});

/// Provider for hotel instant bookings
final hotelInstantBookingsProvider = Provider<List<InstantBookingModel>>((ref) {
  final bookings = ref.watch(instantBookingsProvider).value ?? [];
  return bookings
      .where((booking) => booking.type == InstantBookingType.hotel)
      .toList();
});

/// Provider for flight instant bookings
final flightInstantBookingsProvider =
    Provider<List<InstantBookingModel>>((ref) {
  final bookings = ref.watch(instantBookingsProvider).value ?? [];
  return bookings
      .where((booking) => booking.type == InstantBookingType.flight)
      .toList();
});

/// Provider for car rental instant bookings
final carRentalInstantBookingsProvider =
    Provider<List<InstantBookingModel>>((ref) {
  final bookings = ref.watch(instantBookingsProvider).value ?? [];
  return bookings
      .where((booking) => booking.type == InstantBookingType.carRental)
      .toList();
});

/// Provider for restaurant instant bookings
final restaurantInstantBookingsProvider =
    Provider<List<InstantBookingModel>>((ref) {
  final bookings = ref.watch(instantBookingsProvider).value ?? [];
  return bookings
      .where((booking) => booking.type == InstantBookingType.restaurant)
      .toList();
});

/// Provider for private security instant bookings
final privateSecurityInstantBookingsProvider =
    Provider<List<InstantBookingModel>>((ref) {
  final bookings = ref.watch(instantBookingsProvider).value ?? [];
  return bookings
      .where((booking) => booking.type == InstantBookingType.privateSecurity)
      .toList();
});

/// Provider for cruise instant bookings
final cruiseInstantBookingsProvider =
    Provider<List<InstantBookingModel>>((ref) {
  final bookings = ref.watch(instantBookingsProvider).value ?? [];
  return bookings
      .where((booking) => booking.type == InstantBookingType.cruise)
      .toList();
});

/// Notifier for creating and managing instant bookings
class InstantBookingNotifier
    extends StateNotifier<AsyncValue<InstantBookingModel?>> {
  final InstantBookingService _instantBookingService;

  InstantBookingNotifier(this._instantBookingService)
      : super(const AsyncValue.data(null));

  /// Create an experience instant booking
  Future<void> createExperienceInstantBooking({
    required String experienceId,
    required String experienceName,
    required String experienceImageUrl,
    required DateTime serviceDate,
    required int participantCount,
    required double totalAmount,
    required String currency,
    required PaymentMethodModel paymentMethod,
    Map<String, dynamic> additionalDetails = const {},
  }) async {
    try {
      state = const AsyncValue.loading();

      final booking =
          await _instantBookingService.createExperienceInstantBooking(
        experienceId: experienceId,
        experienceName: experienceName,
        experienceImageUrl: experienceImageUrl,
        serviceDate: serviceDate,
        participantCount: participantCount,
        totalAmount: totalAmount,
        currency: currency,
        paymentMethod: paymentMethod,
        additionalDetails: additionalDetails,
      );

      state = AsyncValue.data(booking);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Create a travel service instant booking
  Future<void> createTravelServiceInstantBooking({
    required TravelService travelService,
    required DateTime serviceDate,
    required int participantCount,
    required double totalAmount,
    required String currency,
    required PaymentMethodModel paymentMethod,
    Map<String, dynamic> additionalDetails = const {},
  }) async {
    try {
      state = const AsyncValue.loading();

      final booking =
          await _instantBookingService.createTravelServiceInstantBooking(
        travelService: travelService,
        serviceDate: serviceDate,
        participantCount: participantCount,
        totalAmount: totalAmount,
        currency: currency,
        paymentMethod: paymentMethod,
        additionalDetails: additionalDetails,
      );

      state = AsyncValue.data(booking);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Cancel an instant booking
  Future<void> cancelInstantBooking(String bookingId) async {
    try {
      state = const AsyncValue.loading();

      final booking =
          await _instantBookingService.cancelInstantBooking(bookingId);

      state = AsyncValue.data(booking);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Reset the state
  void reset() {
    state = const AsyncValue.data(null);
  }
}

/// Provider for the instant booking notifier
final instantBookingNotifierProvider = StateNotifierProvider<
    InstantBookingNotifier, AsyncValue<InstantBookingModel?>>((ref) {
  final instantBookingService = ref.watch(instantBookingServiceProvider);
  return InstantBookingNotifier(instantBookingService);
});
