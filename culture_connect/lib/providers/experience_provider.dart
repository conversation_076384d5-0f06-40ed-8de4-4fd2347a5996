import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/models/filter_options.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'dart:math' as math;

/// Provider for the current filter options
final filterOptionsProvider = StateProvider<FilterOptions>((ref) {
  return const FilterOptions();
});

/// Provider for experiences
final experiencesProvider =
    StateNotifierProvider<ExperiencesNotifier, AsyncValue<List<Experience>>>(
        (ref) {
  final loggingService = ref.watch(loggingServiceProvider);
  return ExperiencesNotifier(loggingService);
});

/// Provider for filtered experiences
final filteredExperiencesProvider = Provider<List<Experience>>((ref) {
  final experiencesAsyncValue = ref.watch(experiencesProvider);
  final filterOptions = ref.watch(filterOptionsProvider);

  return experiencesAsyncValue.when(
    data: (experiences) {
      final experiencesNotifier = ref.read(experiencesProvider.notifier);
      return experiencesNotifier.getFilteredExperiences(
          experiences, filterOptions);
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

/// Provider for saved experiences
final savedExperiencesProvider = Provider<List<Experience>>((ref) {
  final experiencesAsyncValue = ref.watch(experiencesProvider);

  return experiencesAsyncValue.when(
    data: (experiences) => experiences.where((exp) => exp.isSaved).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

/// Provider for recently viewed experiences
final recentlyViewedExperiencesProvider = Provider<List<Experience>>((ref) {
  final experiencesAsyncValue = ref.watch(experiencesProvider);

  return experiencesAsyncValue.when(
    data: (experiences) {
      final filtered =
          experiences.where((exp) => exp.lastViewed != null).toList();
      filtered.sort((a, b) => b.lastViewed!.compareTo(a.lastViewed!));
      return filtered.take(10).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

/// Provider for popular experiences
final popularExperiencesProvider = Provider<List<Experience>>((ref) {
  final experiencesAsyncValue = ref.watch(experiencesProvider);

  return experiencesAsyncValue.when(
    data: (experiences) {
      final filtered = experiences.toList();
      filtered.sort((a, b) => b.viewCount.compareTo(a.viewCount));
      return filtered.take(10).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

/// Provider for featured experiences
final featuredExperiencesProvider = Provider<List<Experience>>((ref) {
  final experiencesAsyncValue = ref.watch(experiencesProvider);

  return experiencesAsyncValue.when(
    data: (experiences) => experiences.where((exp) => exp.isFeatured).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

/// Provider for AR experiences
final arExperiencesProvider = Provider<List<Experience>>((ref) {
  final experiencesAsyncValue = ref.watch(experiencesProvider);

  return experiencesAsyncValue.when(
    data: (experiences) =>
        experiences.where((exp) => exp.hasARContent).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

class ExperiencesNotifier extends StateNotifier<AsyncValue<List<Experience>>> {
  final LoggingService _loggingService;

  ExperiencesNotifier(this._loggingService)
      : super(const AsyncValue.loading()) {
    loadExperiences();
  }

  Future<void> loadExperiences() async {
    try {
      _loggingService.debug('ExperiencesNotifier', 'Loading experiences');

      // TODO: Implement actual API call
      await Future.delayed(
          const Duration(seconds: 1)); // Simulate network delay

      // Sample data
      final experiences = [
        Experience(
          id: '1',
          title: 'Traditional Cooking Class',
          description: 'Learn authentic Nigerian cuisine from local chefs',
          imageUrl: 'https://picsum.photos/200/300',
          category: 'Cooking Classes',
          location: 'Lagos, Nigeria',
          rating: 4.5,
          reviewCount: 128,
          price: 50,
          coordinates: const LatLng(6.5244, 3.3792),
          guideId: 'guide1',
          guideName: 'Chef Ade',
          guideImageUrl: 'https://picsum.photos/100/100',
          languages: ['English', 'Yoruba'],
          includedItems: ['Ingredients', 'Recipe Book', 'Apron'],
          requirements: ['Basic cooking skills'],
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
          updatedAt: DateTime.now(),
          durationHours: 3.0,
          isAccessible: true,
          availableDates: List.generate(
            10,
            (index) => DateTime.now().add(Duration(days: index + 1)),
          ),
          maxParticipants: 8,
          currentParticipants: 3,
          isFeatured: true,
          viewCount: 450,
          isSaved: false,
        ),
        Experience(
          id: '2',
          title: 'Yoruba Cultural Tour',
          description: 'Explore the rich Yoruba culture and traditions',
          imageUrl: 'https://picsum.photos/200/301',
          category: 'Cultural Tours',
          location: 'Ibadan, Nigeria',
          rating: 4.8,
          reviewCount: 95,
          price: 75,
          coordinates: const LatLng(7.3775, 3.9470),
          guideId: 'guide2',
          guideName: 'Bola Adeyemi',
          guideImageUrl: 'https://picsum.photos/100/101',
          languages: ['English', 'Yoruba', 'French'],
          includedItems: ['Transportation', 'Lunch', 'Souvenir'],
          requirements: ['None'],
          createdAt: DateTime.now().subtract(const Duration(days: 45)),
          updatedAt: DateTime.now(),
          durationHours: 6.0,
          isAccessible: false,
          availableDates: List.generate(
            5,
            (index) => DateTime.now().add(Duration(days: index * 2 + 3)),
          ),
          maxParticipants: 12,
          currentParticipants: 8,
          isFeatured: true,
          viewCount: 320,
          isSaved: true,
          lastViewed: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        Experience(
          id: '3',
          title: 'Nairobi Wildlife Safari',
          description: 'Experience Kenya\'s amazing wildlife up close',
          imageUrl: 'https://picsum.photos/200/302',
          category: 'Nature & Wildlife',
          location: 'Nairobi, Kenya',
          rating: 4.9,
          reviewCount: 210,
          price: 120,
          coordinates: const LatLng(-1.2921, 36.8219),
          guideId: 'guide3',
          guideName: 'James Kimani',
          guideImageUrl: 'https://picsum.photos/100/102',
          languages: ['English', 'Swahili'],
          includedItems: ['Transportation', 'Meals', 'Park Fees', 'Guide'],
          requirements: ['Comfortable shoes', 'Sun protection'],
          createdAt: DateTime.now().subtract(const Duration(days: 60)),
          updatedAt: DateTime.now(),
          durationHours: 8.0,
          isAccessible: true,
          availableDates: List.generate(
            15,
            (index) => DateTime.now().add(Duration(days: index + 1)),
          ),
          maxParticipants: 6,
          currentParticipants: 4,
          isFeatured: false,
          viewCount: 580,
          isSaved: true,
          lastViewed: DateTime.now().subtract(const Duration(days: 1)),
        ),
        Experience(
          id: '4',
          title: 'Cape Town Wine Tour',
          description: 'Taste the finest wines of South Africa\'s Cape region',
          imageUrl: 'https://picsum.photos/200/303',
          category: 'Food & Drink',
          location: 'Cape Town, South Africa',
          rating: 4.7,
          reviewCount: 156,
          price: 90,
          coordinates: const LatLng(-33.9249, 18.4241),
          guideId: 'guide4',
          guideName: 'Sarah Johnson',
          guideImageUrl: 'https://picsum.photos/100/103',
          languages: ['English', 'Afrikaans'],
          includedItems: ['Transportation', 'Wine Tasting', 'Cheese Platter'],
          requirements: ['21+ years old'],
          createdAt: DateTime.now().subtract(const Duration(days: 90)),
          updatedAt: DateTime.now(),
          durationHours: 5.0,
          isAccessible: true,
          availableDates: List.generate(
            8,
            (index) => DateTime.now().add(Duration(days: index + 2)),
          ),
          maxParticipants: 10,
          currentParticipants: 7,
          isFeatured: true,
          viewCount: 420,
          isSaved: false,
        ),
        Experience(
          id: '5',
          title: 'Traditional Zulu Dance Workshop',
          description: 'Learn traditional Zulu dance moves and rhythms',
          imageUrl: 'https://picsum.photos/200/304',
          category: 'Music & Dance',
          location: 'Durban, South Africa',
          rating: 4.6,
          reviewCount: 78,
          price: 45,
          coordinates: const LatLng(-29.8587, 31.0218),
          guideId: 'guide5',
          guideName: 'Thabo Nkosi',
          guideImageUrl: 'https://picsum.photos/100/104',
          languages: ['English', 'Zulu'],
          includedItems: ['Traditional Costume Rental', 'Refreshments'],
          requirements: ['No dance experience needed'],
          createdAt: DateTime.now().subtract(const Duration(days: 120)),
          updatedAt: DateTime.now(),
          durationHours: 2.5,
          isAccessible: true,
          availableDates: List.generate(
            6,
            (index) => DateTime.now().add(Duration(days: index * 3 + 1)),
          ),
          maxParticipants: 15,
          currentParticipants: 9,
          isFeatured: false,
          viewCount: 290,
          isSaved: true,
          lastViewed: DateTime.now().subtract(const Duration(hours: 5)),
        ),
        Experience(
          id: '6',
          title: 'Maasai Village Visit',
          description:
              'Experience the traditional lifestyle of the Maasai people',
          imageUrl: 'https://picsum.photos/200/305',
          category: 'Cultural Tours',
          location: 'Maasai Mara, Kenya',
          rating: 4.9,
          reviewCount: 112,
          price: 65,
          coordinates: const LatLng(-1.5, 35.2),
          guideId: 'guide6',
          guideName: 'Lenku Ole Mpoke',
          guideImageUrl: 'https://picsum.photos/100/105',
          languages: ['English', 'Swahili', 'Maa'],
          includedItems: [
            'Transportation',
            'Traditional Lunch',
            'Crafts Workshop'
          ],
          requirements: ['Respectful attire'],
          createdAt: DateTime.now().subtract(const Duration(days: 75)),
          updatedAt: DateTime.now(),
          durationHours: 4.0,
          isAccessible: false,
          availableDates: List.generate(
            10,
            (index) => DateTime.now().add(Duration(days: index + 1)),
          ),
          maxParticipants: 8,
          currentParticipants: 5,
          isFeatured: true,
          viewCount: 380,
          isSaved: false,
          lastViewed: DateTime.now().subtract(const Duration(days: 3)),
          hasARContent: true,
          arContentType: ARContentType.model3d,
          arModelUrl: 'https://example.com/models/temple.glb',
          arMetadata: {
            'scale': 0.5,
            'rotation': 0.0,
            'description': 'Ancient temple with detailed architecture',
          },
        ),
      ];

      _loggingService.debug(
          'ExperiencesNotifier', 'Loaded ${experiences.length} experiences');
      state = AsyncValue.data(experiences);
    } catch (error, stackTrace) {
      _loggingService.error('ExperiencesNotifier', 'Error loading experiences',
          error, stackTrace);
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> refreshExperiences() async {
    _loggingService.debug('ExperiencesNotifier', 'Refreshing experiences');
    state = const AsyncValue.loading();
    await loadExperiences();
  }

  List<Experience> filterByCategory(String category) {
    return state.when(
      data: (experiences) => category == 'All'
          ? experiences
          : experiences.where((exp) => exp.category == category).toList(),
      loading: () => [],
      error: (_, __) => [],
    );
  }

  List<Experience> searchExperiences(String query) {
    return state.when(
      data: (experiences) => experiences
          .where((exp) =>
              exp.title.toLowerCase().contains(query.toLowerCase()) ||
              exp.description.toLowerCase().contains(query.toLowerCase()) ||
              exp.location.toLowerCase().contains(query.toLowerCase()))
          .toList(),
      loading: () => [],
      error: (_, __) => [],
    );
  }

  /// Get filtered and sorted experiences based on filter options
  List<Experience> getFilteredExperiences(
      List<Experience> experiences, FilterOptions filterOptions) {
    // Start with all experiences
    List<Experience> filtered = List.from(experiences);

    // Apply category filter
    if (filterOptions.category != null && filterOptions.category != 'All') {
      filtered = filtered
          .where((exp) => exp.category == filterOptions.category)
          .toList();
    }

    // Apply price range filter
    if (filterOptions.priceRange != null) {
      filtered = filtered
          .where((exp) =>
              exp.price >= filterOptions.priceRange!.start &&
              exp.price <= filterOptions.priceRange!.end)
          .toList();
    }

    // Apply rating filter
    if (filterOptions.minRating != null) {
      filtered = filtered
          .where((exp) => exp.rating >= filterOptions.minRating!)
          .toList();
    }

    // Apply duration filter
    if (filterOptions.durationRange != null) {
      filtered = filtered
          .where((exp) =>
              exp.durationHours >= filterOptions.durationRange!.start &&
              exp.durationHours <= filterOptions.durationRange!.end)
          .toList();
    }

    // Apply language filter
    if (filterOptions.languages != null &&
        filterOptions.languages!.isNotEmpty) {
      filtered = filtered
          .where((exp) => filterOptions.languages!
              .any((lang) => exp.languages.contains(lang)))
          .toList();
    }

    // Apply location filter
    if (filterOptions.locations != null &&
        filterOptions.locations!.isNotEmpty) {
      filtered = filtered
          .where((exp) =>
              filterOptions.locations!.any((loc) => exp.location.contains(loc)))
          .toList();
    }

    // Apply date range filter
    if (filterOptions.dateRange != null) {
      filtered = filtered
          .where((exp) => exp.availableDates.any((date) =>
              date.isAfter(filterOptions.dateRange!.start) &&
              date.isBefore(filterOptions.dateRange!.end)))
          .toList();
    }

    // Apply participants filter
    if (filterOptions.participants != null) {
      filtered = filtered
          .where((exp) =>
              exp.maxParticipants >= filterOptions.participants! &&
              exp.currentParticipants + filterOptions.participants! <=
                  exp.maxParticipants)
          .toList();
    }

    // Apply availability filter
    if (filterOptions.onlyAvailable == true) {
      filtered = filtered
          .where((exp) =>
              exp.currentParticipants < exp.maxParticipants &&
              exp.availableDates.any((date) => date.isAfter(DateTime.now())))
          .toList();
    }

    // Apply accessibility filter
    if (filterOptions.onlyAccessible == true) {
      filtered = filtered.where((exp) => exp.isAccessible).toList();
    }

    // Apply sorting
    switch (filterOptions.sortOption) {
      case SortOption.priceHighToLow:
        filtered.sort((a, b) => b.price.compareTo(a.price));
        break;
      case SortOption.priceLowToHigh:
        filtered.sort((a, b) => a.price.compareTo(b.price));
        break;
      case SortOption.rating:
        filtered.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case SortOption.newest:
        filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case SortOption.popular:
        filtered.sort((a, b) => b.viewCount.compareTo(a.viewCount));
        break;
      case SortOption.distance:
        // This would require user's current location
        // For now, just use a random sort as placeholder
        final random = math.Random();
        filtered.sort((a, b) => random.nextInt(3) - 1);
        break;
      case SortOption.recommended:
        // For recommended, use a weighted score based on rating, popularity, and recency
        filtered.sort((a, b) {
          final scoreA = _calculateRecommendationScore(a);
          final scoreB = _calculateRecommendationScore(b);
          return scoreB.compareTo(scoreA);
        });
        break;
    }

    return filtered;
  }

  /// Calculate a recommendation score for an experience
  double _calculateRecommendationScore(Experience experience) {
    // Weight factors
    const ratingWeight = 0.4;
    const popularityWeight = 0.3;
    const recencyWeight = 0.2;
    const featuredWeight = 0.1;

    // Normalize rating (0-5 scale)
    final ratingScore = experience.rating / 5.0;

    // Normalize popularity (assume max viewCount is 1000)
    final popularityScore = math.min(experience.viewCount / 1000, 1.0);

    // Normalize recency (newer is better, assume 180 days max)
    final ageInDays = DateTime.now().difference(experience.createdAt).inDays;
    final recencyScore = 1.0 - math.min(ageInDays / 180, 1.0);

    // Featured bonus
    final featuredScore = experience.isFeatured ? 1.0 : 0.0;

    // Calculate weighted score
    return (ratingScore * ratingWeight) +
        (popularityScore * popularityWeight) +
        (recencyScore * recencyWeight) +
        (featuredScore * featuredWeight);
  }

  /// Mark an experience as viewed
  Future<void> markAsViewed(String experienceId) async {
    state.whenData((experiences) {
      final index = experiences.indexWhere((exp) => exp.id == experienceId);
      if (index != -1) {
        final updatedExperience = experiences[index].copyWith(
          viewCount: experiences[index].viewCount + 1,
          lastViewed: DateTime.now(),
        );

        final updatedExperiences = List<Experience>.from(experiences);
        updatedExperiences[index] = updatedExperience;

        state = AsyncValue.data(updatedExperiences);
        _loggingService.debug(
            'ExperiencesNotifier', 'Marked experience $experienceId as viewed');
      }
    });
  }

  /// Toggle saved status for an experience
  Future<void> toggleSaved(String experienceId) async {
    state.whenData((experiences) {
      final index = experiences.indexWhere((exp) => exp.id == experienceId);
      if (index != -1) {
        final currentSavedStatus = experiences[index].isSaved;
        final updatedExperience = experiences[index].copyWith(
          isSaved: !currentSavedStatus,
        );

        final updatedExperiences = List<Experience>.from(experiences);
        updatedExperiences[index] = updatedExperience;

        state = AsyncValue.data(updatedExperiences);
        _loggingService.debug('ExperiencesNotifier',
            'Experience $experienceId saved status toggled to ${!currentSavedStatus}');
      }
    });
  }
}
