import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/utils/refresh_animation_utils.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';

/// Provider for the user's preferred refresh animation type
final refreshAnimationTypeProvider =
    StateNotifierProvider<RefreshAnimationTypeNotifier, RefreshAnimationType>(
  (ref) {
    final prefs = ref.watch(sharedPreferencesProvider);
    return RefreshAnimationTypeNotifier(prefs);
  },
);

/// Notifier for the user's preferred refresh animation type
class RefreshAnimationTypeNotifier extends StateNotifier<RefreshAnimationType> {
  /// The shared preferences instance
  final SharedPreferences _prefs;

  /// The key for storing the animation type in shared preferences
  static const String _prefKey = 'refresh_animation_type';

  /// Creates a new refresh animation type notifier
  RefreshAnimationTypeNotifier(this._prefs) : super(_loadAnimationType(_prefs));

  /// Load the animation type from shared preferences
  static RefreshAnimationType _loadAnimationType(SharedPreferences prefs) {
    final typeIndex = prefs.getInt(_prefKey);
    if (typeIndex != null &&
        typeIndex >= 0 &&
        typeIndex < RefreshAnimationType.values.length) {
      return RefreshAnimationType.values[typeIndex];
    }
    return RefreshAnimationType.circular; // Default
  }

  /// Set the animation type
  Future<void> setAnimationType(RefreshAnimationType type) async {
    await _prefs.setInt(_prefKey, type.index);
    state = type;
  }
}

/// Provider for whether the user has seen the refresh animation tutorial
final hasSeenRefreshTutorialProvider =
    StateNotifierProvider<HasSeenRefreshTutorialNotifier, bool>(
  (ref) {
    final prefs = ref.watch(sharedPreferencesProvider);
    return HasSeenRefreshTutorialNotifier(prefs);
  },
);

/// Notifier for whether the user has seen the refresh animation tutorial
class HasSeenRefreshTutorialNotifier extends StateNotifier<bool> {
  /// The shared preferences instance
  final SharedPreferences _prefs;

  /// The key for storing whether the user has seen the tutorial
  static const String _prefKey = 'has_seen_refresh_tutorial';

  /// Creates a new has seen refresh tutorial notifier
  HasSeenRefreshTutorialNotifier(this._prefs)
      : super(_loadHasSeenTutorial(_prefs));

  /// Load whether the user has seen the tutorial from shared preferences
  static bool _loadHasSeenTutorial(SharedPreferences prefs) {
    return prefs.getBool(_prefKey) ?? false;
  }

  /// Mark the tutorial as seen
  Future<void> markAsSeen() async {
    await _prefs.setBool(_prefKey, true);
    state = true;
  }
}
